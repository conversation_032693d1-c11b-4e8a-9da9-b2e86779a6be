using System.ComponentModel.DataAnnotations;
using System.Reflection;
using System.Text.Json;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Technoloway.Core.Common;
using Technoloway.Core.Interfaces;
using Technoloway.Core.Models;
using Technoloway.Infrastructure.Data;

namespace Technoloway.Infrastructure.Services;

public class DatabaseOperationService : IDatabaseOperationService
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<DatabaseOperationService> _logger;

    public DatabaseOperationService(ApplicationDbContext context, ILogger<DatabaseOperationService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<(int Successful, int Failed, List<string> Errors)> InsertDataAsync(
        string entityName, 
        List<Dictionary<string, object>> data, 
        Dictionary<string, string> fieldMappings)
    {
        var successful = 0;
        var failed = 0;
        var errors = new List<string>();

        try
        {
            var entityType = GetEntityType(entityName);
            if (entityType == null)
            {
                errors.Add($"Entity type '{entityName}' not found");
                return (0, data.Count, errors);
            }

            foreach (var (row, index) in data.Select((r, i) => (r, i)))
            {
                try
                {
                    var entity = CreateEntityFromData(entityType, row, fieldMappings);
                    _context.Add(entity);
                    await _context.SaveChangesAsync();
                    successful++;
                }
                catch (Exception ex)
                {
                    failed++;
                    var detailedError = GetDetailedErrorMessage(ex);
                    errors.Add($"Row {index + 1}: {detailedError}");
                    _logger.LogError(ex, "Error inserting row {RowIndex} for entity {EntityName}: {DetailedError}", index + 1, entityName, detailedError);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during insert operation for entity {EntityName}", entityName);
            errors.Add($"Insert operation failed: {ex.Message}");
            failed = data.Count;
        }

        return (successful, failed, errors);
    }

    public async Task<(int Successful, int Failed, List<string> Errors)> UpdateDataAsync(
        string entityName, 
        List<Dictionary<string, object>> data, 
        Dictionary<string, string> fieldMappings, 
        string keyField)
    {
        var successful = 0;
        var failed = 0;
        var errors = new List<string>();

        try
        {
            var entityType = GetEntityType(entityName);
            if (entityType == null)
            {
                errors.Add($"Entity type '{entityName}' not found");
                return (0, data.Count, errors);
            }

            foreach (var (row, index) in data.Select((r, i) => (r, i)))
            {
                try
                {
                    if (!row.ContainsKey(keyField))
                    {
                        failed++;
                        errors.Add($"Row {index + 1}: Key field '{keyField}' not found");
                        continue;
                    }

                    var keyValue = row[keyField];
                    var existingEntity = await FindEntityByKeyAsync(entityType, keyField, keyValue);

                    if (existingEntity == null)
                    {
                        failed++;
                        errors.Add($"Row {index + 1}: Entity with {keyField}='{keyValue}' not found");
                        continue;
                    }

                    UpdateEntityFromData(existingEntity, row, fieldMappings);
                    await _context.SaveChangesAsync();
                    successful++;
                }
                catch (Exception ex)
                {
                    failed++;
                    errors.Add($"Row {index + 1}: {ex.Message}");
                    _logger.LogError(ex, "Error updating row {RowIndex} for entity {EntityName}", index + 1, entityName);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during update operation for entity {EntityName}", entityName);
            errors.Add($"Update operation failed: {ex.Message}");
            failed = data.Count;
        }

        return (successful, failed, errors);
    }

    public async Task<(int Successful, int Failed, List<string> Errors)> UpsertDataAsync(
        string entityName, 
        List<Dictionary<string, object>> data, 
        Dictionary<string, string> fieldMappings, 
        string keyField)
    {
        var successful = 0;
        var failed = 0;
        var errors = new List<string>();

        try
        {
            var entityType = GetEntityType(entityName);
            if (entityType == null)
            {
                errors.Add($"Entity type '{entityName}' not found");
                return (0, data.Count, errors);
            }

            foreach (var (row, index) in data.Select((r, i) => (r, i)))
            {
                try
                {
                    if (!row.ContainsKey(keyField))
                    {
                        // Insert new entity
                        var newEntity = CreateEntityFromData(entityType, row, fieldMappings);
                        _context.Add(newEntity);
                    }
                    else
                    {
                        var keyValue = row[keyField];
                        var existingEntity = await FindEntityByKeyAsync(entityType, keyField, keyValue);

                        if (existingEntity == null)
                        {
                            // Insert new entity
                            var newEntity = CreateEntityFromData(entityType, row, fieldMappings);
                            _context.Add(newEntity);
                        }
                        else
                        {
                            // Update existing entity
                            UpdateEntityFromData(existingEntity, row, fieldMappings);
                        }
                    }

                    await _context.SaveChangesAsync();
                    successful++;
                }
                catch (Exception ex)
                {
                    failed++;
                    errors.Add($"Row {index + 1}: {ex.Message}");
                    _logger.LogError(ex, "Error upserting row {RowIndex} for entity {EntityName}", index + 1, entityName);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during upsert operation for entity {EntityName}", entityName);
            errors.Add($"Upsert operation failed: {ex.Message}");
            failed = data.Count;
        }

        return (successful, failed, errors);
    }

    public async Task<(int Successful, int Failed, List<string> Errors)> TruncateAndInsertAsync(
        string entityName, 
        List<Dictionary<string, object>> data, 
        Dictionary<string, string> fieldMappings)
    {
        var successful = 0;
        var failed = 0;
        var errors = new List<string>();

        try
        {
            var entityType = GetEntityType(entityName);
            if (entityType == null)
            {
                errors.Add($"Entity type '{entityName}' not found");
                return (0, data.Count, errors);
            }

            // Create backup before truncating
            var backupPath = await CreateBackupAsync(entityName);

            // Truncate existing data
            var setMethod = typeof(DbContext).GetMethod("Set", Type.EmptyTypes)?.MakeGenericMethod(entityType);
            var dbSet = setMethod?.Invoke(_context, null) as IQueryable;
            if (dbSet != null)
            {
                var entities = dbSet.Cast<object>().ToList();
                var removeRangeMethod = typeof(DbContext).GetMethod("RemoveRange", new[] { typeof(IEnumerable<object>) });
                removeRangeMethod?.Invoke(_context, new object[] { entities });
            }
            await _context.SaveChangesAsync();

            // Insert new data
            var (insertSuccessful, insertFailed, insertErrors) = await InsertDataAsync(entityName, data, fieldMappings);
            
            successful = insertSuccessful;
            failed = insertFailed;
            errors.AddRange(insertErrors);

            if (failed > 0)
            {
                // If there were failures, consider restoring from backup
                _logger.LogWarning("Truncate and insert had {Failed} failures for entity {EntityName}. Backup available at {BackupPath}", 
                    failed, entityName, backupPath);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during truncate and insert operation for entity {EntityName}", entityName);
            errors.Add($"Truncate and insert operation failed: {ex.Message}");
            failed = data.Count;
        }

        return (successful, failed, errors);
    }

    public async Task<string> CreateBackupAsync(string entityName)
    {
        try
        {
            var entityType = GetEntityType(entityName);
            if (entityType == null)
                throw new ArgumentException($"Entity type '{entityName}' not found");

            var setMethod = typeof(DbContext).GetMethod("Set", Type.EmptyTypes)?.MakeGenericMethod(entityType);
            var dbSet = setMethod?.Invoke(_context, null) as IQueryable;
            var entities = await EntityFrameworkQueryableExtensions.ToListAsync((IQueryable<object>)dbSet!);

            var backupData = entities.Select(e => EntityToDictionary(e)).ToList();
            var backupJson = JsonSerializer.Serialize(backupData, new JsonSerializerOptions { WriteIndented = true });

            var backupFileName = $"{entityName}_backup_{DateTime.UtcNow:yyyyMMdd_HHmmss}.json";
            var backupPath = Path.Combine(Path.GetTempPath(), "DataUploadBackups", backupFileName);

            Directory.CreateDirectory(Path.GetDirectoryName(backupPath)!);
            await File.WriteAllTextAsync(backupPath, backupJson);

            _logger.LogInformation("Created backup for entity {EntityName} at {BackupPath}", entityName, backupPath);
            return backupPath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating backup for entity {EntityName}", entityName);
            throw;
        }
    }

    public async Task<bool> RestoreFromBackupAsync(string entityName, string backupFilePath)
    {
        try
        {
            if (!File.Exists(backupFilePath))
            {
                _logger.LogError("Backup file not found: {BackupFilePath}", backupFilePath);
                return false;
            }

            var entityType = GetEntityType(entityName);
            if (entityType == null)
            {
                _logger.LogError("Entity type '{EntityName}' not found", entityName);
                return false;
            }

            var backupJson = await File.ReadAllTextAsync(backupFilePath);
            var backupData = JsonSerializer.Deserialize<List<Dictionary<string, object>>>(backupJson);

            if (backupData == null)
            {
                _logger.LogError("Failed to deserialize backup data from {BackupFilePath}", backupFilePath);
                return false;
            }

            // Clear existing data
            var setMethod = typeof(DbContext).GetMethod("Set", Type.EmptyTypes)?.MakeGenericMethod(entityType);
            var dbSet = setMethod?.Invoke(_context, null) as IQueryable;
            if (dbSet != null)
            {
                var entities = dbSet.Cast<object>().ToList();
                var removeRangeMethod = typeof(DbContext).GetMethod("RemoveRange", new[] { typeof(IEnumerable<object>) });
                removeRangeMethod?.Invoke(_context, new object[] { entities });
            }

            // Restore data
            foreach (var row in backupData)
            {
                var entity = CreateEntityFromDictionary(entityType, row);
                _context.Add(entity);
            }

            await _context.SaveChangesAsync();

            _logger.LogInformation("Successfully restored entity {EntityName} from backup {BackupFilePath}", entityName, backupFilePath);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error restoring from backup for entity {EntityName}", entityName);
            return false;
        }
    }

    public async Task<DatabaseEntityInfo> GetEntityMetadataAsync(string entityName)
    {
        var entityType = GetEntityType(entityName);
        if (entityType == null)
            throw new ArgumentException($"Entity type '{entityName}' not found");

        var entityInfo = new DatabaseEntityInfo
        {
            EntityName = entityName,
            DisplayName = GetDisplayName(entityType),
            Fields = GetEntityFields(entityType),
            SupportsInsert = true,
            SupportsUpdate = true,
            SupportsUpsert = true,
            SupportsTruncate = true
        };

        return await Task.FromResult(entityInfo);
    }

    public async Task<Technoloway.Core.Models.ValidationResult> ValidateDataConstraintsAsync(
        string entityName, 
        List<Dictionary<string, object>> data, 
        Dictionary<string, string> fieldMappings)
    {
        var result = new Technoloway.Core.Models.ValidationResult { IsValid = true };

        try
        {
            var entityType = GetEntityType(entityName);
            if (entityType == null)
            {
                result.IsValid = false;
                result.Errors.Add(new Technoloway.Core.Models.ValidationError
                {
                    RowNumber = 0,
                    FieldName = "Entity",
                    ErrorMessage = $"Entity type '{entityName}' not found"
                });
                return result;
            }

            var fields = GetEntityFields(entityType);

            foreach (var (row, index) in data.Select((r, i) => (r, i)))
            {
                foreach (var field in fields)
                {
                    var mappedField = fieldMappings.FirstOrDefault(fm => fm.Value == field.FieldName);
                    if (mappedField.Key == null) continue;

                    var value = row.ContainsKey(mappedField.Key) ? row[mappedField.Key] : null;

                    // Check required fields
                    if (field.IsRequired && (value == null || string.IsNullOrWhiteSpace(value.ToString())))
                    {
                        result.IsValid = false;
                        result.Errors.Add(new Technoloway.Core.Models.ValidationError
                        {
                            RowNumber = index + 1,
                            FieldName = field.FieldName,
                            ErrorMessage = $"Required field '{field.FieldName}' is missing or empty",
                            Value = value?.ToString()
                        });
                    }

                    // Check string length
                    if (field.MaxLength.HasValue && value != null)
                    {
                        var stringValue = value.ToString();
                        if (stringValue != null && stringValue.Length > field.MaxLength.Value)
                        {
                            result.IsValid = false;
                            result.Errors.Add(new Technoloway.Core.Models.ValidationError
                            {
                                RowNumber = index + 1,
                                FieldName = field.FieldName,
                                ErrorMessage = $"Value exceeds maximum length of {field.MaxLength.Value}",
                                Value = stringValue
                            });
                        }
                    }

                    // Check allowed values
                    if (field.AllowedValues != null && field.AllowedValues.Any() && value != null)
                    {
                        var stringValue = value.ToString();
                        if (!field.AllowedValues.Contains(stringValue, StringComparer.OrdinalIgnoreCase))
                        {
                            result.Warnings.Add(new Technoloway.Core.Models.ValidationWarning
                            {
                                RowNumber = index + 1,
                                FieldName = field.FieldName,
                                WarningMessage = $"Value '{stringValue}' is not in the list of allowed values: {string.Join(", ", field.AllowedValues)}",
                                Value = stringValue
                            });
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating data constraints for entity {EntityName}", entityName);
            result.IsValid = false;
            result.Errors.Add(new Technoloway.Core.Models.ValidationError
            {
                RowNumber = 0,
                FieldName = "Validation",
                ErrorMessage = $"Validation failed: {ex.Message}"
            });
        }

        return result;
    }

    private Type? GetEntityType(string entityName)
    {
        var assembly = typeof(BaseEntity).Assembly;
        return assembly.GetTypes()
            .FirstOrDefault(t => t.Name.Equals(entityName, StringComparison.OrdinalIgnoreCase) && 
                                t.IsSubclassOf(typeof(BaseEntity)));
    }

    private string GetDisplayName(Type entityType)
    {
        // Convert PascalCase to readable format
        var name = entityType.Name;
        return System.Text.RegularExpressions.Regex.Replace(name, "([a-z])([A-Z])", "$1 $2");
    }

    private string GetFieldDisplayName(string fieldName)
    {
        // Convert PascalCase field names to readable format
        // e.g., "FirstName" -> "First Name", "CategID" -> "Categ ID"
        return System.Text.RegularExpressions.Regex.Replace(fieldName, "([a-z])([A-Z])", "$1 $2");
    }

    private List<EntityFieldInfo> GetEntityFields(Type entityType)
    {
        var fields = new List<EntityFieldInfo>();

        _logger.LogInformation("=== Getting fields for entity type: {EntityType} ===", entityType.Name);

        foreach (var property in entityType.GetProperties(BindingFlags.Public | BindingFlags.Instance))
        {
            _logger.LogInformation("Examining property: {PropertyName} of type {PropertyType}",
                property.Name, property.PropertyType.Name);

            // Include all fields - even system audit fields can be mapped by users
            // Users might want to import data with specific CreatedAt dates, etc.

            // Skip collection navigation properties
            if (property.PropertyType.IsGenericType)
            {
                var genericType = property.PropertyType.GetGenericTypeDefinition();
                if (genericType == typeof(ICollection<>) ||
                    genericType == typeof(List<>) ||
                    genericType == typeof(IEnumerable<>) ||
                    genericType == typeof(HashSet<>))
                {
                    _logger.LogInformation("SKIPPED: Collection navigation property: {PropertyName}", property.Name);
                    continue;
                }
            }

            // Skip single entity navigation properties (complex types that are not value types)
            // Keep: primitives, strings, value types, nullable value types, and foreign key properties
            var isSimpleType = property.PropertyType.IsPrimitive ||
                              property.PropertyType == typeof(string) ||
                              property.PropertyType == typeof(decimal) ||
                              property.PropertyType == typeof(DateTime) ||
                              property.PropertyType.IsEnum ||
                              property.PropertyType.IsValueType;

            var isNullableValueType = property.PropertyType.IsGenericType &&
                                     property.PropertyType.GetGenericTypeDefinition() == typeof(Nullable<>);

            // Check if it's a foreign key property (int/int? ending with ID or Id)
            var isForeignKey = (property.PropertyType == typeof(int) || property.PropertyType == typeof(int?)) &&
                              (property.Name.EndsWith("ID") || property.Name.EndsWith("Id") || property.Name == "Id");

            if (!isSimpleType && !isNullableValueType && !isForeignKey)
            {
                _logger.LogInformation("SKIPPED: Complex navigation property: {PropertyName} of type {PropertyType}",
                    property.Name, property.PropertyType.Name);
                continue;
            }

            var field = new EntityFieldInfo
            {
                FieldName = property.Name,
                DisplayName = property.Name, // Use exact property name
                DataType = GetDataTypeName(property.PropertyType),
                IsRequired = IsRequired(property),
                IsIdentity = property.Name == "Id",
                IsPrimaryKey = property.Name == "Id",
                IsForeignKey = isForeignKey
            };

            // Get max length from StringLength attribute
            var stringLengthAttr = property.GetCustomAttribute<StringLengthAttribute>();
            if (stringLengthAttr != null)
            {
                field.MaxLength = stringLengthAttr.MaximumLength;
            }

            fields.Add(field);
            _logger.LogInformation("ADDED: Field {FieldName} ({DataType}) - Required: {IsRequired}, FK: {IsForeignKey}",
                field.FieldName, field.DataType, field.IsRequired, field.IsForeignKey);
        }

        _logger.LogInformation("=== SUMMARY for {EntityType}: {FieldCount} fields found ===", entityType.Name, fields.Count);
        _logger.LogInformation("All fields: {FieldNames}", string.Join(", ", fields.Select(f => f.FieldName)));

        return fields;
    }

    private bool IsRequired(PropertyInfo property)
    {
        // Check for Required attribute
        if (property.GetCustomAttribute<RequiredAttribute>() != null)
            return true;

        // Check if it's a non-nullable value type (but not nullable<T>)
        if (property.PropertyType.IsValueType &&
            Nullable.GetUnderlyingType(property.PropertyType) == null)
        {
            // Exception for Id field which is auto-generated
            if (property.Name == "Id")
                return false;

            return true;
        }

        return false;
    }

    private string GetDataTypeName(Type type)
    {
        if (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>))
        {
            type = type.GetGenericArguments()[0];
        }

        return type.Name switch
        {
            "String" => "string",
            "Int32" => "int",
            "Int64" => "long",
            "Decimal" => "decimal",
            "Double" => "double",
            "Boolean" => "bool",
            "DateTime" => "datetime",
            _ => type.Name.ToLower()
        };
    }

    private object CreateEntityFromData(Type entityType, Dictionary<string, object> data, Dictionary<string, string> fieldMappings)
    {
        var entity = Activator.CreateInstance(entityType)!;

        // Track which base entity properties were explicitly mapped
        var mappedBaseProperties = new HashSet<string>();

        foreach (var mapping in fieldMappings)
        {
            var sourceField = mapping.Key;
            var targetField = mapping.Value;

            if (!data.ContainsKey(sourceField)) continue;

            var property = entityType.GetProperty(targetField);
            if (property == null || !property.CanWrite) continue;

            var value = data[sourceField];
            var convertedValue = ConvertValue(value, property.PropertyType);

            property.SetValue(entity, convertedValue);

            // Track base entity properties that were explicitly mapped
            if (property.Name == "CreatedAt" || property.Name == "UpdatedAt" || property.Name == "IsDeleted")
            {
                mappedBaseProperties.Add(property.Name);
            }
        }

        // Set base entity properties only if they weren't explicitly mapped
        if (entity is BaseEntity baseEntity)
        {
            if (!mappedBaseProperties.Contains("CreatedAt"))
            {
                baseEntity.CreatedAt = DateTime.UtcNow;
            }
            if (!mappedBaseProperties.Contains("IsDeleted"))
            {
                baseEntity.IsDeleted = false;
            }
        }

        return entity;
    }

    private object CreateEntityFromDictionary(Type entityType, Dictionary<string, object> data)
    {
        var entity = Activator.CreateInstance(entityType)!;

        foreach (var kvp in data)
        {
            var property = entityType.GetProperty(kvp.Key);
            if (property == null || !property.CanWrite) continue;

            var convertedValue = ConvertValue(kvp.Value, property.PropertyType);
            property.SetValue(entity, convertedValue);
        }

        return entity;
    }

    private void UpdateEntityFromData(object entity, Dictionary<string, object> data, Dictionary<string, string> fieldMappings)
    {
        var entityType = entity.GetType();

        foreach (var mapping in fieldMappings)
        {
            var sourceField = mapping.Key;
            var targetField = mapping.Value;

            if (!data.ContainsKey(sourceField)) continue;

            var property = entityType.GetProperty(targetField);
            if (property == null || !property.CanWrite || property.Name == "Id") continue;

            var value = data[sourceField];
            var convertedValue = ConvertValue(value, property.PropertyType);

            property.SetValue(entity, convertedValue);
        }

        // Update base entity properties
        if (entity is BaseEntity baseEntity)
        {
            baseEntity.UpdatedAt = DateTime.UtcNow;
        }
    }

    private async Task<object?> FindEntityByKeyAsync(Type entityType, string keyField, object keyValue)
    {
        var setMethod = typeof(DbContext).GetMethod("Set", Type.EmptyTypes)?.MakeGenericMethod(entityType);
        var dbSet = setMethod?.Invoke(_context, null) as IQueryable;
        var parameter = System.Linq.Expressions.Expression.Parameter(entityType, "e");
        var property = System.Linq.Expressions.Expression.Property(parameter, keyField);
        var constant = System.Linq.Expressions.Expression.Constant(keyValue);
        var equal = System.Linq.Expressions.Expression.Equal(property, constant);
        var lambda = System.Linq.Expressions.Expression.Lambda(equal, parameter);

        var method = typeof(EntityFrameworkQueryableExtensions)
            .GetMethod("FirstOrDefaultAsync", new[] { typeof(IQueryable<>).MakeGenericType(entityType), typeof(System.Linq.Expressions.Expression<>).MakeGenericType(typeof(Func<,>).MakeGenericType(entityType, typeof(bool))), typeof(CancellationToken) });

        if (method != null)
        {
            var genericMethod = method.MakeGenericMethod(entityType);
            var task = (Task)genericMethod.Invoke(null, new object[] { dbSet, lambda, CancellationToken.None })!;
            await task;

            var resultProperty = task.GetType().GetProperty("Result");
            return resultProperty?.GetValue(task);
        }

        return null;
    }

    private object? ConvertValue(object? value, Type targetType)
    {
        if (value == null || (value is string str && string.IsNullOrWhiteSpace(str)))
        {
            // For nullable types, return null
            if (targetType.IsGenericType && targetType.GetGenericTypeDefinition() == typeof(Nullable<>))
                return null;

            // For non-nullable value types, return default value
            if (targetType.IsValueType)
                return Activator.CreateInstance(targetType);

            return null;
        }

        var originalTargetType = targetType;
        if (targetType.IsGenericType && targetType.GetGenericTypeDefinition() == typeof(Nullable<>))
        {
            targetType = targetType.GetGenericArguments()[0];
        }

        try
        {
            if (targetType == typeof(string))
                return value.ToString();

            if (targetType == typeof(int))
            {
                if (value is string stringValue && string.IsNullOrWhiteSpace(stringValue))
                    return originalTargetType.IsGenericType ? (int?)null : 0;
                return Convert.ToInt32(value);
            }

            if (targetType == typeof(long))
            {
                if (value is string stringValue && string.IsNullOrWhiteSpace(stringValue))
                    return originalTargetType.IsGenericType ? (long?)null : 0L;
                return Convert.ToInt64(value);
            }

            if (targetType == typeof(decimal))
            {
                if (value is string stringValue && string.IsNullOrWhiteSpace(stringValue))
                    return originalTargetType.IsGenericType ? (decimal?)null : 0m;
                return Convert.ToDecimal(value);
            }

            if (targetType == typeof(double))
            {
                if (value is string stringValue && string.IsNullOrWhiteSpace(stringValue))
                    return originalTargetType.IsGenericType ? (double?)null : 0.0;
                return Convert.ToDouble(value);
            }

            if (targetType == typeof(bool))
            {
                if (value is string stringValue)
                {
                    if (string.IsNullOrWhiteSpace(stringValue))
                        return originalTargetType.IsGenericType ? (bool?)null : false;

                    // Handle common boolean representations
                    var lowerValue = stringValue.ToLower().Trim();
                    if (lowerValue == "true" || lowerValue == "1" || lowerValue == "yes" || lowerValue == "y")
                        return true;
                    if (lowerValue == "false" || lowerValue == "0" || lowerValue == "no" || lowerValue == "n")
                        return false;
                }
                return Convert.ToBoolean(value);
            }

            if (targetType == typeof(DateTime))
            {
                if (value is string stringValue && string.IsNullOrWhiteSpace(stringValue))
                    return originalTargetType.IsGenericType ? (DateTime?)null : DateTime.MinValue;
                return Convert.ToDateTime(value);
            }

            return Convert.ChangeType(value, targetType);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Cannot convert value '{value}' to type '{targetType.Name}': {ex.Message}", ex);
        }
    }

    private Dictionary<string, object> EntityToDictionary(object entity)
    {
        var result = new Dictionary<string, object>();
        var properties = entity.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance);

        foreach (var property in properties)
        {
            if (property.CanRead)
            {
                var value = property.GetValue(entity);
                result[property.Name] = value ?? string.Empty;
            }
        }

        return result;
    }

    private string GetDetailedErrorMessage(Exception ex)
    {
        var message = ex.Message;

        // Check for inner exceptions
        if (ex.InnerException != null)
        {
            message += $" Inner exception: {ex.InnerException.Message}";

            // Check for database-specific errors
            if (ex.InnerException.InnerException != null)
            {
                message += $" Details: {ex.InnerException.InnerException.Message}";
            }
        }

        // Check for specific Entity Framework errors
        if (ex is Microsoft.EntityFrameworkCore.DbUpdateException dbEx)
        {
            if (dbEx.InnerException != null)
            {
                message = $"Database error: {dbEx.InnerException.Message}";
            }
        }

        return message;
    }
}
