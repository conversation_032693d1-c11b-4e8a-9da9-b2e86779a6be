using Technoloway.Core.Entities;

namespace Technoloway.Web.Areas.Admin.ViewModels
{
    public class ClientReportsViewModel
    {
        // Basic counts
        public int TotalClients { get; set; }
        public int ActiveClients { get; set; }
        public int ClientsWithProjects { get; set; }
        public int ClientsWithInvoices { get; set; }

        // Financial metrics
        public decimal TotalClientRevenue { get; set; }
        public decimal AverageRevenuePerClient { get; set; }
        public decimal TotalOutstandingAmount { get; set; }

        // Project metrics
        public int TotalProjects { get; set; }
        public decimal AverageProjectsPerClient { get; set; }

        // Chart data
        public List<CountryDistributionData> CountryDistribution { get; set; } = new List<CountryDistributionData>();
        public List<ClientRevenueData> TopClientsByRevenue { get; set; } = new List<ClientRevenueData>();
        public List<ClientProjectData> TopClientsByProjects { get; set; } = new List<ClientProjectData>();
        public List<MonthlyClientData> MonthlyClientGrowth { get; set; } = new List<MonthlyClientData>();
        public List<ClientAcquisitionData> ClientAcquisitionTrend { get; set; } = new List<ClientAcquisitionData>();

        // Recent activity
        public List<Core.Entities.Client> RecentClients { get; set; } = new List<Core.Entities.Client>();

        // Growth metrics
        public decimal MonthOverMonthGrowth { get; set; }
        public decimal YearOverYearGrowth { get; set; }

        // Calculated properties
        public decimal ClientRetentionRate => TotalClients > 0 ? (decimal)ActiveClients / TotalClients * 100 : 0;
        public decimal ProjectEngagementRate => TotalClients > 0 ? (decimal)ClientsWithProjects / TotalClients * 100 : 0;
        public decimal InvoiceEngagementRate => TotalClients > 0 ? (decimal)ClientsWithInvoices / TotalClients * 100 : 0;
    }

    public class CountryDistributionData
    {
        public string Country { get; set; } = string.Empty;
        public int ClientCount { get; set; }
        public decimal TotalRevenue { get; set; }
        public int TotalProjects { get; set; }
    }

    public class ClientProjectData
    {
        public string ClientName { get; set; } = string.Empty;
        public int ProjectCount { get; set; }
        public int CompletedProjects { get; set; }
        public decimal TotalRevenue { get; set; }
    }

    public class MonthlyClientData
    {
        public string Month { get; set; } = string.Empty;
        public int NewClients { get; set; }
        public decimal TotalRevenue { get; set; }
        public int TotalProjects { get; set; }
    }

    public class ClientAcquisitionData
    {
        public string Period { get; set; } = string.Empty;
        public int NewClients { get; set; }
        public decimal TotalValue { get; set; }
    }
}
