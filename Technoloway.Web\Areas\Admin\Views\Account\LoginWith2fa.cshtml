@model Technoloway.Web.Areas.Admin.Models.LoginWith2faViewModel

@{
    ViewData["Title"] = "Two-factor authentication";
    Layout = "_AdminLoginLayout";
}

<div class="login-container">
    <div class="login-box">
        <div class="login-logo">
            <img src="~/images/technoloway-logo.svg" alt="Technoloway Logo" class="img-fluid" style="max-height: 100px;" />
        </div>
        <div class="login-header">
            <h2>Two-Factor Authentication</h2>
            <p class="text-muted">Your login is protected with an authenticator app. Enter your authenticator code below.</p>
        </div>
        <form asp-action="LoginWith2fa" asp-route-returnurl="@ViewData["ReturnUrl"]" method="post">
            <input asp-for="RememberMe" type="hidden" />
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <div class="form-group mb-3">
                <label asp-for="TwoFactorCode" class="form-label">Authenticator Code</label>
                <input asp-for="TwoFactorCode" class="form-control" autocomplete="off" placeholder="Enter your authenticator code" />
                <span asp-validation-for="TwoFactorCode" class="text-danger"></span>
            </div>
            <div class="form-check mb-3">
                <input asp-for="RememberMachine" class="form-check-input" />
                <label asp-for="RememberMachine" class="form-check-label">Remember this machine</label>
            </div>
            <div class="form-group">
                <button type="submit" class="btn btn-primary w-100">Log in</button>
            </div>
        </form>
        <div class="login-footer mt-3">
            <p class="text-center mb-0">
                <a href="/">Back to Website</a>
            </p>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
