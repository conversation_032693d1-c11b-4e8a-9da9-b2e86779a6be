using Microsoft.EntityFrameworkCore;
using Technoloway.Core.Entities;
using Technoloway.Core.Interfaces;
using Technoloway.Infrastructure.Data;

namespace Technoloway.Infrastructure.Repositories
{
    public class HeroSectionRepository : Repository<HeroSection>, IHeroSectionRepository
    {
        public HeroSectionRepository(ApplicationDbContext context) : base(context)
        {
        }

        public async Task<HeroSection?> GetActiveWithSlidesAsync()
        {
            return await _context.HeroSections
                .Include(h => h.Slides.Where(s => s.IsActive && !s.IsDeleted).OrderBy(s => s.DisplayOrder))
                .FirstOrDefaultAsync(h => h.IsActive && !h.IsDeleted && h.PageName == "Home");
        }

        public async Task<HeroSection?> GetActiveByPageWithSlidesAsync(string pageName)
        {
            return await _context.HeroSections
                .Include(h => h.Slides.Where(s => s.IsActive && !s.<PERSON>).OrderBy(s => s.DisplayOrder))
                .FirstOrDefaultAsync(h => h.IsActive && !h.IsDeleted && h.PageName == pageName);
        }

        public async Task<HeroSection?> GetByIdWithSlidesAsync(int id)
        {
            return await _context.HeroSections
                .Include(h => h.Slides.Where(s => !s.IsDeleted).OrderBy(s => s.DisplayOrder))
                .FirstOrDefaultAsync(h => h.Id == id && !h.IsDeleted);
        }

        public async Task<List<HeroSection>> GetAllWithSlidesAsync()
        {
            return await _context.HeroSections
                .Include(h => h.Slides.Where(s => !s.IsDeleted).OrderBy(s => s.DisplayOrder))
                .Where(h => !h.IsDeleted)
                .OrderBy(h => h.PageName)
                .ThenByDescending(h => h.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<HeroSection>> GetByPageWithSlidesAsync(string pageName)
        {
            return await _context.HeroSections
                .Include(h => h.Slides.Where(s => !s.IsDeleted).OrderBy(s => s.DisplayOrder))
                .Where(h => !h.IsDeleted && h.PageName == pageName)
                .OrderByDescending(h => h.CreatedAt)
                .ToListAsync();
        }
    }
}
