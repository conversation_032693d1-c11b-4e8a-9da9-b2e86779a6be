using Technoloway.Core.Common;
using System.ComponentModel.DataAnnotations;

namespace Technoloway.Core.Entities;

public class PayrollRecord : BaseEntity
{
    [Required]
    public int TransNo { get; set; }
    
    [Required]
    public int TeamMemberID { get; set; }
    
    public DateTime? PayDate { get; set; }
    
    public int? WorkHours { get; set; }
    
    public decimal? PayRate { get; set; }
    
    public decimal? GrossPay { get; set; }
    
    public decimal? Taxes { get; set; }
    
    public decimal? Bonus { get; set; }
    
    public decimal? Deduction { get; set; }
    
    public decimal? NetPay { get; set; }
    
    [StringLength(10)]
    public string? PayMethod { get; set; }
    
    [StringLength(10)]
    public string? Status { get; set; }
    
    public string? Notes { get; set; }
    
    // Navigation properties
    public TeamMember TeamMember { get; set; } = null!;
}
