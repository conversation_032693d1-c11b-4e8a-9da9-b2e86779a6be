@model Technoloway.Core.Entities.Invoice

@{
    ViewData["Title"] = "Delete Invoice";
    Layout = "_AdminLayout";
    var client = ViewBag.Client as Technoloway.Core.Entities.Client;
    var project = ViewBag.Project as Technoloway.Core.Entities.Project;
    var itemCount = ViewBag.ItemCount as int? ?? 0;
    var paymentCount = ViewBag.PaymentCount as int? ?? 0;
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Delete Invoice</h1>
    <a asp-action="Index" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">
        <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to List
    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Confirm Deletion</h6>
    </div>
    <div class="card-body">
        <div class="alert alert-danger">
            <h5>Are you sure you want to delete this invoice?</h5>
            <p>This action will mark the invoice as deleted but will not permanently remove it from the database.</p>
            
            @if (itemCount > 0 || paymentCount > 0)
            {
                <div class="mt-3">
                    <p><strong>Warning:</strong> This invoice has associated data that will be affected:</p>
                    <ul>
                        @if (itemCount > 0)
                        {
                            <li>@itemCount invoice items</li>
                        }
                        @if (paymentCount > 0)
                        {
                            <li>@paymentCount payments</li>
                        }
                    </ul>
                </div>
            }
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="mb-4">
                    <h4>Invoice #@Model.InvoiceNumber</h4>
                    <p>
                        <strong>Issue Date:</strong> @Model.IssueDate.ToString("yyyy-MM-dd")<br />
                        <strong>Due Date:</strong> @Model.DueDate.ToString("yyyy-MM-dd")<br />
                        <strong>Status:</strong> 
                        @switch (Model.Status)
                        {
                            case "Pending":
                                <span class="badge bg-warning">Pending</span>
                                break;
                            case "Paid":
                                <span class="badge bg-success">Paid</span>
                                break;
                            case "Overdue":
                                <span class="badge bg-danger">Overdue</span>
                                break;
                            case "Cancelled":
                                <span class="badge bg-secondary">Cancelled</span>
                                break;
                            default:
                                <span class="badge bg-info">@Model.Status</span>
                                break;
                        }
                    </p>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="mb-4">
                    <p>
                        <strong>Amount:</strong> @Model.Amount.ToString("C")<br />
                        <strong>Tax:</strong> @Model.TaxAmount.ToString("C") (@Model.TaxRate%)<br />
                        <strong>Total Amount:</strong> @Model.TotalAmount.ToString("C")
                    </p>
                    
                    @if (client != null)
                    {
                        <p>
                            <strong>Client:</strong> @client.CompanyName
                        </p>
                    }
                    
                    @if (project != null)
                    {
                        <p>
                            <strong>Project:</strong> @project.Name
                        </p>
                    }
                </div>
            </div>
        </div>
        
        @if (!string.IsNullOrEmpty(Model.Notes))
        {
            <div class="mb-4">
                <strong>Notes:</strong>
                <p>@Model.Notes</p>
            </div>
        }
        
        <form asp-action="Delete" method="post">
            <input type="hidden" asp-for="Id" />
            <button type="submit" class="btn btn-danger">Delete</button>
            <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-info">View Details</a>
            <a asp-action="Index" class="btn btn-secondary">Cancel</a>
        </form>
    </div>
</div>
