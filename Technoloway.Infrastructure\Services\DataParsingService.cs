using System.Data;
using System.Globalization;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Xml;
using Microsoft.Extensions.Logging;
using OfficeOpenXml;
using Technoloway.Core.Interfaces;

namespace Technoloway.Infrastructure.Services;

public class DataParsingService : IDataParsingService
{
    private readonly ILogger<DataParsingService> _logger;

    public DataParsingService(ILogger<DataParsingService> logger)
    {
        _logger = logger;
        // Set EPPlus license context for non-commercial use
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
    }

    public async Task<(List<Dictionary<string, object>> Data, List<string> Headers)> ParseCsvAsync(Stream stream, bool hasHeaders = true, string delimiter = ",")
    {
        try
        {
            var data = new List<Dictionary<string, object>>();
            var headers = new List<string>();

            using var reader = new StreamReader(stream, Encoding.UTF8);
            string? line;
            int rowIndex = 0;

            while ((line = await reader.ReadLineAsync()) != null)
            {
                if (string.IsNullOrWhiteSpace(line)) continue;

                var values = ParseCsvLine(line, delimiter);

                if (rowIndex == 0)
                {
                    if (hasHeaders)
                    {
                        headers = values.Select(v => v?.ToString() ?? $"Column{headers.Count + 1}").ToList();
                        rowIndex++;
                        continue;
                    }
                    else
                    {
                        headers = values.Select((_, i) => $"Column{i + 1}").ToList();
                    }
                }

                var row = new Dictionary<string, object>();
                for (int i = 0; i < Math.Max(values.Count, headers.Count); i++)
                {
                    var header = i < headers.Count ? headers[i] : $"Column{i + 1}";
                    var value = i < values.Count ? values[i] : null;
                    row[header] = value ?? string.Empty;
                }

                data.Add(row);
                rowIndex++;
            }

            return (data, headers);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error parsing CSV file");
            throw new InvalidOperationException("Failed to parse CSV file", ex);
        }
    }

    public async Task<(List<Dictionary<string, object>> Data, List<string> Headers)> ParseExcelAsync(Stream stream, bool hasHeaders = true, int sheetIndex = 0)
    {
        try
        {
            var data = new List<Dictionary<string, object>>();
            var headers = new List<string>();

            using var package = new ExcelPackage(stream);
            var worksheet = package.Workbook.Worksheets[sheetIndex];

            if (worksheet == null)
                throw new InvalidOperationException($"Worksheet at index {sheetIndex} not found");

            var startRow = worksheet.Dimension?.Start.Row ?? 1;
            var endRow = worksheet.Dimension?.End.Row ?? 1;
            var startCol = worksheet.Dimension?.Start.Column ?? 1;
            var endCol = worksheet.Dimension?.End.Column ?? 1;

            // Get headers
            if (hasHeaders && startRow <= endRow)
            {
                for (int col = startCol; col <= endCol; col++)
                {
                    var headerValue = worksheet.Cells[startRow, col].Value?.ToString() ?? $"Column{col}";
                    headers.Add(headerValue);
                }
                startRow++;
            }
            else
            {
                for (int col = startCol; col <= endCol; col++)
                {
                    headers.Add($"Column{col}");
                }
            }

            // Get data rows
            for (int row = startRow; row <= endRow; row++)
            {
                var rowData = new Dictionary<string, object>();
                bool hasData = false;

                for (int col = startCol; col <= endCol; col++)
                {
                    var headerIndex = col - startCol;
                    var header = headerIndex < headers.Count ? headers[headerIndex] : $"Column{col}";
                    var cellValue = worksheet.Cells[row, col].Value;
                    
                    if (cellValue != null)
                    {
                        hasData = true;
                        rowData[header] = cellValue;
                    }
                    else
                    {
                        rowData[header] = string.Empty;
                    }
                }

                if (hasData)
                {
                    data.Add(rowData);
                }
            }

            return (data, headers);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error parsing Excel file");
            throw new InvalidOperationException("Failed to parse Excel file", ex);
        }
    }

    public async Task<(List<Dictionary<string, object>> Data, List<string> Headers)> ParseJsonAsync(Stream stream)
    {
        try
        {
            using var reader = new StreamReader(stream, Encoding.UTF8);
            var jsonContent = await reader.ReadToEndAsync();

            var jsonDocument = JsonDocument.Parse(jsonContent);
            var data = new List<Dictionary<string, object>>();
            var headers = new HashSet<string>();

            if (jsonDocument.RootElement.ValueKind == JsonValueKind.Array)
            {
                foreach (var element in jsonDocument.RootElement.EnumerateArray())
                {
                    var row = ParseJsonElement(element);
                    data.Add(row);
                    foreach (var key in row.Keys)
                    {
                        headers.Add(key);
                    }
                }
            }
            else if (jsonDocument.RootElement.ValueKind == JsonValueKind.Object)
            {
                var row = ParseJsonElement(jsonDocument.RootElement);
                data.Add(row);
                foreach (var key in row.Keys)
                {
                    headers.Add(key);
                }
            }

            return (data, headers.ToList());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error parsing JSON file");
            throw new InvalidOperationException("Failed to parse JSON file", ex);
        }
    }

    public async Task<(List<Dictionary<string, object>> Data, List<string> Headers)> ParseXmlAsync(Stream stream)
    {
        try
        {
            var data = new List<Dictionary<string, object>>();
            var headers = new HashSet<string>();

            var xmlDoc = new XmlDocument();
            xmlDoc.Load(stream);

            var rootNode = xmlDoc.DocumentElement;
            if (rootNode == null)
                throw new InvalidOperationException("Invalid XML structure");

            // Find the first repeating element as data rows
            var dataNodes = rootNode.ChildNodes.Cast<XmlNode>()
                .Where(n => n.NodeType == XmlNodeType.Element)
                .GroupBy(n => n.Name)
                .OrderByDescending(g => g.Count())
                .FirstOrDefault();

            if (dataNodes != null)
            {
                foreach (XmlNode node in dataNodes)
                {
                    var row = ParseXmlNode(node);
                    data.Add(row);
                    foreach (var key in row.Keys)
                    {
                        headers.Add(key);
                    }
                }
            }

            return (data, headers.ToList());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error parsing XML file");
            throw new InvalidOperationException("Failed to parse XML file", ex);
        }
    }

    public async Task<(List<Dictionary<string, object>> Data, List<string> Headers)> ParseSqlAsync(Stream stream, string tableName)
    {
        try
        {
            using var reader = new StreamReader(stream, Encoding.UTF8);
            var sqlContent = await reader.ReadToEndAsync();

            var data = new List<Dictionary<string, object>>();
            var headers = new List<string>();

            // Parse INSERT statements - more flexible pattern that works with any table name
            var insertPattern = @"INSERT\s+INTO\s+(?:\[?(\w+)\]?\.)?(?:\[?(\w+)\]?)\s*\(([^)]+)\)\s*VALUES\s*\(([^)]+)\)";
            var matches = Regex.Matches(sqlContent, insertPattern, RegexOptions.IgnoreCase | RegexOptions.Multiline);

            if (matches.Count == 0)
            {
                // Try alternative pattern for INSERT statements without explicit column list
                insertPattern = @"INSERT\s+INTO\s+(?:\[?(\w+)\]?\.)?(?:\[?(\w+)\]?)\s+VALUES\s*\(([^)]+)\)";
                matches = Regex.Matches(sqlContent, insertPattern, RegexOptions.IgnoreCase | RegexOptions.Multiline);
            }

            if (matches.Count > 0)
            {
                var firstMatch = matches[0];

                // Check if we have explicit column names
                if (firstMatch.Groups.Count >= 4 && !string.IsNullOrEmpty(firstMatch.Groups[3].Value))
                {
                    // Pattern with explicit columns: INSERT INTO table (col1, col2) VALUES (val1, val2)
                    var columnsPart = firstMatch.Groups[3].Value;
                    headers = columnsPart.Split(',')
                        .Select(c => c.Trim().Trim('[', ']', '`', '"', '\''))
                        .Where(c => !string.IsNullOrEmpty(c))
                        .ToList();

                    foreach (Match match in matches)
                    {
                        var valuesPart = match.Groups.Count >= 5 ? match.Groups[4].Value : match.Groups[3].Value;
                        var values = ParseSqlValues(valuesPart);

                        var row = new Dictionary<string, object>();
                        for (int i = 0; i < Math.Min(headers.Count, values.Count); i++)
                        {
                            row[headers[i]] = values[i];
                        }
                        data.Add(row);
                    }
                }
                else
                {
                    // Pattern without explicit columns: INSERT INTO table VALUES (val1, val2)
                    // Generate generic column names
                    var firstValuesPart = firstMatch.Groups[3].Value;
                    var firstValues = ParseSqlValues(firstValuesPart);
                    headers = firstValues.Select((_, i) => $"Column{i + 1}").ToList();

                    foreach (Match match in matches)
                    {
                        var valuesPart = match.Groups[3].Value;
                        var values = ParseSqlValues(valuesPart);

                        var row = new Dictionary<string, object>();
                        for (int i = 0; i < Math.Min(headers.Count, values.Count); i++)
                        {
                            row[headers[i]] = values[i];
                        }
                        data.Add(row);
                    }
                }
            }
            else
            {
                // Try to parse other SQL statements like CREATE TABLE to extract column info
                var createTablePattern = @"CREATE\s+TABLE\s+(?:\[?(\w+)\]?\.)?(?:\[?(\w+)\]?)\s*\(([^)]+)\)";
                var createMatch = Regex.Match(sqlContent, createTablePattern, RegexOptions.IgnoreCase | RegexOptions.Multiline);

                if (createMatch.Success)
                {
                    var columnDefinitions = createMatch.Groups[3].Value;
                    headers = ExtractColumnNamesFromCreateTable(columnDefinitions);
                }
            }

            return (data, headers);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error parsing SQL file");
            throw new InvalidOperationException("Failed to parse SQL file", ex);
        }
    }

    public string DetectFileType(string fileName, Stream stream)
    {
        var extension = Path.GetExtension(fileName).ToLowerInvariant();
        
        return extension switch
        {
            ".csv" => "CSV",
            ".xlsx" or ".xls" => "Excel",
            ".json" => "JSON",
            ".xml" => "XML",
            ".sql" => "SQL",
            ".tsv" or ".tab" => "TSV",
            _ => "Unknown"
        };
    }

    public async Task<(bool IsValid, List<string> Issues)> ValidateFileContentAsync(Stream stream, string fileType)
    {
        var issues = new List<string>();
        var isValid = true;

        try
        {
            // Reset stream position
            stream.Position = 0;

            // Check file size (max 100MB)
            if (stream.Length > 100 * 1024 * 1024)
            {
                issues.Add("File size exceeds 100MB limit");
                isValid = false;
            }

            // Basic content validation based on file type
            using var reader = new StreamReader(stream, Encoding.UTF8, leaveOpen: true);
            var buffer = new char[1024];
            var charsRead = await reader.ReadAsync(buffer, 0, 1024);
            var sampleContent = new string(buffer, 0, charsRead);

            // Check for potentially malicious content
            var suspiciousPatterns = new[]
            {
                @"<script[^>]*>",
                @"javascript:",
                @"vbscript:",
                @"onload\s*=",
                @"onerror\s*=",
                @"eval\s*\(",
                @"exec\s*\(",
                @"system\s*\(",
                @"cmd\s*\.",
                @"powershell",
                @"\.exe\b",
                @"\.bat\b",
                @"\.cmd\b"
            };

            foreach (var pattern in suspiciousPatterns)
            {
                if (Regex.IsMatch(sampleContent, pattern, RegexOptions.IgnoreCase))
                {
                    issues.Add($"Potentially malicious content detected: {pattern}");
                    isValid = false;
                }
            }

            // Reset stream position
            stream.Position = 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating file content");
            issues.Add("Failed to validate file content");
            isValid = false;
        }

        return (isValid, issues);
    }

    private List<string> ParseCsvLine(string line, string delimiter)
    {
        var values = new List<string>();
        var inQuotes = false;
        var currentValue = new StringBuilder();

        for (int i = 0; i < line.Length; i++)
        {
            var c = line[i];

            if (c == '"')
            {
                if (inQuotes && i + 1 < line.Length && line[i + 1] == '"')
                {
                    currentValue.Append('"');
                    i++; // Skip next quote
                }
                else
                {
                    inQuotes = !inQuotes;
                }
            }
            else if (c.ToString() == delimiter && !inQuotes)
            {
                values.Add(currentValue.ToString());
                currentValue.Clear();
            }
            else
            {
                currentValue.Append(c);
            }
        }

        values.Add(currentValue.ToString());
        return values;
    }

    private Dictionary<string, object> ParseJsonElement(JsonElement element)
    {
        var result = new Dictionary<string, object>();

        foreach (var property in element.EnumerateObject())
        {
            result[property.Name] = property.Value.ValueKind switch
            {
                JsonValueKind.String => property.Value.GetString() ?? string.Empty,
                JsonValueKind.Number => property.Value.GetDecimal(),
                JsonValueKind.True => true,
                JsonValueKind.False => false,
                JsonValueKind.Null => string.Empty,
                _ => property.Value.ToString()
            };
        }

        return result;
    }

    private Dictionary<string, object> ParseXmlNode(XmlNode node)
    {
        var result = new Dictionary<string, object>();

        // Add attributes
        if (node.Attributes != null)
        {
            foreach (XmlAttribute attr in node.Attributes)
            {
                result[$"@{attr.Name}"] = attr.Value;
            }
        }

        // Add child elements
        foreach (XmlNode child in node.ChildNodes)
        {
            if (child.NodeType == XmlNodeType.Element)
            {
                result[child.Name] = child.InnerText;
            }
        }

        // If no children, use inner text
        if (result.Count == 0)
        {
            result["Value"] = node.InnerText;
        }

        return result;
    }

    private List<string> ParseSqlValues(string valuesPart)
    {
        var values = new List<string>();
        var inQuotes = false;
        var quoteChar = '\0';
        var currentValue = new StringBuilder();

        for (int i = 0; i < valuesPart.Length; i++)
        {
            var c = valuesPart[i];

            if ((c == '\'' || c == '"') && !inQuotes)
            {
                inQuotes = true;
                quoteChar = c;
            }
            else if (c == quoteChar && inQuotes)
            {
                if (i + 1 < valuesPart.Length && valuesPart[i + 1] == quoteChar)
                {
                    currentValue.Append(quoteChar);
                    i++; // Skip next quote
                }
                else
                {
                    inQuotes = false;
                    quoteChar = '\0';
                }
            }
            else if (c == ',' && !inQuotes)
            {
                values.Add(currentValue.ToString().Trim());
                currentValue.Clear();
            }
            else
            {
                currentValue.Append(c);
            }
        }

        values.Add(currentValue.ToString().Trim());
        return values.Select(v => v == "NULL" ? string.Empty : v).ToList();
    }

    private List<string> ExtractColumnNamesFromCreateTable(string columnDefinitions)
    {
        var columns = new List<string>();

        // Split by comma, but be careful of commas inside parentheses (for data types like VARCHAR(50))
        var parts = new List<string>();
        var currentPart = new StringBuilder();
        var parenthesesLevel = 0;

        foreach (char c in columnDefinitions)
        {
            if (c == '(')
                parenthesesLevel++;
            else if (c == ')')
                parenthesesLevel--;
            else if (c == ',' && parenthesesLevel == 0)
            {
                parts.Add(currentPart.ToString().Trim());
                currentPart.Clear();
                continue;
            }

            currentPart.Append(c);
        }

        if (currentPart.Length > 0)
            parts.Add(currentPart.ToString().Trim());

        foreach (var part in parts)
        {
            // Extract column name (first word, excluding constraints)
            var words = part.Trim().Split(new[] { ' ', '\t', '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
            if (words.Length > 0)
            {
                var columnName = words[0].Trim('[', ']', '`', '"', '\'');

                // Skip common SQL keywords that aren't column names
                if (!IsReservedKeyword(columnName))
                {
                    columns.Add(columnName);
                }
            }
        }

        return columns;
    }

    private bool IsReservedKeyword(string word)
    {
        var keywords = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            "PRIMARY", "KEY", "FOREIGN", "REFERENCES", "CONSTRAINT", "INDEX",
            "UNIQUE", "NOT", "NULL", "DEFAULT", "CHECK", "AUTO_INCREMENT",
            "IDENTITY", "ON", "DELETE", "UPDATE", "CASCADE", "SET"
        };

        return keywords.Contains(word);
    }
}
