@model Technoloway.Web.Areas.Admin.Models.ChatbotDashboardViewModel
@{
    ViewData["Title"] = "Chatbot Management";
}

<div class="admin-content">
    <!-- Page Header -->
    <div class="page-header">
        <div class="header-content">
            <h1 class="page-title">
                <i class="fas fa-robot me-2"></i>
                Chatbot Management
            </h1>
            <p class="page-subtitle">Manage your AI chatbot system and conversations</p>
        </div>
        <div class="header-actions">
            <a href="@Url.Action("CreateIntent")" class="btn-modern-admin primary">
                <i class="fas fa-plus"></i>
                <span>Add Intent</span>
            </a>
        </div>
    </div>

    <!-- KPI Cards -->
    <div class="kpi-grid">
        <!-- Total Intents Card -->
        <a href="@Url.Action("Intents")" class="kpi-card-link">
            <div class="kpi-card intents-card">
                <div class="kpi-header">
                    <div class="kpi-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <div class="kpi-actions">
                        <button class="kpi-action-btn" title="View All Intents">
                            <i class="fas fa-external-link-alt"></i>
                        </button>
                    </div>
                </div>
                <div class="kpi-content">
                    <h3 class="kpi-value">@Model.TotalIntents</h3>
                    <p class="kpi-label">Total Intents</p>
                    <div class="intent-stats">
                        <div class="stat-item">
                            <i class="fas fa-check-circle"></i>
                            <span>@Model.ActiveIntents Active</span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-pause-circle"></i>
                            <span>@(Model.TotalIntents - Model.ActiveIntents) Inactive</span>
                        </div>
                    </div>
                </div>
                <div class="kpi-overlay">
                    <div class="overlay-content">
                        <i class="fas fa-arrow-right"></i>
                    </div>
                </div>
            </div>
        </a>

        <!-- Total Responses Card -->
        <a href="@Url.Action("Responses")" class="kpi-card-link">
            <div class="kpi-card responses-card">
                <div class="kpi-header">
                    <div class="kpi-icon">
                        <i class="fas fa-comments"></i>
                    </div>
                    <div class="kpi-actions">
                        <button class="kpi-action-btn" title="View All Responses">
                            <i class="fas fa-external-link-alt"></i>
                        </button>
                    </div>
                </div>
                <div class="kpi-content">
                    <h3 class="kpi-value">@Model.TotalResponses</h3>
                    <p class="kpi-label">Total Responses</p>
                    <div class="response-stats">
                        <div class="stat-item">
                            <i class="fas fa-check-circle"></i>
                            <span>@Model.ActiveResponses Active</span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-mouse-pointer"></i>
                            <span>@Model.TotalQuickActions Quick Actions</span>
                        </div>
                    </div>
                </div>
                <div class="kpi-overlay">
                    <div class="overlay-content">
                        <i class="fas fa-arrow-right"></i>
                    </div>
                </div>
            </div>
        </a>

        <!-- Total Keywords Card -->
        <a href="@Url.Action("Keywords")" class="kpi-card-link">
            <div class="kpi-card keywords-card">
                <div class="kpi-header">
                    <div class="kpi-icon">
                        <i class="fas fa-key"></i>
                    </div>
                    <div class="kpi-actions">
                        <button class="kpi-action-btn" title="View All Keywords">
                            <i class="fas fa-external-link-alt"></i>
                        </button>
                    </div>
                </div>
                <div class="kpi-content">
                    <h3 class="kpi-value">@Model.TotalKeywords</h3>
                    <p class="kpi-label">Total Keywords</p>
                    <div class="keyword-stats">
                        <div class="stat-item">
                            <i class="fas fa-check-circle"></i>
                            <span>@Model.ActiveKeywords Active</span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-weight-hanging"></i>
                            <span>@(Model.Keywords.Any() ? Model.Keywords.Average(k => k.Weight).ToString("F1") : "0") Avg Weight</span>
                        </div>
                    </div>
                </div>
                <div class="kpi-overlay">
                    <div class="overlay-content">
                        <i class="fas fa-arrow-right"></i>
                    </div>
                </div>
            </div>
        </a>

        <!-- System Status Card -->
        <div class="kpi-card system-card">
            <div class="kpi-header">
                <div class="kpi-icon">
                    <i class="fas fa-cogs"></i>
                </div>
                <div class="kpi-badge @(Model.ActiveIntents > 0 ? "success" : "warning")">
                    <span>@(Model.ActiveIntents > 0 ? "Online" : "Offline")</span>
                </div>
            </div>
            <div class="kpi-content">
                <h3 class="kpi-value">@(Model.ActiveIntents > 0 ? "Active" : "Inactive")</h3>
                <p class="kpi-label">Chatbot Status</p>
                <div class="system-stats">
                    <div class="stat-item">
                        <i class="fas fa-database"></i>
                        <span>@(Model.TotalIntents + Model.TotalResponses + Model.TotalKeywords) Total Items</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-clock"></i>
                        <span>Last updated: @(Model.Intents.Any() ? Model.Intents.Max(i => i.UpdatedAt ?? i.CreatedAt).ToString("MMM dd") : "Never")</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions Grid -->
    <div class="analytics-grid">
        <!-- Quick Actions Card -->
        <div class="analytics-card quick-actions-card">
            <div class="card-header">
                <div class="header-content">
                    <h3 class="card-title">
                        <i class="fas fa-bolt me-2"></i>
                        Quick Actions
                    </h3>
                    <p class="card-subtitle">Manage your chatbot components</p>
                </div>
            </div>
            <div class="card-body">
                <div class="quick-actions-grid">
                    <a href="@Url.Action("CreateIntent")" class="quick-action-item">
                        <div class="action-icon">
                            <i class="fas fa-brain"></i>
                        </div>
                        <div class="action-content">
                            <h4 class="action-title">Create Intent</h4>
                            <p class="action-description">Add new conversation intent</p>
                        </div>
                    </a>
                    <a href="@Url.Action("CreateResponse")" class="quick-action-item">
                        <div class="action-icon">
                            <i class="fas fa-comments"></i>
                        </div>
                        <div class="action-content">
                            <h4 class="action-title">Create Response</h4>
                            <p class="action-description">Add new chatbot response</p>
                        </div>
                    </a>
                    <a href="@Url.Action("CreateKeyword")" class="quick-action-item">
                        <div class="action-icon">
                            <i class="fas fa-key"></i>
                        </div>
                        <div class="action-content">
                            <h4 class="action-title">Create Keyword</h4>
                            <p class="action-description">Add new trigger keyword</p>
                        </div>
                    </a>
                    <a href="@Url.Action("Intents")" class="quick-action-item">
                        <div class="action-icon">
                            <i class="fas fa-list"></i>
                        </div>
                        <div class="action-content">
                            <h4 class="action-title">View All Intents</h4>
                            <p class="action-description">Manage conversation intents</p>
                        </div>
                    </a>
                </div>
            </div>
        </div>

        <!-- Recent Activity Card -->
        <div class="analytics-card recent-activity-card">
            <div class="card-header">
                <div class="header-content">
                    <h3 class="card-title">
                        <i class="fas fa-history me-2"></i>
                        Recent Activity
                    </h3>
                    <p class="card-subtitle">Latest chatbot updates</p>
                </div>
            </div>
            <div class="card-body">
                <div class="activity-list">
                    @if (Model.RecentIntents.Any())
                    {
                        @foreach (var intent in Model.RecentIntents.Take(3))
                        {
                            <div class="activity-item">
                                <div class="activity-icon">
                                    <i class="fas fa-brain"></i>
                                </div>
                                <div class="activity-content">
                                    <h5 class="activity-title">@intent.DisplayName</h5>
                                    <p class="activity-description">Intent created</p>
                                    <span class="activity-time">@intent.CreatedAt.ToString("MMM dd, yyyy")</span>
                                </div>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="empty-activity">
                            <i class="fas fa-inbox"></i>
                            <p>No recent activity</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- Data Tables -->
    <div class="data-tables-grid">
        <!-- Recent Intents Table -->
        <div class="data-table-card">
            <div class="table-header">
                <div class="header-content">
                    <h3 class="table-title">
                        <i class="fas fa-brain me-2"></i>
                        Recent Intents
                    </h3>
                    <p class="table-subtitle">Latest conversation intents</p>
                </div>
                <div class="header-actions">
                    <a asp-action="Intents" class="view-all-btn">
                        <span>View All</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
            <div class="table-container">
                @if (Model.RecentIntents.Any())
                {
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Intent</th>
                                <th>Keywords</th>
                                <th>Responses</th>
                                <th>Status</th>
                                <th>Created</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var intent in Model.RecentIntents)
                            {
                                <tr>
                                    <td>
                                        <div class="table-cell-content">
                                            <strong>@intent.DisplayName</strong>
                                            <small class="text-muted d-block">@intent.Name</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge badge-info">@intent.Keywords.Count</span>
                                    </td>
                                    <td>
                                        <span class="badge badge-success">@intent.Responses.Count</span>
                                    </td>
                                    <td>
                                        @if (intent.IsActive)
                                        {
                                            <span class="status-badge active">Active</span>
                                        }
                                        else
                                        {
                                            <span class="status-badge inactive">Inactive</span>
                                        }
                                    </td>
                                    <td>
                                        <span class="date-text">@intent.CreatedAt.ToString("MMM dd, yyyy")</span>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                }
                else
                {
                    <div class="empty-table">
                        <i class="fas fa-brain"></i>
                        <p>No intents found</p>
                        <a href="@Url.Action("CreateIntent")" class="btn-modern-admin primary">Create First Intent</a>
                    </div>
                }
            </div>
        </div>
    </div>
</div>
