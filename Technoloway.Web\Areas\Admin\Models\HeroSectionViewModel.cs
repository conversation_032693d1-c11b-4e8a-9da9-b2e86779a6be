using System.ComponentModel.DataAnnotations;

namespace Technoloway.Web.Areas.Admin.Models
{
    public class HeroSectionViewModel
    {
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        [Display(Name = "Title")]
        public string Title { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        [Display(Name = "Page Name")]
        public string PageName { get; set; } = "Home";

        [StringLength(200)]
        [Display(Name = "Meta Description")]
        public string? MetaDescription { get; set; }

        [StringLength(500)]
        [Display(Name = "Meta Keywords")]
        public string? MetaKeywords { get; set; }

        [Required]
        [StringLength(200)]
        [Display(Name = "Main Title")]
        public string MainTitle { get; set; } = string.Empty;

        [Required]
        [StringLength(500)]
        [Display(Name = "Main Subtitle")]
        public string MainSubtitle { get; set; } = string.Empty;

        [StringLength(1000)]
        [Display(Name = "Main Description")]
        public string? MainDescription { get; set; }

        [StringLength(50)]
        [Display(Name = "Primary Button Text")]
        public string? PrimaryButtonText { get; set; }

        [StringLength(200)]
        [Display(Name = "Primary Button URL")]
        public string? PrimaryButtonUrl { get; set; }

        [StringLength(50)]
        [Display(Name = "Secondary Button Text")]
        public string? SecondaryButtonText { get; set; }

        [StringLength(200)]
        [Display(Name = "Secondary Button URL")]
        public string? SecondaryButtonUrl { get; set; }

        [Display(Name = "Enable Slideshow")]
        public bool EnableSlideshow { get; set; } = true;

        [Range(1000, 30000)]
        [Display(Name = "Slideshow Speed (ms)")]
        public int SlideshowSpeed { get; set; } = 5000;

        [Display(Name = "Auto Play")]
        public bool AutoPlay { get; set; } = true;

        [Display(Name = "Show Dots")]
        public bool ShowDots { get; set; } = true;

        [Display(Name = "Show Arrows")]
        public bool ShowArrows { get; set; } = true;

        [Display(Name = "Enable Floating Elements")]
        public bool EnableFloatingElements { get; set; } = true;

        [Display(Name = "Floating Elements Config (JSON)")]
        public string? FloatingElementsConfig { get; set; }

        [Display(Name = "Is Active")]
        public bool IsActive { get; set; } = true;

        public List<HeroSlideViewModel> Slides { get; set; } = new List<HeroSlideViewModel>();
    }

    public class HeroSlideViewModel
    {
        public int Id { get; set; }

        [Required]
        [Display(Name = "Slide Content")]
        public string Content { get; set; } = string.Empty; // Combined Title, Subtitle, and Description with rich text

        [Required]
        [StringLength(10)]
        [Display(Name = "Media Type")]
        public string MediaType { get; set; } = "image";

        [StringLength(500)]
        [Display(Name = "Image URL")]
        public string? ImageUrl { get; set; }

        [StringLength(500)]
        [Display(Name = "Video URL")]
        public string? VideoUrl { get; set; }

        [StringLength(200)]
        [Display(Name = "Media Alt Text")]
        public string? MediaAlt { get; set; }

        [Display(Name = "Video Auto Play")]
        public bool VideoAutoPlay { get; set; } = true;

        [Display(Name = "Video Muted")]
        public bool VideoMuted { get; set; } = true;

        [Display(Name = "Video Loop")]
        public bool VideoLoop { get; set; } = true;

        [Display(Name = "Video Controls")]
        public bool VideoControls { get; set; } = false;

        [StringLength(50)]
        [Display(Name = "Button Text")]
        public string? ButtonText { get; set; }

        [StringLength(200)]
        [Display(Name = "Button URL")]
        public string? ButtonUrl { get; set; }

        [Display(Name = "Display Order")]
        public int DisplayOrder { get; set; }

        [Display(Name = "Is Active")]
        public bool IsActive { get; set; } = true;

        [StringLength(50)]
        [Display(Name = "Animation Type")]
        public string? AnimationType { get; set; } = "fade";

        [Range(1000, 30000)]
        [Display(Name = "Duration (ms)")]
        public int Duration { get; set; } = 5000;
    }
}
