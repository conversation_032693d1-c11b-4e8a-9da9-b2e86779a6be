using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;
using Technoloway.Core.Entities;
using Technoloway.Core.Interfaces;
using Technoloway.Web.Areas.Admin.ViewModels;

namespace Technoloway.Web.Areas.Admin.Controllers;

[Area("Admin")]
[Authorize(Policy = "RequireAdminRole")]
[Route("Admin/[controller]/[action]")]
public class JobsController : Controller
{
    private readonly IRepository<JobListing> _jobListingRepository;
    private readonly IRepository<JobApplication> _jobApplicationRepository;

    public JobsController(
        IRepository<JobListing> jobListingRepository,
        IRepository<JobApplication> jobApplicationRepository)
    {
        _jobListingRepository = jobListingRepository;
        _jobApplicationRepository = jobApplicationRepository;
    }

    public async Task<IActionResult> Index()
    {
        var jobListings = await _jobListingRepository.GetAll()
            .OrderByDescending(j => j.CreatedAt)
            .ToListAsync();

        // Calculate application statistics
        var allApplications = await _jobApplicationRepository.GetAll().ToListAsync();

        var totalApplications = allApplications.Count;
        var pendingApplications = allApplications.Count(a => a.Status == "Pending");
        var reviewedApplications = allApplications.Count(a => a.Status == "Reviewed");
        var hiredApplications = allApplications.Count(a => a.Status == "Hired");
        var rejectedApplications = allApplications.Count(a => a.Status == "Rejected");

        // Calculate application counts per job
        var jobApplicationCounts = allApplications
            .GroupBy(a => a.JobListingId)
            .ToDictionary(g => g.Key, g => g.Count());

        // Ensure all jobs have an entry (even if 0 applications)
        foreach (var job in jobListings)
        {
            if (!jobApplicationCounts.ContainsKey(job.Id))
            {
                jobApplicationCounts[job.Id] = 0;
            }
        }

        var viewModel = new JobStatsViewModel
        {
            JobListings = jobListings,
            JobApplicationCounts = jobApplicationCounts,
            TotalApplications = totalApplications,
            PendingApplications = pendingApplications,
            ReviewedApplications = reviewedApplications,
            HiredApplications = hiredApplications,
            RejectedApplications = rejectedApplications
        };

        return View(viewModel);
    }

    public IActionResult Create()
    {
        return View();
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(JobListing jobListing)
    {
        if (ModelState.IsValid)
        {
            jobListing.CreatedAt = DateTime.UtcNow;
            jobListing.UpdatedAt = DateTime.UtcNow;

            await _jobListingRepository.AddAsync(jobListing);
            return RedirectToAction(nameof(Index));
        }
        return View(jobListing);
    }

    public async Task<IActionResult> Edit(int id)
    {
        var jobListing = await _jobListingRepository.GetByIdAsync(id);
        if (jobListing == null)
        {
            return NotFound();
        }
        return View(jobListing);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Edit(int id, JobListing jobListing)
    {
        if (id != jobListing.Id)
        {
            return NotFound();
        }

        if (ModelState.IsValid)
        {
            try
            {
                jobListing.UpdatedAt = DateTime.UtcNow;
                await _jobListingRepository.UpdateAsync(jobListing);
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!await JobListingExists(jobListing.Id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }
            return RedirectToAction(nameof(Index));
        }
        return View(jobListing);
    }

    public async Task<IActionResult> Details(int id)
    {
        var jobListing = await _jobListingRepository.GetByIdAsync(id);
        if (jobListing == null)
        {
            return NotFound();
        }

        return View(jobListing);
    }

    public async Task<IActionResult> Delete(int id)
    {
        var jobListing = await _jobListingRepository.GetByIdAsync(id);
        if (jobListing == null)
        {
            return NotFound();
        }

        return View(jobListing);
    }

    [HttpPost, ActionName("Delete")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DeleteConfirmed(int id)
    {
        var jobListing = await _jobListingRepository.GetByIdAsync(id);
        if (jobListing == null)
        {
            return NotFound();
        }

        // Soft delete
        jobListing.IsDeleted = true;
        jobListing.UpdatedAt = DateTime.UtcNow;
        await _jobListingRepository.UpdateAsync(jobListing);

        return RedirectToAction(nameof(Index));
    }

    public async Task<IActionResult> Applications(int id)
    {
        var jobListing = await _jobListingRepository.GetByIdAsync(id);
        if (jobListing == null)
        {
            return NotFound();
        }

        var applications = await _jobApplicationRepository.ListAsync(a => a.JobListingId == id);
        ViewBag.JobListing = jobListing;

        return View(applications);
    }

    public async Task<IActionResult> ApplicationDetails(int id)
    {
        var application = await _jobApplicationRepository.GetByIdAsync(id);
        if (application == null)
        {
            return NotFound();
        }

        var jobListing = await _jobListingRepository.GetByIdAsync(application.JobListingId);
        ViewBag.JobListing = jobListing;

        return View(application);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> UpdateApplicationStatus(int id, string status)
    {
        var application = await _jobApplicationRepository.GetByIdAsync(id);
        if (application == null)
        {
            return NotFound();
        }

        application.Status = status;
        application.UpdatedAt = DateTime.UtcNow;
        await _jobApplicationRepository.UpdateAsync(application);

        return RedirectToAction(nameof(ApplicationDetails), new { id });
    }

    private async Task<bool> JobListingExists(int id)
    {
        var jobListing = await _jobListingRepository.GetByIdAsync(id);
        return jobListing != null;
    }
}
