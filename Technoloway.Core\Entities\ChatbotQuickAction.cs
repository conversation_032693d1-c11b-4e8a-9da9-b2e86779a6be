using System.ComponentModel.DataAnnotations;
using Technoloway.Core.Common;

namespace Technoloway.Core.Entities;

public class ChatbotQuickAction : BaseEntity
{
    [Required(ErrorMessage = "Text is required")]
    [StringLength(100, ErrorMessage = "Text cannot exceed 100 characters")]
    public string Text { get; set; } = string.Empty;

    [Required(ErrorMessage = "ActionValue is required")]
    [StringLength(500, ErrorMessage = "ActionValue cannot exceed 500 characters")]
    public string ActionValue { get; set; } = string.Empty;

    [StringLength(50, ErrorMessage = "Icon class cannot exceed 50 characters")]
    public string IconClass { get; set; } = string.Empty;

    [StringLength(20, ErrorMessage = "Action type cannot exceed 20 characters")]
    public string ActionType { get; set; } = "message"; // message, url, function

    [StringLength(500, ErrorMessage = "URL cannot exceed 500 characters")]
    public string Url { get; set; } = string.Empty;

    public bool IsActive { get; set; } = true;

    [Range(0, int.MaxValue, ErrorMessage = "Display order must be a positive number")]
    public int DisplayOrder { get; set; }

    [StringLength(100, ErrorMessage = "CSS class cannot exceed 100 characters")]
    public string CssClass { get; set; } = string.Empty;

    // Navigation properties
    public int? ChatbotResponseId { get; set; }
    public virtual ChatbotResponse? ChatbotResponse { get; set; }

    public int? ChatbotIntentId { get; set; }
    public virtual ChatbotIntent? ChatbotIntent { get; set; }
}
