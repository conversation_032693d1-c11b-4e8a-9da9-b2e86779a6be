@model Technoloway.Core.Entities.ProjectDocument

@{
    ViewData["Title"] = "Document Preview";
    string fileExtension = System.IO.Path.GetExtension(Model.FileName).TrimStart('.').ToLower();
    bool isImage = ViewBag.IsImage as bool? ?? false;
    bool isPdf = ViewBag.IsPdf as bool? ?? false;
    bool isText = ViewBag.IsText as bool? ?? false;
    string textContent = ViewBag.TextContent as string;
}

<div class="container-fluid p-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="text-gradient mb-2">
                        <i class="fas fa-search me-2"></i>Document Preview
                    </h1>
                    <p class="text-muted">@Model.FileName</p>
                </div>
                <div class="d-flex gap-2">
                    <a asp-action="Download" asp-route-id="@Model.Id" class="btn btn-success">
                        <i class="fas fa-download me-1"></i> Download
                    </a>
                    <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-outline-info">
                        <i class="fas fa-info-circle me-1"></i> Details
                    </a>
                    <a asp-action="Index" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i> Back
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Document Info Bar -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body py-2">
                    <div class="row align-items-center">
                        <div class="col-md-3">
                            <small class="text-muted">File:</small>
                            <span class="fw-bold ms-1">@Model.FileName</span>
                        </div>
                        <div class="col-md-2">
                            <small class="text-muted">Size:</small>
                            <span class="ms-1">@(Model.FileSize / 1024) KB</span>
                        </div>
                        <div class="col-md-3">
                            <small class="text-muted">Project:</small>
                            <span class="ms-1">@(Model.Project?.Name ?? "Unknown")</span>
                        </div>
                        <div class="col-md-2">
                            <small class="text-muted">Type:</small>
                            <span class="badge bg-primary ms-1">@Model.FileType.ToUpper()</span>
                        </div>
                        <div class="col-md-2">
                            <small class="text-muted">Uploaded:</small>
                            <span class="ms-1">@Model.CreatedAt.ToString("MMM dd, yyyy")</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Preview Content -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body p-0">
                    @if (isImage)
                    {
                        <!-- Image Preview -->
                        <div class="text-center p-4">
                            <img src="@Model.FileUrl" alt="@Model.FileName" class="img-fluid rounded shadow" style="max-width: 100%; max-height: 80vh;">
                        </div>
                    }
                    else if (isPdf)
                    {
                        <!-- PDF Preview -->
                        <div class="pdf-preview-container">
                            <iframe src="@Model.FileUrl" width="100%" height="800px" class="border-0">
                                <p>Your browser does not support PDFs.
                                   <a href="@Model.FileUrl" target="_blank">Download the PDF</a> to view it.
                                </p>
                            </iframe>
                        </div>
                    }
                    else if (isText && !string.IsNullOrEmpty(textContent))
                    {
                        <!-- Text File Preview -->
                        <div class="p-4">
                            <div class="bg-light rounded p-3" style="max-height: 600px; overflow-y: auto;">
                                <pre class="mb-0" style="white-space: pre-wrap; font-family: 'Courier New', monospace; font-size: 14px;">@textContent</pre>
                            </div>
                        </div>
                    }
                    else
                    {
                        <!-- Unsupported File Type -->
                        <div class="text-center py-5">
                            <div class="mb-4">
                                <i class="fas fa-file fa-4x text-muted"></i>
                            </div>
                            <h5 class="text-muted">Preview Not Available</h5>
                            <p class="text-muted">This file type (@Model.FileType.ToUpper()) does not support preview.</p>
                            <div class="mt-4">
                                <a asp-action="Download" asp-route-id="@Model.Id" class="btn btn-success me-2">
                                    <i class="fas fa-download me-1"></i> Download File
                                </a>
                                <a href="@Model.FileUrl" target="_blank" class="btn btn-outline-primary">
                                    <i class="fas fa-external-link-alt me-1"></i> Open in New Tab
                                </a>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    @if (isImage || isPdf || isText)
    {
        <!-- Preview Controls -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-0">Preview Controls</h6>
                                <small class="text-muted">Use these options to interact with the document</small>
                            </div>
                            <div class="btn-group" role="group">
                                @if (isImage)
                                {
                                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="zoomIn()">
                                        <i class="fas fa-search-plus"></i> Zoom In
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="zoomOut()">
                                        <i class="fas fa-search-minus"></i> Zoom Out
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="resetZoom()">
                                        <i class="fas fa-expand-arrows-alt"></i> Reset
                                    </button>
                                }
                                @if (isPdf)
                                {
                                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="toggleFullscreen()">
                                        <i class="fas fa-expand"></i> Fullscreen
                                    </button>
                                }
                                <a href="@Model.FileUrl" target="_blank" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-external-link-alt"></i> Open in New Tab
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

<style>
.text-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.pdf-preview-container {
    position: relative;
    width: 100%;
    height: 800px;
}

.pdf-preview-container iframe {
    border: none;
    border-radius: 0.375rem;
}

.image-preview {
    transition: transform 0.3s ease;
    cursor: zoom-in;
}

.image-preview.zoomed {
    cursor: zoom-out;
}

.card {
    transition: all 0.2s ease-in-out;
}
</style>

<script>
let currentZoom = 1;
const zoomStep = 0.2;
const maxZoom = 3;
const minZoom = 0.5;

function zoomIn() {
    const img = document.querySelector('.img-fluid');
    if (img && currentZoom < maxZoom) {
        currentZoom += zoomStep;
        img.style.transform = `scale(${currentZoom})`;
        img.style.transition = 'transform 0.3s ease';
    }
}

function zoomOut() {
    const img = document.querySelector('.img-fluid');
    if (img && currentZoom > minZoom) {
        currentZoom -= zoomStep;
        img.style.transform = `scale(${currentZoom})`;
        img.style.transition = 'transform 0.3s ease';
    }
}

function resetZoom() {
    const img = document.querySelector('.img-fluid');
    if (img) {
        currentZoom = 1;
        img.style.transform = 'scale(1)';
        img.style.transition = 'transform 0.3s ease';
    }
}

function toggleFullscreen() {
    const iframe = document.querySelector('iframe');
    if (iframe) {
        if (iframe.requestFullscreen) {
            iframe.requestFullscreen();
        } else if (iframe.webkitRequestFullscreen) {
            iframe.webkitRequestFullscreen();
        } else if (iframe.msRequestFullscreen) {
            iframe.msRequestFullscreen();
        }
    }
}

// Add click to zoom functionality for images
document.addEventListener('DOMContentLoaded', function() {
    const img = document.querySelector('.img-fluid');
    if (img) {
        img.addEventListener('click', function() {
            if (currentZoom === 1) {
                zoomIn();
            } else {
                resetZoom();
            }
        });
    }
});
</script>
