@model Technoloway.Web.Areas.Admin.Models.ChatbotIntentViewModel
@{
    ViewData["Title"] = "Create Chatbot Intent";
}

<div class="chatbot-admin">
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-robot mr-2"></i>
                        Create New Chatbot Intent
                    </h3>
                    <div class="card-tools">
                        <a href="@Url.Action("Intents")" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left mr-1"></i>
                            Back to Intents
                        </a>
                    </div>
                </div>
                <form asp-action="CreateIntent" method="post">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Name" class="form-label"></label>
                                    <input asp-for="Name" class="form-control" placeholder="e.g., services, portfolio, contact" />
                                    <span asp-validation-for="Name" class="text-danger"></span>
                                    <small class="form-text text-muted">Unique identifier for the intent (lowercase, no spaces)</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="DisplayName" class="form-label"></label>
                                    <input asp-for="DisplayName" class="form-control" placeholder="e.g., Our Services, Portfolio, Contact Us" />
                                    <span asp-validation-for="DisplayName" class="text-danger"></span>
                                    <small class="form-text text-muted">Human-readable name for the intent</small>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label asp-for="Description" class="form-label"></label>
                            <textarea asp-for="Description" class="form-control" rows="3" placeholder="Describe what this intent handles..."></textarea>
                            <span asp-validation-for="Description" class="text-danger"></span>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label asp-for="IconClass" class="form-label"></label>
                                    <input asp-for="IconClass" class="form-control" placeholder="fas fa-code" />
                                    <span asp-validation-for="IconClass" class="text-danger"></span>
                                    <small class="form-text text-muted">FontAwesome icon class</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label asp-for="DisplayOrder" class="form-label"></label>
                                    <input asp-for="DisplayOrder" class="form-control" type="number" min="0" />
                                    <span asp-validation-for="DisplayOrder" class="text-danger"></span>
                                    <small class="form-text text-muted">Lower numbers appear first</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <div class="custom-control custom-switch mt-4">
                                        <input asp-for="IsActive" class="custom-control-input" type="checkbox" />
                                        <label asp-for="IsActive" class="custom-control-label"></label>
                                    </div>
                                    <small class="form-text text-muted">Only active intents are used by the chatbot</small>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle mr-1"></i> Next Steps</h6>
                            <p class="mb-0">After creating this intent, you'll need to:</p>
                            <ul class="mb-0 mt-2">
                                <li>Add keywords that trigger this intent</li>
                                <li>Create responses for this intent</li>
                                <li>Configure quick actions (optional)</li>
                            </ul>
                        </div>
                    </div>
                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save mr-1"></i>
                            Create Intent
                        </button>
                        <a href="@Url.Action("Intents")" class="btn btn-secondary">
                            <i class="fas fa-times mr-1"></i>
                            Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
