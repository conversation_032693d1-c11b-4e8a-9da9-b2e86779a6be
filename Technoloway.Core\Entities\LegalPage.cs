using System.ComponentModel.DataAnnotations;
using Technoloway.Core.Common;

namespace Technoloway.Core.Entities
{
    public class LegalPage : BaseEntity
    {
        [Required]
        [StringLength(100)]
        public string Title { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string Slug { get; set; } = string.Empty;

        [StringLength(200)]
        public string? MetaDescription { get; set; }

        [Required]
        public string Content { get; set; } = string.Empty;

        public bool IsActive { get; set; } = true;

        public DateTime LastModified { get; set; } = DateTime.UtcNow;

        [StringLength(100)]
        public string? ModifiedBy { get; set; }

        // Navigation properties for sections
        public virtual ICollection<LegalPageSection> Sections { get; set; } = new List<LegalPageSection>();
    }

    public class LegalPageSection : BaseEntity
    {
        [Required]
        public int LegalPageId { get; set; }

        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;

        [Required]
        public string Content { get; set; } = string.Empty;

        [StringLength(50)]
        public string? IconClass { get; set; }

        public int DisplayOrder { get; set; }

        public bool IsActive { get; set; } = true;

        // Navigation property
        public virtual LegalPage LegalPage { get; set; } = null!;
    }
}
