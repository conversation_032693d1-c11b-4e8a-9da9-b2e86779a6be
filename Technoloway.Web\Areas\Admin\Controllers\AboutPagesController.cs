using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Technoloway.Core.Entities;
using Technoloway.Core.Interfaces;
using Technoloway.Web.Areas.Admin.Models;

namespace Technoloway.Web.Areas.Admin.Controllers
{
    [Area("Admin")]
    [Authorize(Policy = "RequireContentManager")]
    public class AboutPagesController : Controller
    {
        private readonly IAboutPageRepository _aboutPageRepository;

        public AboutPagesController(IAboutPageRepository aboutPageRepository)
        {
            _aboutPageRepository = aboutPageRepository;
        }

        public async Task<IActionResult> Index()
        {
            var aboutPages = await _aboutPageRepository.ListAllAsync();
            return View(aboutPages);
        }

        public async Task<IActionResult> Details(int id)
        {
            var aboutPage = await _aboutPageRepository.GetWithSectionsAsync(id);
            if (aboutPage == null)
            {
                return NotFound();
            }
            return View(aboutPage);
        }

        public IActionResult Create()
        {
            var model = new AboutPageViewModel
            {
                IsActive = true,
                Sections = new List<AboutPageSectionViewModel>(),
                // Set default values
                HeroTitle = "About Technoloway",
                HeroSubtitle = "Learn about our mission, values, and the passionate team behind our innovative software solutions.",
                StoryTitle = "Our Story",
                StorySubtitle = "Technoloway was founded with a mission to deliver innovative software solutions.",
                Stat1Number = "8+",
                Stat1Label = "Years Experience",
                Stat2Number = "100+",
                Stat2Label = "Projects Completed",
                MissionTitle = "Our Mission",
                VisionTitle = "Our Vision",
                ValuesTitle = "Our Values",
                CtaTitle = "Ready to Work With Us?",
                CtaSubtitle = "Let's discuss how we can help your business succeed with innovative software solutions.",
                CtaPrimaryButtonText = "Get Started",
                CtaPrimaryButtonUrl = "/contact",
                CtaSecondaryButtonText = "View Portfolio",
                CtaSecondaryButtonUrl = "/projects"
            };
            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(AboutPageViewModel model)
        {
            if (ModelState.IsValid)
            {
                var aboutPage = new AboutPage
                {
                    Title = model.Title,
                    MetaDescription = model.MetaDescription,
                    MetaKeywords = model.MetaKeywords,
                    HeroTitle = model.HeroTitle,
                    HeroSubtitle = model.HeroSubtitle,
                    HeroImageUrl = model.HeroImageUrl,
                    StoryTitle = model.StoryTitle,
                    StorySubtitle = model.StorySubtitle,
                    StoryContent = model.StoryContent,
                    StoryImageUrl = model.StoryImageUrl,
                    Stat1Number = model.Stat1Number,
                    Stat1Label = model.Stat1Label,
                    Stat2Number = model.Stat2Number,
                    Stat2Label = model.Stat2Label,
                    Stat3Number = model.Stat3Number,
                    Stat3Label = model.Stat3Label,
                    Stat4Number = model.Stat4Number,
                    Stat4Label = model.Stat4Label,
                    MissionTitle = model.MissionTitle,
                    MissionContent = model.MissionContent,
                    VisionTitle = model.VisionTitle,
                    VisionContent = model.VisionContent,
                    ValuesTitle = model.ValuesTitle,
                    ValuesContent = model.ValuesContent,
                    CtaTitle = model.CtaTitle,
                    CtaSubtitle = model.CtaSubtitle,
                    CtaPrimaryButtonText = model.CtaPrimaryButtonText,
                    CtaPrimaryButtonUrl = model.CtaPrimaryButtonUrl,
                    CtaSecondaryButtonText = model.CtaSecondaryButtonText,
                    CtaSecondaryButtonUrl = model.CtaSecondaryButtonUrl,
                    IsActive = model.IsActive,
                    LastModified = DateTime.UtcNow,
                    ModifiedBy = User.Identity?.Name
                };

                // Add sections
                if (model.Sections != null)
                {
                    foreach (var sectionModel in model.Sections)
                    {
                        aboutPage.Sections.Add(new AboutPageSection
                        {
                            Title = sectionModel.Title,
                            Content = sectionModel.Content,
                            IconClass = sectionModel.IconClass,
                            ImageUrl = sectionModel.ImageUrl,
                            DisplayOrder = sectionModel.DisplayOrder,
                            IsActive = sectionModel.IsActive,
                            SectionType = sectionModel.SectionType
                        });
                    }
                }

                await _aboutPageRepository.AddAsync(aboutPage);
                TempData["SuccessMessage"] = "About page created successfully.";
                return RedirectToAction(nameof(Index));
            }

            return View(model);
        }

        public async Task<IActionResult> Edit(int id)
        {
            var aboutPage = await _aboutPageRepository.GetWithSectionsAsync(id);
            if (aboutPage == null)
            {
                return NotFound();
            }

            var model = new AboutPageViewModel
            {
                Id = aboutPage.Id,
                Title = aboutPage.Title,
                MetaDescription = aboutPage.MetaDescription,
                MetaKeywords = aboutPage.MetaKeywords,
                HeroTitle = aboutPage.HeroTitle,
                HeroSubtitle = aboutPage.HeroSubtitle,
                HeroImageUrl = aboutPage.HeroImageUrl,
                StoryTitle = aboutPage.StoryTitle,
                StorySubtitle = aboutPage.StorySubtitle,
                StoryContent = aboutPage.StoryContent,
                StoryImageUrl = aboutPage.StoryImageUrl,
                Stat1Number = aboutPage.Stat1Number,
                Stat1Label = aboutPage.Stat1Label,
                Stat2Number = aboutPage.Stat2Number,
                Stat2Label = aboutPage.Stat2Label,
                Stat3Number = aboutPage.Stat3Number,
                Stat3Label = aboutPage.Stat3Label,
                Stat4Number = aboutPage.Stat4Number,
                Stat4Label = aboutPage.Stat4Label,
                MissionTitle = aboutPage.MissionTitle,
                MissionContent = aboutPage.MissionContent,
                VisionTitle = aboutPage.VisionTitle,
                VisionContent = aboutPage.VisionContent,
                ValuesTitle = aboutPage.ValuesTitle,
                ValuesContent = aboutPage.ValuesContent,
                CtaTitle = aboutPage.CtaTitle,
                CtaSubtitle = aboutPage.CtaSubtitle,
                CtaPrimaryButtonText = aboutPage.CtaPrimaryButtonText,
                CtaPrimaryButtonUrl = aboutPage.CtaPrimaryButtonUrl,
                CtaSecondaryButtonText = aboutPage.CtaSecondaryButtonText,
                CtaSecondaryButtonUrl = aboutPage.CtaSecondaryButtonUrl,
                IsActive = aboutPage.IsActive,
                Sections = aboutPage.Sections.Select(s => new AboutPageSectionViewModel
                {
                    Id = s.Id,
                    Title = s.Title,
                    Content = s.Content,
                    IconClass = s.IconClass,
                    ImageUrl = s.ImageUrl,
                    DisplayOrder = s.DisplayOrder,
                    IsActive = s.IsActive,
                    SectionType = s.SectionType
                }).ToList()
            };

            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, AboutPageViewModel model)
        {
            if (id != model.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                var aboutPage = await _aboutPageRepository.GetWithSectionsAsync(id);
                if (aboutPage == null)
                {
                    return NotFound();
                }

                // Update main properties
                aboutPage.Title = model.Title;
                aboutPage.MetaDescription = model.MetaDescription;
                aboutPage.MetaKeywords = model.MetaKeywords;
                aboutPage.HeroTitle = model.HeroTitle;
                aboutPage.HeroSubtitle = model.HeroSubtitle;
                aboutPage.HeroImageUrl = model.HeroImageUrl;
                aboutPage.StoryTitle = model.StoryTitle;
                aboutPage.StorySubtitle = model.StorySubtitle;
                aboutPage.StoryContent = model.StoryContent;
                aboutPage.StoryImageUrl = model.StoryImageUrl;
                aboutPage.Stat1Number = model.Stat1Number;
                aboutPage.Stat1Label = model.Stat1Label;
                aboutPage.Stat2Number = model.Stat2Number;
                aboutPage.Stat2Label = model.Stat2Label;
                aboutPage.Stat3Number = model.Stat3Number;
                aboutPage.Stat3Label = model.Stat3Label;
                aboutPage.Stat4Number = model.Stat4Number;
                aboutPage.Stat4Label = model.Stat4Label;
                aboutPage.MissionTitle = model.MissionTitle;
                aboutPage.MissionContent = model.MissionContent;
                aboutPage.VisionTitle = model.VisionTitle;
                aboutPage.VisionContent = model.VisionContent;
                aboutPage.ValuesTitle = model.ValuesTitle;
                aboutPage.ValuesContent = model.ValuesContent;
                aboutPage.CtaTitle = model.CtaTitle;
                aboutPage.CtaSubtitle = model.CtaSubtitle;
                aboutPage.CtaPrimaryButtonText = model.CtaPrimaryButtonText;
                aboutPage.CtaPrimaryButtonUrl = model.CtaPrimaryButtonUrl;
                aboutPage.CtaSecondaryButtonText = model.CtaSecondaryButtonText;
                aboutPage.CtaSecondaryButtonUrl = model.CtaSecondaryButtonUrl;
                aboutPage.IsActive = model.IsActive;
                aboutPage.LastModified = DateTime.UtcNow;
                aboutPage.ModifiedBy = User.Identity?.Name;

                // Update sections - remove existing and add new ones
                aboutPage.Sections.Clear();
                if (model.Sections != null)
                {
                    foreach (var sectionModel in model.Sections)
                    {
                        aboutPage.Sections.Add(new AboutPageSection
                        {
                            Title = sectionModel.Title,
                            Content = sectionModel.Content,
                            IconClass = sectionModel.IconClass,
                            ImageUrl = sectionModel.ImageUrl,
                            DisplayOrder = sectionModel.DisplayOrder,
                            IsActive = sectionModel.IsActive,
                            SectionType = sectionModel.SectionType
                        });
                    }
                }

                await _aboutPageRepository.UpdateAsync(aboutPage);
                TempData["SuccessMessage"] = "About page updated successfully.";
                return RedirectToAction(nameof(Index));
            }

            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Delete(int id)
        {
            var aboutPage = await _aboutPageRepository.GetByIdAsync(id);
            if (aboutPage == null)
            {
                return NotFound();
            }

            aboutPage.IsDeleted = true;
            await _aboutPageRepository.UpdateAsync(aboutPage);
            TempData["SuccessMessage"] = "About page deleted successfully.";
            return RedirectToAction(nameof(Index));
        }
    }
}
