using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Technoloway.Core.Entities;
using Technoloway.Infrastructure.Data;

namespace Technoloway.Web.Areas.Admin.Controllers
{
    [Area("Admin")]
    public class DataSeedController : Controller
    {
        private readonly ApplicationDbContext _context;

        public DataSeedController(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<IActionResult> Index()
        {
            var hasData = await _context.Categories.AnyAsync();
            ViewBag.HasData = hasData;
            return View();
        }

        [HttpPost]
        public async Task<IActionResult> SeedData()
        {
            try
            {
                // Check if data already exists
                if (await _context.Categories.AnyAsync())
                {
                    TempData["Error"] = "Data already exists in the database.";
                    return RedirectToAction("Index");
                }

                await SeedSampleData();
                TempData["Success"] = "Sample data has been successfully inserted!";
            }
            catch (Exception ex)
            {
                TempData["Error"] = $"Error seeding data: {ex.Message}";
            }

            return RedirectToAction("Index");
        }

        [HttpPost]
        public async Task<IActionResult> ClearData()
        {
            try
            {
                // Clear all data in reverse order of dependencies
                _context.ServiceOptionFeatures.RemoveRange(_context.ServiceOptionFeatures);
                _context.ServiceOptions.RemoveRange(_context.ServiceOptions);
                _context.Services.RemoveRange(_context.Services);
                _context.Categories.RemoveRange(_context.Categories);
                
                await _context.SaveChangesAsync();
                TempData["Success"] = "All data has been cleared!";
            }
            catch (Exception ex)
            {
                TempData["Error"] = $"Error clearing data: {ex.Message}";
            }

            return RedirectToAction("Index");
        }

        private async Task SeedSampleData()
        {
            // Create Categories
            var webDevelopmentCategory = new Category
            {
                CategName = "Web Development",
                CategDesc = "Complete web development solutions including frontend, backend, and full-stack development",
                ParentID = null,
                CreatedAt = DateTime.UtcNow,
                IsDeleted = false
            };

            var mobileAppCategory = new Category
            {
                CategName = "Mobile App Development",
                CategDesc = "Native and cross-platform mobile application development for iOS and Android",
                ParentID = null,
                CreatedAt = DateTime.UtcNow,
                IsDeleted = false
            };

            var cloudServicesCategory = new Category
            {
                CategName = "Cloud Services",
                CategDesc = "Cloud infrastructure, deployment, and management services",
                ParentID = null,
                CreatedAt = DateTime.UtcNow,
                IsDeleted = false
            };

            var digitalMarketingCategory = new Category
            {
                CategName = "Digital Marketing",
                CategDesc = "SEO, social media marketing, and digital advertising solutions",
                ParentID = null,
                CreatedAt = DateTime.UtcNow,
                IsDeleted = false
            };

            _context.Categories.AddRange(webDevelopmentCategory, mobileAppCategory, cloudServicesCategory, digitalMarketingCategory);
            await _context.SaveChangesAsync();

            // Create Subcategories
            var frontendCategory = new Category
            {
                CategName = "Frontend Development",
                CategDesc = "User interface and user experience development",
                ParentID = webDevelopmentCategory.Id,
                CreatedAt = DateTime.UtcNow,
                IsDeleted = false
            };

            var backendCategory = new Category
            {
                CategName = "Backend Development",
                CategDesc = "Server-side development and API creation",
                ParentID = webDevelopmentCategory.Id,
                CreatedAt = DateTime.UtcNow,
                IsDeleted = false
            };

            _context.Categories.AddRange(frontendCategory, backendCategory);
            await _context.SaveChangesAsync();

            // Create Services
            var reactWebsiteService = new Service
            {
                Name = "React Website Development",
                Description = "Modern, responsive websites built with React.js framework",
                Price = 2500.00m,
                CategID = frontendCategory.Id,
                ServManager = "Frontend Team",
                IconClass = "fab fa-react",
                IsActive = true,
                DisplayOrder = 1,
                CreatedAt = DateTime.UtcNow,
                IsDeleted = false
            };

            var nodeApiService = new Service
            {
                Name = "Node.js API Development",
                Description = "RESTful APIs and backend services using Node.js and Express",
                Price = 3000.00m,
                CategID = backendCategory.Id,
                ServManager = "Backend Team",
                IconClass = "fab fa-node-js",
                IsActive = true,
                DisplayOrder = 1,
                CreatedAt = DateTime.UtcNow,
                IsDeleted = false
            };

            var mobileAppService = new Service
            {
                Name = "React Native App",
                Description = "Cross-platform mobile applications for iOS and Android",
                Price = 5000.00m,
                CategID = mobileAppCategory.Id,
                ServManager = "Mobile Team",
                IconClass = "fas fa-mobile-alt",
                IsActive = true,
                DisplayOrder = 1,
                CreatedAt = DateTime.UtcNow,
                IsDeleted = false
            };

            var awsDeploymentService = new Service
            {
                Name = "AWS Cloud Deployment",
                Description = "Deploy and manage applications on Amazon Web Services",
                Price = 1500.00m,
                CategID = cloudServicesCategory.Id,
                ServManager = "DevOps Team",
                IconClass = "fab fa-aws",
                IsActive = true,
                DisplayOrder = 1,
                CreatedAt = DateTime.UtcNow,
                IsDeleted = false
            };

            var seoService = new Service
            {
                Name = "SEO Optimization",
                Description = "Search engine optimization to improve website visibility",
                Price = 800.00m,
                CategID = digitalMarketingCategory.Id,
                ServManager = "Marketing Team",
                IconClass = "fas fa-search",
                IsActive = true,
                DisplayOrder = 1,
                CreatedAt = DateTime.UtcNow,
                IsDeleted = false
            };

            _context.Services.AddRange(reactWebsiteService, nodeApiService, mobileAppService, awsDeploymentService, seoService);
            await _context.SaveChangesAsync();

            // Create Service Options
            var basicReactOption = new ServiceOption
            {
                OptName = "Basic React Package",
                OptDesc = "Standard React website with basic features",
                OptCost = 2500.00m,
                OptAvailability = "Available",
                OptDiscountRate = 0,
                ServID = reactWebsiteService.Id,
                CreatedAt = DateTime.UtcNow,
                IsDeleted = false
            };

            var premiumReactOption = new ServiceOption
            {
                OptName = "Premium React Package",
                OptDesc = "Advanced React website with premium features and animations",
                OptCost = 4000.00m,
                OptAvailability = "Available",
                OptDiscountRate = 10,
                ServID = reactWebsiteService.Id,
                CreatedAt = DateTime.UtcNow,
                IsDeleted = false
            };

            var basicApiOption = new ServiceOption
            {
                OptName = "Basic API Package",
                OptDesc = "Simple REST API with basic CRUD operations",
                OptCost = 3000.00m,
                OptAvailability = "Available",
                OptDiscountRate = 0,
                ServID = nodeApiService.Id,
                CreatedAt = DateTime.UtcNow,
                IsDeleted = false
            };

            var enterpriseApiOption = new ServiceOption
            {
                OptName = "Enterprise API Package",
                OptDesc = "Advanced API with authentication, rate limiting, and monitoring",
                OptCost = 5500.00m,
                OptAvailability = "Available",
                OptDiscountRate = 15,
                ServID = nodeApiService.Id,
                CreatedAt = DateTime.UtcNow,
                IsDeleted = false
            };

            var basicMobileOption = new ServiceOption
            {
                OptName = "Basic Mobile App",
                OptDesc = "Simple mobile app with core functionality",
                OptCost = 5000.00m,
                OptAvailability = "Available",
                OptDiscountRate = 0,
                ServID = mobileAppService.Id,
                CreatedAt = DateTime.UtcNow,
                IsDeleted = false
            };

            var advancedMobileOption = new ServiceOption
            {
                OptName = "Advanced Mobile App",
                OptDesc = "Feature-rich mobile app with advanced integrations",
                OptCost = 8000.00m,
                OptAvailability = "Available",
                OptDiscountRate = 20,
                ServID = mobileAppService.Id,
                CreatedAt = DateTime.UtcNow,
                IsDeleted = false
            };

            _context.ServiceOptions.AddRange(basicReactOption, premiumReactOption, basicApiOption, enterpriseApiOption, basicMobileOption, advancedMobileOption);
            await _context.SaveChangesAsync();

            // Create Service Option Features
            var features = new List<ServiceOptionFeature>
            {
                // Basic React Package Features
                new ServiceOptionFeature
                {
                    FeatName = "Responsive Design",
                    FeatDesc = "Mobile-first responsive design that works on all devices",
                    FeatCost = 0,
                    FeatAvailability = "Included",
                    FeatDiscountRate = 0,
                    OptID = basicReactOption.Id,
                    CreatedAt = DateTime.UtcNow,
                    IsDeleted = false
                },
                new ServiceOptionFeature
                {
                    FeatName = "Basic SEO Setup",
                    FeatDesc = "Meta tags, structured data, and basic SEO optimization",
                    FeatCost = 200,
                    FeatAvailability = "Optional",
                    FeatDiscountRate = 0,
                    OptID = basicReactOption.Id,
                    CreatedAt = DateTime.UtcNow,
                    IsDeleted = false
                },
                new ServiceOptionFeature
                {
                    FeatName = "Contact Form",
                    FeatDesc = "Functional contact form with email integration",
                    FeatCost = 150,
                    FeatAvailability = "Optional",
                    FeatDiscountRate = 0,
                    OptID = basicReactOption.Id,
                    CreatedAt = DateTime.UtcNow,
                    IsDeleted = false
                },
                // Premium React Package Features
                new ServiceOptionFeature
                {
                    FeatName = "Advanced Animations",
                    FeatDesc = "Smooth animations and transitions using Framer Motion",
                    FeatCost = 0,
                    FeatAvailability = "Included",
                    FeatDiscountRate = 0,
                    OptID = premiumReactOption.Id,
                    CreatedAt = DateTime.UtcNow,
                    IsDeleted = false
                },
                new ServiceOptionFeature
                {
                    FeatName = "Content Management System",
                    FeatDesc = "Easy-to-use CMS for content updates",
                    FeatCost = 0,
                    FeatAvailability = "Included",
                    FeatDiscountRate = 0,
                    OptID = premiumReactOption.Id,
                    CreatedAt = DateTime.UtcNow,
                    IsDeleted = false
                },
                new ServiceOptionFeature
                {
                    FeatName = "Analytics Integration",
                    FeatDesc = "Google Analytics and performance tracking",
                    FeatCost = 100,
                    FeatAvailability = "Optional",
                    FeatDiscountRate = 0,
                    OptID = premiumReactOption.Id,
                    CreatedAt = DateTime.UtcNow,
                    IsDeleted = false
                },
                // Basic API Package Features
                new ServiceOptionFeature
                {
                    FeatName = "CRUD Operations",
                    FeatDesc = "Create, Read, Update, Delete operations for all entities",
                    FeatCost = 0,
                    FeatAvailability = "Included",
                    FeatDiscountRate = 0,
                    OptID = basicApiOption.Id,
                    CreatedAt = DateTime.UtcNow,
                    IsDeleted = false
                },
                new ServiceOptionFeature
                {
                    FeatName = "API Documentation",
                    FeatDesc = "Comprehensive API documentation with Swagger",
                    FeatCost = 300,
                    FeatAvailability = "Optional",
                    FeatDiscountRate = 0,
                    OptID = basicApiOption.Id,
                    CreatedAt = DateTime.UtcNow,
                    IsDeleted = false
                },
                new ServiceOptionFeature
                {
                    FeatName = "Unit Testing",
                    FeatDesc = "Comprehensive unit tests for all API endpoints",
                    FeatCost = 500,
                    FeatAvailability = "Optional",
                    FeatDiscountRate = 10,
                    OptID = basicApiOption.Id,
                    CreatedAt = DateTime.UtcNow,
                    IsDeleted = false
                },
                // Enterprise API Package Features
                new ServiceOptionFeature
                {
                    FeatName = "JWT Authentication",
                    FeatDesc = "Secure JWT-based authentication and authorization",
                    FeatCost = 0,
                    FeatAvailability = "Included",
                    FeatDiscountRate = 0,
                    OptID = enterpriseApiOption.Id,
                    CreatedAt = DateTime.UtcNow,
                    IsDeleted = false
                },
                new ServiceOptionFeature
                {
                    FeatName = "Rate Limiting",
                    FeatDesc = "API rate limiting to prevent abuse",
                    FeatCost = 0,
                    FeatAvailability = "Included",
                    FeatDiscountRate = 0,
                    OptID = enterpriseApiOption.Id,
                    CreatedAt = DateTime.UtcNow,
                    IsDeleted = false
                },
                new ServiceOptionFeature
                {
                    FeatName = "Performance Monitoring",
                    FeatDesc = "Real-time API performance monitoring and alerts",
                    FeatCost = 200,
                    FeatAvailability = "Optional",
                    FeatDiscountRate = 0,
                    OptID = enterpriseApiOption.Id,
                    CreatedAt = DateTime.UtcNow,
                    IsDeleted = false
                },
                // Basic Mobile App Features
                new ServiceOptionFeature
                {
                    FeatName = "Native UI Components",
                    FeatDesc = "Platform-specific UI components for iOS and Android",
                    FeatCost = 0,
                    FeatAvailability = "Included",
                    FeatDiscountRate = 0,
                    OptID = basicMobileOption.Id,
                    CreatedAt = DateTime.UtcNow,
                    IsDeleted = false
                },
                new ServiceOptionFeature
                {
                    FeatName = "Offline Support",
                    FeatDesc = "Basic offline functionality and data caching",
                    FeatCost = 800,
                    FeatAvailability = "Optional",
                    FeatDiscountRate = 0,
                    OptID = basicMobileOption.Id,
                    CreatedAt = DateTime.UtcNow,
                    IsDeleted = false
                },
                new ServiceOptionFeature
                {
                    FeatName = "Push Notifications",
                    FeatDesc = "Firebase push notifications for user engagement",
                    FeatCost = 600,
                    FeatAvailability = "Optional",
                    FeatDiscountRate = 15,
                    OptID = basicMobileOption.Id,
                    CreatedAt = DateTime.UtcNow,
                    IsDeleted = false
                },
                // Advanced Mobile App Features
                new ServiceOptionFeature
                {
                    FeatName = "Advanced UI/UX",
                    FeatDesc = "Custom animations, gestures, and advanced UI patterns",
                    FeatCost = 0,
                    FeatAvailability = "Included",
                    FeatDiscountRate = 0,
                    OptID = advancedMobileOption.Id,
                    CreatedAt = DateTime.UtcNow,
                    IsDeleted = false
                },
                new ServiceOptionFeature
                {
                    FeatName = "Third-party Integrations",
                    FeatDesc = "Integration with social media, payment gateways, and APIs",
                    FeatCost = 0,
                    FeatAvailability = "Included",
                    FeatDiscountRate = 0,
                    OptID = advancedMobileOption.Id,
                    CreatedAt = DateTime.UtcNow,
                    IsDeleted = false
                },
                new ServiceOptionFeature
                {
                    FeatName = "Advanced Analytics",
                    FeatDesc = "User behavior tracking and advanced analytics",
                    FeatCost = 400,
                    FeatAvailability = "Optional",
                    FeatDiscountRate = 0,
                    OptID = advancedMobileOption.Id,
                    CreatedAt = DateTime.UtcNow,
                    IsDeleted = false
                }
            };

            _context.ServiceOptionFeatures.AddRange(features);
            await _context.SaveChangesAsync();
        }
    }
}
