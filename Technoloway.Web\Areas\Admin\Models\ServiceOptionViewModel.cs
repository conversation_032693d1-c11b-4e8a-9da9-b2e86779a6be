using System.ComponentModel.DataAnnotations;
using Technoloway.Core.Entities;

namespace Technoloway.Web.Areas.Admin.Models
{
    public class ServiceOptionViewModel
    {
        public int Id { get; set; }

        [Required]
        [Display(Name = "Option ID")]
        public int OptID { get; set; }

        [Required]
        [Display(Name = "Service ID")]
        public int ServID { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "Option Name")]
        public string OptName { get; set; } = string.Empty;

        [Display(Name = "Cost")]
        [Range(0, double.MaxValue, ErrorMessage = "Cost must be a positive number")]
        public decimal? OptCost { get; set; }

        [Display(Name = "Discount Rate (%)")]
        [Range(0, 100, ErrorMessage = "Discount rate must be between 0 and 100")]
        public int? OptDiscountRate { get; set; }

        [Display(Name = "Total Discount")]
        [Range(0, double.MaxValue, ErrorMessage = "Total discount must be a positive number")]
        public decimal? OptTotalDiscount { get; set; }

        [StringLength(12)]
        [Display(Name = "Availability")]
        public string? OptAvailability { get; set; }

        [Display(Name = "Description")]
        public string? OptDesc { get; set; }

        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        // Convert from Entity to ViewModel
        public static ServiceOptionViewModel FromEntity(ServiceOption serviceOption)
        {
            return new ServiceOptionViewModel
            {
                Id = serviceOption.Id,
                OptID = serviceOption.OptID,
                ServID = serviceOption.ServID,
                OptName = serviceOption.OptName,
                OptCost = serviceOption.OptCost,
                OptDiscountRate = serviceOption.OptDiscountRate,
                OptTotalDiscount = serviceOption.OptTotalDiscount,
                OptAvailability = serviceOption.OptAvailability,
                OptDesc = serviceOption.OptDesc,
                CreatedAt = serviceOption.CreatedAt,
                UpdatedAt = serviceOption.UpdatedAt
            };
        }

        // Convert from ViewModel to Entity
        public ServiceOption ToEntity()
        {
            return new ServiceOption
            {
                Id = Id,
                OptID = OptID,
                ServID = ServID,
                OptName = OptName,
                OptCost = OptCost,
                OptDiscountRate = OptDiscountRate,
                OptTotalDiscount = OptTotalDiscount,
                OptAvailability = OptAvailability,
                OptDesc = OptDesc,
                CreatedAt = CreatedAt,
                UpdatedAt = UpdatedAt
            };
        }
    }
}
