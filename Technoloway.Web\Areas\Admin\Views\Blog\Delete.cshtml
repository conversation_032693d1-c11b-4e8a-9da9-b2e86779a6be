@model Technoloway.Core.Entities.BlogPost

@{
    ViewData["Title"] = "Delete Blog Post";
    Layout = "_AdminLayout";
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Delete Blog Post</h1>
    <a asp-action="Index" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">
        <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to List
    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Confirm Deletion</h6>
    </div>
    <div class="card-body">
        <div class="alert alert-danger">
            <h5>Are you sure you want to delete this blog post?</h5>
            <p>This action will mark the post as deleted but will not permanently remove it from the database.</p>
        </div>
        
        <div class="mb-4">
            <h4>@Model.Title</h4>
            <p class="text-muted">
                @if (Model.IsPublished)
                {
                    <span class="badge bg-success">Published</span>
                }
                else
                {
                    <span class="badge bg-secondary">Draft</span>
                }
                <span class="ms-2">Created: @Model.CreatedAt.ToString("yyyy-MM-dd")</span>
                @if (Model.PublishedAt.HasValue)
                {
                    <span class="ms-2">Published: @Model.PublishedAt.Value.ToString("yyyy-MM-dd")</span>
                }
            </p>
            
            @if (!string.IsNullOrEmpty(Model.Excerpt))
            {
                <p>@Model.Excerpt</p>
            }
        </div>
        
        <form asp-action="Delete" method="post">
            <input type="hidden" asp-for="Id" />
            <button type="submit" class="btn btn-danger">Delete</button>
            <a asp-action="Index" class="btn btn-secondary">Cancel</a>
        </form>
    </div>
</div>
