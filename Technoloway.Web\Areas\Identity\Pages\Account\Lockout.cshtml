@page
@model Technoloway.Web.Areas.Identity.Pages.Account.LockoutModel
@{
    ViewData["Title"] = "Account Locked";
    Layout = "_ClientLoginLayout";
}

<div class="modern-client-login-header">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="login-header-content text-center">
                    <div class="login-breadcrumb">
                        <a href="/" class="breadcrumb-link">
                            <i class="fas fa-home"></i>
                            <span>Home</span>
                        </a>
                        <span class="breadcrumb-separator">•</span>
                        <span class="breadcrumb-current">Account Locked</span>
                    </div>
                    <h1 class="login-title">
                        <span class="title-highlight">Account</span> Locked
                    </h1>
                    <p class="login-subtitle">
                        Your account has been temporarily locked for security reasons
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<section class="modern-section client-login-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-6 col-md-8">
                <div class="modern-card login-card">
                    <div class="card-header text-center">
                        <div class="login-logo">
                            <img src="~/images/technoloway-logo.svg" alt="Technoloway Logo" class="brand-logo" />
                        </div>
                        <h3 class="card-title">
                            <span class="title-highlight">Account Locked</span>
                        </h3>
                        <p class="card-subtitle">
                            Security protection activated
                        </p>
                    </div>

                    <div class="card-body text-center">
                        <div class="lockout-icon mb-4">
                            <i class="fas fa-lock text-warning" style="font-size: 4rem;"></i>
                        </div>

                        <div class="lockout-message mb-4">
                            <h5 class="text-warning mb-3">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Account Temporarily Locked
                            </h5>
                            <p class="mb-3">
                                Your account has been locked due to multiple failed login attempts.
                                This is a security measure to protect your account.
                            </p>
                        </div>

                        <div class="lockout-info mb-4">
                            <div class="info-card">
                                <h6><i class="fas fa-clock me-2"></i>What happens next?</h6>
                                <ul class="list-unstyled text-start">
                                    <li><i class="fas fa-check text-success me-2"></i>Your account will be automatically unlocked after a short period</li>
                                    <li><i class="fas fa-check text-success me-2"></i>You can try logging in again later</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Contact support if you need immediate assistance</li>
                                </ul>
                            </div>
                        </div>

                        <div class="lockout-actions">
                            <div class="d-grid gap-2">
                                <a href="/" class="btn btn-primary">
                                    <i class="fas fa-home me-2"></i>Return to Homepage
                                </a>
                                <div class="row">
                                    <div class="col-6">
                                        <a asp-page="./Login" class="btn btn-outline-primary w-100">
                                            <i class="fas fa-sign-in-alt me-2"></i>Try Again Later
                                        </a>
                                    </div>
                                    <div class="col-6">
                                        <a href="/Contact" class="btn btn-outline-info w-100">
                                            <i class="fas fa-envelope me-2"></i>Contact Support
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="security-tips mt-4">
                            <h6><i class="fas fa-shield-alt me-2"></i>Security Tips</h6>
                            <div class="tips-list text-start">
                                <small class="text-muted">
                                    <div class="tip-item">
                                        <i class="fas fa-key me-1"></i>
                                        Make sure you're using the correct password
                                    </div>
                                    <div class="tip-item">
                                        <i class="fas fa-eye me-1"></i>
                                        Check if Caps Lock is enabled
                                    </div>
                                    <div class="tip-item">
                                        <i class="fas fa-user-shield me-1"></i>
                                        Contact us if you suspect unauthorized access
                                    </div>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.info-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.tip-item {
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
}

.tips-list {
    background: #f8f9fa;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-top: 0.5rem;
}

.lockout-icon {
    animation: pulse 2s infinite;
}

@@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}
</style>
