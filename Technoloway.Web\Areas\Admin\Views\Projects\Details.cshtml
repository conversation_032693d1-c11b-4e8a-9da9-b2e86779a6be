@model Technoloway.Core.Entities.Project

@{
    ViewData["Title"] = "Project Details";
    Layout = "_AdminLayout";
    var documents = ViewBag.Documents as IEnumerable<Technoloway.Core.Entities.ProjectDocument>;
    var messages = ViewBag.Messages as IEnumerable<Technoloway.Core.Entities.Message>;
    var invoices = ViewBag.Invoices as IEnumerable<Technoloway.Core.Entities.Invoice>;
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">@Model.Name</h1>
    <div>
        <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-sm btn-primary shadow-sm">
            <i class="fas fa-edit fa-sm text-white-50"></i> Edit
        </a>
        <a asp-action="Documents" asp-route-id="@Model.Id" class="btn btn-sm btn-success shadow-sm">
            <i class="fas fa-file fa-sm text-white-50"></i> Documents
        </a>
        <a asp-action="Messages" asp-route-id="@Model.Id" class="btn btn-sm btn-warning shadow-sm">
            <i class="fas fa-comments fa-sm text-white-50"></i> Messages
        </a>
        <a asp-action="Index" class="btn btn-sm btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to List
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Project Information</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        @if (!string.IsNullOrEmpty(Model.ImageUrl))
                        {
                            <div class="text-center mb-4">
                                <img src="@Model.ImageUrl" alt="@Model.Name" class="img-fluid rounded" style="max-height: 200px;" />
                            </div>
                        }

                        <dl class="row">
                            <dt class="col-sm-4">Name</dt>
                            <dd class="col-sm-8">@Model.Name</dd>

                            <dt class="col-sm-4">Client</dt>
                            <dd class="col-sm-8">
                                @if (Model.Client != null)
                                {
                                    <a asp-controller="Clients" asp-action="Details" asp-route-id="@Model.ClientId">
                                        @Model.Client.CompanyName
                                    </a>
                                }
                                else
                                {
                                    <span>@Model.ClientName</span>
                                }
                            </dd>

                            <dt class="col-sm-4">Order</dt>
                            <dd class="col-sm-8">
                                @if (Model.Order != null)
                                {
                                    <span>@Model.Order.OrderTitle</span>
                                }
                                else
                                {
                                    <span>-</span>
                                }
                            </dd>

                            <dt class="col-sm-4">Completion</dt>
                            <dd class="col-sm-8">
                                @(Model.ProjCompletionDate?.ToString("yyyy-MM-dd") ?? "Not set")
                                @if (Model.ProjCompletionDate.HasValue && Model.ProjCompletionDate > DateTime.UtcNow)
                                {
                                    <span class="badge bg-primary ml-2">In Progress</span>
                                }
                                else if (Model.ProjCompletionDate.HasValue)
                                {
                                    <span class="badge bg-success ml-2">Completed</span>
                                }
                            </dd>

                            <dt class="col-sm-4">Featured</dt>
                            <dd class="col-sm-8">
                                @if (Model.IsFeatured)
                                {
                                    <span class="badge bg-success">Yes</span>
                                }
                                else
                                {
                                    <span class="badge bg-secondary">No</span>
                                }
                            </dd>

                            <dt class="col-sm-4">Display Order</dt>
                            <dd class="col-sm-8">@Model.DisplayOrder</dd>

                            <dt class="col-sm-4">Created</dt>
                            <dd class="col-sm-8">@Model.CreatedAt.ToString("yyyy-MM-dd")</dd>

                            @if (Model.UpdatedAt.HasValue)
                            {
                                <dt class="col-sm-4">Updated</dt>
                                <dd class="col-sm-8">@Model.UpdatedAt.Value.ToString("yyyy-MM-dd")</dd>
                            }

                            @if (!string.IsNullOrEmpty(Model.ProjectUrl))
                            {
                                <dt class="col-sm-4">Project URL</dt>
                                <dd class="col-sm-8">
                                    <a href="@Model.ProjectUrl" target="_blank">@Model.ProjectUrl</a>
                                </dd>
                            }
                        </dl>
                    </div>

                    <div class="col-md-6">
                        <h5 class="font-weight-bold">Description</h5>
                        <p>@Model.Description</p>

                        @if (Model.Technologies != null && Model.Technologies.Any())
                        {
                            <h5 class="font-weight-bold mt-4">Technologies</h5>
                            <div class="d-flex flex-wrap">
                                @foreach (var tech in Model.Technologies)
                                {
                                    <span class="badge bg-info m-1 p-2">@tech.Name</span>
                                }
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card shadow mb-4">
                    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                        <h6 class="m-0 font-weight-bold text-primary">Documents</h6>
                        <a asp-action="Documents" asp-route-id="@Model.Id" class="btn btn-sm btn-success">
                            <i class="fas fa-file"></i> View All
                        </a>
                    </div>
                    <div class="card-body">
                        @if (documents == null || !documents.Any())
                        {
                            <div class="alert alert-info">
                                <p class="mb-0">No documents have been uploaded for this project yet.</p>
                            </div>

                            <div class="text-center mt-3">
                                <a asp-action="AddDocument" asp-route-id="@Model.Id" class="btn btn-success">
                                    <i class="fas fa-upload"></i> Upload Document
                                </a>
                            </div>
                        }
                        else
                        {
                            <div class="list-group">
                                @foreach (var doc in ViewBag.RecentDocuments)
                                {
                                    <a href="@doc.FileUrl" target="_blank" class="list-group-item list-group-item-action">
                                        <div class="d-flex w-100 justify-content-between">
                                            <h6 class="mb-1">@doc.FileName</h6>
                                            <small>@doc.CreatedAt.ToString("yyyy-MM-dd")</small>
                                        </div>
                                        <small>@doc.FileType | @(doc.FileSize / 1024) KB</small>
                                    </a>
                                }
                            </div>

                            @if (documents.Count() > 5)
                            {
                                <div class="text-center mt-3">
                                    <a asp-action="Documents" asp-route-id="@Model.Id" class="btn btn-sm btn-outline-primary">
                                        View All @documents.Count() Documents
                                    </a>
                                </div>
                            }
                        }
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card shadow mb-4">
                    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                        <h6 class="m-0 font-weight-bold text-primary">Messages</h6>
                        <a asp-action="Messages" asp-route-id="@Model.Id" class="btn btn-sm btn-warning">
                            <i class="fas fa-comments"></i> View All
                        </a>
                    </div>
                    <div class="card-body">
                        @if (messages == null || !messages.Any())
                        {
                            <div class="alert alert-info">
                                <p class="mb-0">No messages have been added to this project yet.</p>
                            </div>

                            <div class="text-center mt-3">
                                <a asp-action="AddMessage" asp-route-id="@Model.Id" class="btn btn-warning">
                                    <i class="fas fa-comment"></i> Add Message
                                </a>
                            </div>
                        }
                        else
                        {
                            <div class="list-group">
                                @foreach (var msg in messages.OrderByDescending(m => m.CreatedAt).Take(5))
                                {
                                    <div class="list-group-item">
                                        <div class="d-flex w-100 justify-content-between">
                                            <h6 class="mb-1">@msg.SenderName</h6>
                                            <small>@msg.CreatedAt.ToString("yyyy-MM-dd HH:mm")</small>
                                        </div>
                                        <p class="mb-1">@msg.Content</p>
                                        <small>@msg.SenderRole</small>
                                    </div>
                                }
                            </div>

                            @if (messages.Count() > 5)
                            {
                                <div class="text-center mt-3">
                                    <a asp-action="Messages" asp-route-id="@Model.Id" class="btn btn-sm btn-outline-primary">
                                        View All @messages.Count() Messages
                                    </a>
                                </div>
                            }
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Invoices</h6>
            </div>
            <div class="card-body">
                @if (invoices == null || !invoices.Any())
                {
                    <div class="alert alert-info">
                        <p class="mb-0">No invoices have been created for this project yet.</p>
                    </div>

                    <div class="text-center mt-3">
                        <a asp-controller="Invoices" asp-action="Create" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Create Invoice
                        </a>
                    </div>
                }
                else
                {
                    <div class="text-center mb-4">
                        <h1 class="h3 mb-0 text-gray-800">@ViewBag.InvoiceCount</h1>
                        <p class="text-muted">Total Invoices</p>
                    </div>

                    <div class="text-center mb-4">
                        <h1 class="h4 mb-0 text-gray-800">@ViewBag.TotalInvoiceAmount.ToString("C")</h1>
                        <p class="text-muted">Total Amount</p>
                    </div>

                    <div class="text-center mb-4">
                        <h1 class="h4 mb-0 text-success">@ViewBag.PaidInvoiceAmount.ToString("C")</h1>
                        <p class="text-muted">Paid Amount</p>
                    </div>

                    <div class="list-group">
                        @foreach (var invoice in ViewBag.RecentInvoices)
                        {
                            <a asp-controller="Invoices" asp-action="Details" asp-route-id="@invoice.Id" class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">Invoice #@invoice.InvoiceNumber</h6>
                                    <small>@invoice.IssueDate.ToString("yyyy-MM-dd")</small>
                                </div>
                                <div class="d-flex w-100 justify-content-between">
                                    <small>Due: @invoice.DueDate.ToString("yyyy-MM-dd")</small>
                                    <span>
                                        @switch (invoice.Status)
                                        {
                                            case "Pending":
                                                <span class="badge bg-warning">Pending</span>
                                                break;
                                            case "Paid":
                                                <span class="badge bg-success">Paid</span>
                                                break;
                                            case "Overdue":
                                                <span class="badge bg-danger">Overdue</span>
                                                break;
                                            case "Cancelled":
                                                <span class="badge bg-secondary">Cancelled</span>
                                                break;
                                            default:
                                                <span class="badge bg-info">@invoice.Status</span>
                                                break;
                                        }
                                        <strong>@invoice.TotalAmount.ToString("C")</strong>
                                    </span>
                                </div>
                            </a>
                        }
                    </div>

                    @if (invoices.Count() > 5)
                    {
                        <div class="text-center mt-3">
                            <a asp-controller="Invoices" asp-action="Index" class="btn btn-sm btn-outline-primary">
                                View All @invoices.Count() Invoices
                            </a>
                        </div>
                    }
                }
            </div>
        </div>
    </div>
</div>
