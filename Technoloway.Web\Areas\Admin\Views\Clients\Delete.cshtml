@model Technoloway.Core.Entities.Client

@{
    ViewData["Title"] = "Delete Client";
    Layout = "_AdminLayout";
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Delete Client</h1>
    <a asp-action="Index" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">
        <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to List
    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Confirm Deletion</h6>
    </div>
    <div class="card-body">
        <div class="alert alert-danger">
            <h5>Are you sure you want to delete this client?</h5>
            <p>This action will mark the client as deleted but will not permanently remove it from the database.</p>
            
            @if (ViewBag.ProjectCount > 0 || ViewBag.InvoiceCount > 0)
            {
                <div class="mt-3">
                    <p><strong>Warning:</strong> This client has associated data that will be affected:</p>
                    <ul>
                        @if (ViewBag.ProjectCount > 0)
                        {
                            <li>@ViewBag.ProjectCount projects</li>
                        }
                        @if (ViewBag.InvoiceCount > 0)
                        {
                            <li>@ViewBag.InvoiceCount invoices</li>
                        }
                    </ul>
                </div>
            }
        </div>
        
        <div class="mb-4">
            <h4>@Model.CompanyName</h4>
            <p>
                <strong>Contact:</strong> @Model.ContactName<br />
                <strong>Email:</strong> @Model.ContactEmail<br />
                <strong>Phone:</strong> @Model.ContactPhone
            </p>
            
            <p>
                <strong>Address:</strong><br />
                @if (!string.IsNullOrEmpty(Model.Address))
                {
                    @Model.Address<br />
                }
                @if (!string.IsNullOrEmpty(Model.City) || !string.IsNullOrEmpty(Model.State) || !string.IsNullOrEmpty(Model.ZipCode))
                {
                    <span>
                        @if (!string.IsNullOrEmpty(Model.City))
                        {
                            <span>@Model.City</span>
                        }
                        @if (!string.IsNullOrEmpty(Model.State))
                        {
                            <span>@(string.IsNullOrEmpty(Model.City) ? "" : ", ")@Model.State</span>
                        }
                        @if (!string.IsNullOrEmpty(Model.ZipCode))
                        {
                            <span> @Model.ZipCode</span>
                        }
                    </span>
                    <br />
                }
                @if (!string.IsNullOrEmpty(Model.Country))
                {
                    @Model.Country
                }
            </p>
        </div>
        
        <form asp-action="Delete" method="post">
            <input type="hidden" asp-for="Id" />
            <button type="submit" class="btn btn-danger">Delete</button>
            <a asp-action="Index" class="btn btn-secondary">Cancel</a>
        </form>
    </div>
</div>
