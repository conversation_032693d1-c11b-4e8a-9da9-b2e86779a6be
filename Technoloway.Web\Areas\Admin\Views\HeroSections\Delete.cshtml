@model Technoloway.Core.Entities.HeroSection

@{
    ViewData["Title"] = "Delete Hero Section";
    ViewData["PageTitle"] = "Delete Hero Section";
    ViewData["PageDescription"] = "Confirm deletion of hero section";
}

@section Styles {
    <style>
        .delete-card {
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            margin-bottom: 1.5rem;
        }

        .delete-header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 0.5rem 0.5rem 0 0;
        }

        .delete-body {
            padding: 1.5rem;
        }

        .detail-item {
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #e9ecef;
        }

        .detail-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .detail-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.25rem;
        }

        .detail-value {
            color: #6c757d;
        }

        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }

        .warning-icon {
            color: #856404;
            font-size: 1.25rem;
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(220, 53, 69, 0.25);
        }

        .btn-danger:hover {
            transform: translateY(-1px);
            box-shadow: 0 0.25rem 0.5rem rgba(220, 53, 69, 0.35);
        }

        .slide-preview {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            background: #f8f9fa;
            font-size: 0.875rem;
        }

        .media-preview {
            max-width: 100px;
            max-height: 60px;
            border-radius: 0.25rem;
            object-fit: cover;
        }

        .slide-content-preview {
            max-height: 100px;
            overflow: hidden;
            position: relative;
            font-size: 0.875rem;
        }

        .slide-content-preview::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 20px;
            background: linear-gradient(transparent, white);
        }

        .slide-content-preview h1, .slide-content-preview h2, .slide-content-preview h3 {
            font-size: 0.875rem;
            margin: 0.125rem 0;
        }

        .slide-content-preview p {
            font-size: 0.75rem;
            margin: 0.125rem 0;
        }
    </style>
}

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-1">
                <i class="fas fa-trash me-2 text-danger"></i>Delete Hero Section
            </h1>
            <p class="text-muted mb-0">Confirm deletion of "@Model.Title"</p>
        </div>
        <div class="btn-group">
            <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-outline-info">
                <i class="fas fa-eye me-1"></i>View Details
            </a>
            <a asp-action="Index" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to List
            </a>
        </div>
    </div>

    <!-- Warning Box -->
    <div class="warning-box">
        <div class="d-flex align-items-center">
            <i class="fas fa-exclamation-triangle warning-icon me-3"></i>
            <div>
                <h6 class="mb-1 text-warning">Warning: This action cannot be undone</h6>
                <p class="mb-0 text-muted">
                    Deleting this hero section will permanently remove it from the system.
                    @if (Model.Slides.Any())
                    {
                        <span>This will also delete @Model.Slides.Count associated slide(s).</span>
                    }
                </p>
            </div>
        </div>
    </div>

    <!-- Hero Section Details -->
    <div class="delete-card">
        <div class="delete-header">
            <h5 class="mb-0">
                <i class="fas fa-info-circle me-2"></i>Hero Section Information
            </h5>
        </div>
        <div class="delete-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="detail-item">
                        <div class="detail-label">Title</div>
                        <div class="detail-value">@Model.Title</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Page Assignment</div>
                        <div class="detail-value">@Model.PageName</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Status</div>
                        <div class="detail-value">
                            @if (Model.IsActive)
                            {
                                <span class="badge bg-success">Active</span>
                            }
                            else
                            {
                                <span class="badge bg-secondary">Inactive</span>
                            }
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="detail-item">
                        <div class="detail-label">Main Title</div>
                        <div class="detail-value">@Html.Raw(Model.MainTitle ?? "Not set")</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Slideshow</div>
                        <div class="detail-value">
                            @if (Model.EnableSlideshow)
                            {
                                <span class="badge bg-primary">Enabled</span>
                            }
                            else
                            {
                                <span class="badge bg-secondary">Disabled</span>
                            }
                        </div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Number of Slides</div>
                        <div class="detail-value">@Model.Slides.Count</div>
                    </div>
                </div>
            </div>

            @if (!string.IsNullOrEmpty(Model.MainSubtitle))
            {
                <div class="detail-item">
                    <div class="detail-label">Main Subtitle</div>
                    <div class="detail-value">@Model.MainSubtitle</div>
                </div>
            }

            @if (!string.IsNullOrEmpty(Model.MainDescription))
            {
                <div class="detail-item">
                    <div class="detail-label">Main Description</div>
                    <div class="detail-value">@Model.MainDescription</div>
                </div>
            }

            @if (Model.Slides.Any())
            {
                <div class="detail-item">
                    <div class="detail-label">Associated Slides</div>
                    <div class="detail-value">
                        @foreach (var slide in Model.Slides.OrderBy(s => s.DisplayOrder))
                        {
                            <div class="slide-preview">
                                <div class="d-flex align-items-center">
                                    @if (slide.MediaType == "video" && !string.IsNullOrEmpty(slide.VideoUrl))
                                    {
                                        <video class="media-preview me-2" muted>
                                            <source src="@slide.VideoUrl" type="video/mp4">
                                        </video>
                                        <span class="badge bg-info me-2">Video</span>
                                    }
                                    else if (!string.IsNullOrEmpty(slide.ImageUrl))
                                    {
                                        <img src="@slide.ImageUrl" alt="@slide.MediaAlt" class="media-preview me-2">
                                        <span class="badge bg-success me-2">Image</span>
                                    }
                                    else
                                    {
                                        <div class="media-preview bg-light d-flex align-items-center justify-content-center me-2">
                                            <i class="fas fa-image text-muted"></i>
                                        </div>
                                        <span class="badge bg-secondary me-2">No Media</span>
                                    }
                                    <div class="flex-grow-1">
                                        <div class="slide-content-preview">
                                            @Html.Raw(slide.Content)
                                        </div>
                                    </div>
                                    <span class="badge bg-primary">Order: @slide.DisplayOrder</span>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            }
        </div>
    </div>

    <!-- Confirmation Form -->
    <form asp-action="Delete" method="post" class="d-flex justify-content-between align-items-center">
        <div>
            <a asp-action="Index" class="btn btn-outline-secondary">
                <i class="fas fa-times me-1"></i>Cancel
            </a>
            <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-outline-primary ms-2">
                <i class="fas fa-edit me-1"></i>Edit Instead
            </a>
        </div>
        <div>
            <button type="submit" class="btn btn-danger btn-lg" onclick="return confirm('Are you absolutely sure you want to delete this hero section? This action cannot be undone.')">
                <i class="fas fa-trash me-2"></i>Delete Hero Section
            </button>
        </div>
    </form>
</div>



@section Scripts {
    <script>
        // Additional confirmation for delete
        document.querySelector('form').addEventListener('submit', function(e) {
            const confirmed = confirm('This will permanently delete the hero section "' + '@Model.Title' + '" and all its slides. Are you absolutely sure?');
            if (!confirmed) {
                e.preventDefault();
            }
        });
    </script>
}
