@{
    ViewData["Title"] = "Data Seeding";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-database me-2"></i>
                        Database Seeding
                    </h3>
                </div>
                <div class="card-body">
                    @if (TempData["Success"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            @TempData["Success"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    @if (TempData["Error"] != null)
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            @TempData["Error"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">
                                        <i class="fas fa-plus-circle me-2"></i>
                                        Seed Sample Data
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">
                                        Insert comprehensive sample data including:
                                    </p>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-folder text-primary me-2"></i> 4 Main Categories</li>
                                        <li><i class="fas fa-folder-open text-secondary me-2"></i> 2 Subcategories</li>
                                        <li><i class="fas fa-server text-success me-2"></i> 5 Services</li>
                                        <li><i class="fas fa-cogs text-warning me-2"></i> 6 Service Options</li>
                                        <li><i class="fas fa-star text-info me-2"></i> 18 Service Features</li>
                                    </ul>
                                    
                                    @if (ViewBag.HasData == true)
                                    {
                                        <div class="alert alert-warning">
                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                            Data already exists in the database. Clear existing data first if you want to reseed.
                                        </div>
                                        <button type="button" class="btn btn-primary" disabled>
                                            <i class="fas fa-plus-circle me-2"></i>
                                            Seed Sample Data
                                        </button>
                                    }
                                    else
                                    {
                                        <form method="post" asp-action="SeedData" onsubmit="return confirm('Are you sure you want to seed sample data?');">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-plus-circle me-2"></i>
                                                Seed Sample Data
                                            </button>
                                        </form>
                                    }
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card border-danger">
                                <div class="card-header bg-danger text-white">
                                    <h5 class="mb-0">
                                        <i class="fas fa-trash me-2"></i>
                                        Clear All Data
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">
                                        Remove all existing data from the database:
                                    </p>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-star text-info me-2"></i> Service Features</li>
                                        <li><i class="fas fa-cogs text-warning me-2"></i> Service Options</li>
                                        <li><i class="fas fa-server text-success me-2"></i> Services</li>
                                        <li><i class="fas fa-folder text-primary me-2"></i> Categories</li>
                                    </ul>
                                    
                                    @if (ViewBag.HasData == false)
                                    {
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle me-2"></i>
                                            No data exists in the database.
                                        </div>
                                        <button type="button" class="btn btn-danger" disabled>
                                            <i class="fas fa-trash me-2"></i>
                                            Clear All Data
                                        </button>
                                    }
                                    else
                                    {
                                        <form method="post" asp-action="ClearData" onsubmit="return confirm('Are you sure you want to clear ALL data? This action cannot be undone!');">
                                            <button type="submit" class="btn btn-danger">
                                                <i class="fas fa-trash me-2"></i>
                                                Clear All Data
                                            </button>
                                        </form>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h5 class="mb-0">
                                        <i class="fas fa-info-circle me-2"></i>
                                        Sample Data Overview
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6 class="text-primary">Categories & Services:</h6>
                                            <ul class="list-unstyled ms-3">
                                                <li><strong>Web Development</strong>
                                                    <ul>
                                                        <li>Frontend Development → React Website Development</li>
                                                        <li>Backend Development → Node.js API Development</li>
                                                    </ul>
                                                </li>
                                                <li><strong>Mobile App Development</strong> → React Native App</li>
                                                <li><strong>Cloud Services</strong> → AWS Cloud Deployment</li>
                                                <li><strong>Digital Marketing</strong> → SEO Optimization</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-6">
                                            <h6 class="text-warning">Service Options & Features:</h6>
                                            <ul class="list-unstyled ms-3">
                                                <li><strong>React Website:</strong> Basic & Premium packages</li>
                                                <li><strong>Node.js API:</strong> Basic & Enterprise packages</li>
                                                <li><strong>Mobile App:</strong> Basic & Advanced packages</li>
                                                <li>Each package includes multiple features (included & optional)</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <a href="@Url.Action("Index", "Services")" class="btn btn-success">
                            <i class="fas fa-eye me-2"></i>
                            View Services Management
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
