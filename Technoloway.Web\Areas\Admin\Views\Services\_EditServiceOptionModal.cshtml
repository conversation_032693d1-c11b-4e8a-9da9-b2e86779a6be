@model Technoloway.Web.Areas.Admin.Models.ServiceOptionViewModel

<div class="modal-header">
    <h5 class="modal-title">
        <i class="fas fa-edit me-2"></i>Edit Service Option
    </h5>
    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
</div>

<form id="serviceOptionForm" asp-action="EditServiceOption" method="post">
    @Html.AntiForgeryToken()
    <input asp-for="Id" type="hidden" />
    <input asp-for="OptID" type="hidden" />
    <input asp-for="ServID" type="hidden" />
    
    <div class="modal-body">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            Editing option for service: <strong>@ViewBag.ServiceName</strong>
        </div>

        <div class="mb-3">
            <label asp-for="OptName" class="form-label">Option Name</label>
            <input asp-for="OptName" class="form-control" placeholder="e.g., Basic Package, Premium Package" />
            <span asp-validation-for="OptName" class="text-danger"></span>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label asp-for="OptCost" class="form-label">Cost</label>
                    <div class="input-group">
                        <span class="input-group-text">$</span>
                        <input asp-for="OptCost" class="form-control" placeholder="0.00" />
                    </div>
                    <span asp-validation-for="OptCost" class="text-danger"></span>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label asp-for="OptDiscountRate" class="form-label">Discount Rate (%)</label>
                    <input asp-for="OptDiscountRate" class="form-control" placeholder="0" />
                    <span asp-validation-for="OptDiscountRate" class="text-danger"></span>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label asp-for="OptTotalDiscount" class="form-label">Total Discount</label>
                    <div class="input-group">
                        <span class="input-group-text">$</span>
                        <input asp-for="OptTotalDiscount" class="form-control" placeholder="0.00" />
                    </div>
                    <span asp-validation-for="OptTotalDiscount" class="text-danger"></span>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label asp-for="OptAvailability" class="form-label">Availability</label>
                    <select asp-for="OptAvailability" class="form-select">
                        <option value="Available">Available</option>
                        <option value="Limited">Limited</option>
                        <option value="Unavailable">Unavailable</option>
                    </select>
                    <span asp-validation-for="OptAvailability" class="text-danger"></span>
                </div>
            </div>
        </div>

        <div class="mb-3">
            <label asp-for="OptDesc" class="form-label">Description</label>
            <textarea asp-for="OptDesc" class="form-control" rows="3" placeholder="Describe what this option includes"></textarea>
            <span asp-validation-for="OptDesc" class="text-danger"></span>
        </div>
    </div>

    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="submit" class="btn btn-primary">
            <i class="fas fa-save me-1"></i>Update Option
        </button>
    </div>
</form>

<script>
    $(document).ready(function() {
        // Focus on the first input
        $('#OptName').focus();
        
        // Auto-calculate total discount
        $('#OptCost, #OptDiscountRate').on('input', function() {
            var cost = parseFloat($('#OptCost').val()) || 0;
            var rate = parseFloat($('#OptDiscountRate').val()) || 0;
            var discount = (cost * rate / 100).toFixed(2);
            $('#OptTotalDiscount').val(discount);
        });
        
        // Initialize validation
        $('#serviceOptionForm').validate({
            rules: {
                OptName: {
                    required: true,
                    maxlength: 50
                },
                OptCost: {
                    min: 0
                },
                OptDiscountRate: {
                    min: 0,
                    max: 100
                },
                OptTotalDiscount: {
                    min: 0
                }
            }
        });
    });
</script>
