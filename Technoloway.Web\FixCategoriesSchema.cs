using Microsoft.EntityFrameworkCore;
using Technoloway.Infrastructure.Data;
using Technoloway.Core.Entities;

namespace Technoloway.Web
{
    public class FixCategoriesSchema
    {
        public static async Task ExecuteAsync(ApplicationDbContext context)
        {
            Console.WriteLine("Fixing Categories schema...");

            // First, check if the table exists and has the constraint issue
            var tableInfo = await context.Database.ExecuteSqlRawAsync(@"
                PRAGMA table_info(Categories);
            ");

            // SQLite doesn't support ALTER COLUMN directly, so we need to recreate the table
            // Step 1: Create a new table with the correct schema
            await context.Database.ExecuteSqlRawAsync(@"
                CREATE TABLE Categories_New (
                    Id INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
                    ParentID INTEGER NULL,
                    CategName TEXT NOT NULL,
                    CategDesc TEXT NULL,
                    CreatedAt TEXT NOT NULL,
                    UpdatedAt TEXT NULL,
                    IsDeleted INTEGER NOT NULL,
                    FOREIGN KEY (ParentID) REFERENCES Categories_New (Id) ON DELETE RESTRICT
                );
            ");

            // Step 2: Copy existing data (if any)
            await context.Database.ExecuteSqlRawAsync(@"
                INSERT INTO Categories_New (Id, ParentID, CategName, CategDesc, CreatedAt, UpdatedAt, IsDeleted)
                SELECT Id, 
                       CASE WHEN ParentID = 0 THEN NULL ELSE ParentID END,
                       CategName, 
                       CategDesc, 
                       CreatedAt, 
                       UpdatedAt, 
                       IsDeleted
                FROM Categories;
            ");

            // Step 3: Drop the old table
            await context.Database.ExecuteSqlRawAsync("DROP TABLE Categories;");

            // Step 4: Rename the new table
            await context.Database.ExecuteSqlRawAsync("ALTER TABLE Categories_New RENAME TO Categories;");

            // Step 5: Recreate the index
            await context.Database.ExecuteSqlRawAsync(@"
                CREATE INDEX IX_Categories_ParentID ON Categories (ParentID);
            ");

            // Step 6: Update the Services foreign key constraint
            await context.Database.ExecuteSqlRawAsync(@"
                PRAGMA foreign_keys=off;

                CREATE TABLE Services_New (
                    Id INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
                    CategID INTEGER NOT NULL,
                    Name TEXT NOT NULL,
                    Description TEXT NOT NULL,
                    IconClass TEXT NULL,
                    Price TEXT NOT NULL,
                    ServDiscountRate INTEGER NULL,
                    ServTotalDiscount TEXT NULL,
                    ServManager TEXT NULL,
                    IsActive INTEGER NOT NULL,
                    DisplayOrder INTEGER NOT NULL,
                    CreatedAt TEXT NOT NULL,
                    UpdatedAt TEXT NULL,
                    IsDeleted INTEGER NOT NULL,
                    FOREIGN KEY (CategID) REFERENCES Categories (Id) ON DELETE RESTRICT
                );

                INSERT INTO Services_New (Id, CategID, Name, Description, IconClass, Price, ServDiscountRate, ServTotalDiscount, ServManager, IsActive, DisplayOrder, CreatedAt, UpdatedAt, IsDeleted)
                SELECT Id, CategID, Name, Description, IconClass, Price, ServDiscountRate, ServTotalDiscount, ServManager, IsActive, DisplayOrder, CreatedAt, UpdatedAt, IsDeleted FROM Services;
                DROP TABLE Services;
                ALTER TABLE Services_New RENAME TO Services;
                CREATE INDEX IX_Services_CategID ON Services (CategID);

                PRAGMA foreign_keys=on;
            ");

            Console.WriteLine("Categories schema fixed successfully!");
        }
    }
}
