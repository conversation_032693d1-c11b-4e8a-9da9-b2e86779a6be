using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Technoloway.Core.Interfaces;
using Technoloway.Core.Entities;
using Technoloway.Web.Areas.Client.Models;

namespace Technoloway.Web.Areas.Client.Controllers;

[Area("Client")]
[Authorize(Policy = "RequireClient")]
public class ProfileController : Controller
{
    private readonly IRepository<Core.Entities.Client> _clientRepository;
    private readonly UserManager<IdentityUser> _userManager;
    
    public ProfileController(
        IRepository<Core.Entities.Client> clientRepository,
        UserManager<IdentityUser> userManager)
    {
        _clientRepository = clientRepository;
        _userManager = userManager;
    }
    
    public async Task<IActionResult> Index()
    {
        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return Challenge();
        }
        
        var client = (await _clientRepository.ListAsync(c => c.UserId == user.Id)).FirstOrDefault();
        if (client == null)
        {
            return RedirectToAction("Create");
        }
        
        var model = new ClientProfileViewModel
        {
            Id = client.Id,
            CompanyName = client.CompanyName,
            ContactName = client.ContactName,
            ContactEmail = client.ContactEmail,
            ContactPhone = client.ContactPhone,
            Address = client.Address,
            City = client.City,
            State = client.State,
            ZipCode = client.ZipCode,
            Country = client.Country,
            LogoUrl = client.LogoUrl
        };
        
        return View(model);
    }
    
    public IActionResult Create()
    {
        var model = new ClientProfileViewModel();
        return View(model);
    }
    
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(ClientProfileViewModel model)
    {
        if (!ModelState.IsValid)
        {
            return View(model);
        }
        
        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return Challenge();
        }
        
        // Check if client profile already exists
        var existingClient = (await _clientRepository.ListAsync(c => c.UserId == user.Id)).FirstOrDefault();
        if (existingClient != null)
        {
            TempData["Error"] = "Client profile already exists.";
            return RedirectToAction("Index");
        }
        
        var client = new Core.Entities.Client
        {
            UserId = user.Id,
            CompanyName = model.CompanyName,
            ContactName = model.ContactName,
            ContactEmail = model.ContactEmail,
            ContactPhone = model.ContactPhone ?? string.Empty,
            Address = model.Address ?? string.Empty,
            City = model.City ?? string.Empty,
            State = model.State ?? string.Empty,
            ZipCode = model.ZipCode ?? string.Empty,
            Country = model.Country ?? string.Empty,
            LogoUrl = model.LogoUrl ?? string.Empty
        };
        
        await _clientRepository.AddAsync(client);
        
        TempData["Success"] = "Client profile created successfully!";
        return RedirectToAction("Index", "Home");
    }
    
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Update(ClientProfileViewModel model)
    {
        if (!ModelState.IsValid)
        {
            return View("Index", model);
        }
        
        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return Challenge();
        }
        
        var client = (await _clientRepository.ListAsync(c => c.UserId == user.Id)).FirstOrDefault();
        if (client == null)
        {
            TempData["Error"] = "Client profile not found.";
            return RedirectToAction("Create");
        }
        
        client.CompanyName = model.CompanyName;
        client.ContactName = model.ContactName;
        client.ContactEmail = model.ContactEmail;
        client.ContactPhone = model.ContactPhone ?? string.Empty;
        client.Address = model.Address ?? string.Empty;
        client.City = model.City ?? string.Empty;
        client.State = model.State ?? string.Empty;
        client.ZipCode = model.ZipCode ?? string.Empty;
        client.Country = model.Country ?? string.Empty;
        client.LogoUrl = model.LogoUrl ?? string.Empty;
        
        await _clientRepository.UpdateAsync(client);
        
        TempData["Success"] = "Profile updated successfully!";
        return RedirectToAction("Index");
    }
}
