/* Main Site Styles */

/* Hide top corner buttons */
.top-corner-buttons {
  display: none !important;
}

html {
  font-size: 14px;
  position: relative;
  min-height: 100%;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

body {
  margin-bottom: 0;
}

.form-floating > .form-control-plaintext::placeholder, .form-floating > .form-control::placeholder {
  color: var(--bs-secondary-color);
  text-align: end;
}

.form-floating > .form-control-plaintext:focus::placeholder, .form-floating > .form-control:focus::placeholder {
  text-align: start;
}

/* Hero Section */
.hero-section {
    background-color: #343a40;
    color: white;
    padding: 100px 0;
    position: relative;
}

.hero-section::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('/images/hero-bg.jpg');
    background-size: cover;
    background-position: center;
    opacity: 0.3;
    z-index: 0;
}

.hero-content {
    position: relative;
    z-index: 1;
}

/* Services Section */
.services-section {
    padding: 80px 0;
    background-color: #f8f9fa;
}

.service-card {
    padding: 30px;
    border-radius: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    height: 100%;
}

.service-card:hover {
    transform: translateY(-10px);
}

.service-icon {
    font-size: 3rem;
    margin-bottom: 20px;
    color: #0d6efd;
}

/* Projects Section */
.projects-section {
    padding: 80px 0;
}

.project-card {
    margin-bottom: 30px;
    border-radius: 5px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    position: relative;
}

.project-img {
    width: 100%;
    height: 250px;
    object-fit: cover;
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.project-card:hover .project-overlay {
    opacity: 1;
}

/* Technologies Section */
.technologies-section {
    padding: 80px 0;
    background-color: #f8f9fa;
}

.technology-logo {
    height: 80px;
    object-fit: contain;
    margin: 20px;
    filter: grayscale(100%);
    transition: filter 0.3s ease;
}

.technology-logo:hover {
    filter: grayscale(0%);
}

/* Testimonials Section */
.testimonials-section {
    padding: 80px 0;
    background-color: #343a40;
    color: white;
}

.testimonial-card {
    padding: 30px;
    border-radius: 5px;
    background-color: rgba(255, 255, 255, 0.1);
    margin-bottom: 30px;
}

.testimonial-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 20px;
}

/* Contact Section */
.contact-section {
    padding: 80px 0;
}

.contact-info {
    margin-bottom: 30px;
}

.contact-icon {
    font-size: 2rem;
    color: #0d6efd;
    margin-bottom: 15px;
}

/* Fix FontAwesome icon backgrounds */
.contact-icon i,
footer i.fas,
footer i.fab,
footer i.far,
footer i.fal {
    background: none !important;
    border: none !important;
    box-shadow: none !important;
    padding: 0 !important;
}

/* Social Icons Styling */
.social-icons {
    display: flex;
    flex-wrap: wrap;
    gap: 0;
}

.social-icons a {
    text-decoration: none !important;
    margin-right: 1rem !important;
    display: inline-block;
    position: relative;
}

.social-icons a::before,
.social-icons a::after {
    content: none !important;
    display: none !important;
}

.social-icons a:last-child {
    margin-right: 0 !important;
}

/* Remove any potential separators or dashes */
.social-icons a + a::before {
    content: none !important;
    display: none !important;
}

.social-icons i {
    text-decoration: none !important;
    border: none !important;
    outline: none !important;
}

/* Blog Section */
.blog-section {
    padding: 80px 0;
}

.blog-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-radius: 10px;
    overflow: hidden;
}

.blog-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15) !important;
}

.blog-card .card-img-top {
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.blog-card:hover .card-img-top {
    transform: scale(1.05);
}

.blog-card .card-title a {
    transition: color 0.3s ease;
}

.blog-card .card-title a:hover {
    color: #0d6efd !important;
}

.blog-content {
    line-height: 1.8;
}

.blog-content img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 20px 0;
}

.blog-content h1,
.blog-content h2,
.blog-content h3,
.blog-content h4,
.blog-content h5,
.blog-content h6 {
    margin-top: 30px;
    margin-bottom: 15px;
    font-weight: 600;
}

.blog-content p {
    margin-bottom: 20px;
}

.blog-content blockquote {
    border-left: 4px solid #0d6efd;
    padding-left: 20px;
    margin: 30px 0;
    font-style: italic;
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 5px;
}

.blog-content pre {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 5px;
    overflow-x: auto;
    margin: 20px 0;
}

.blog-content code {
    background-color: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.9em;
}

.blog-share .btn {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.blog-share .btn:hover {
    transform: translateY(-2px);
}

.blog-author-bio {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    padding: 25px;
}

.blog-author-bio img {
    border: 3px solid #fff;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Responsive Design for Blog */
@media (max-width: 991.98px) {
    .blog-section {
        padding: 60px 0;
    }

    .blog-card .card-img-top {
        height: 180px;
    }

    .blog-content {
        font-size: 16px;
    }

    .blog-share {
        text-align: center;
    }

    .blog-share .btn {
        margin: 5px;
    }
}

@media (max-width: 767.98px) {
    .blog-section {
        padding: 40px 0;
    }

    .blog-card .card-img-top {
        height: 160px;
    }

    .blog-content {
        font-size: 15px;
    }

    .blog-content h1 { font-size: 1.8rem; }
    .blog-content h2 { font-size: 1.6rem; }
    .blog-content h3 { font-size: 1.4rem; }
    .blog-content h4 { font-size: 1.2rem; }
    .blog-content h5 { font-size: 1.1rem; }
    .blog-content h6 { font-size: 1rem; }

    .blog-author-bio {
        text-align: center;
        padding: 20px;
    }

    .blog-author-bio .d-flex {
        flex-direction: column !important;
        align-items: center !important;
    }

    .blog-author-bio img {
        margin-bottom: 15px !important;
        margin-right: 0 !important;
    }

    .blog-share .d-flex {
        flex-wrap: wrap;
        justify-content: center;
        gap: 10px;
    }

    .blog-share .btn {
        width: 40px;
        height: 40px;
        margin: 0;
    }
}

@media (max-width: 575.98px) {
    .blog-section {
        padding: 30px 0;
    }

    .blog-card .card-img-top {
        height: 140px;
    }

    .blog-content {
        font-size: 14px;
    }

    .blog-content h1 { font-size: 1.6rem; }
    .blog-content h2 { font-size: 1.4rem; }
    .blog-content h3 { font-size: 1.3rem; }
    .blog-content h4 { font-size: 1.2rem; }
    .blog-content h5 { font-size: 1.1rem; }
    .blog-content h6 { font-size: 1rem; }

    .blog-content blockquote {
        padding: 15px;
        margin: 20px 0;
    }

    .blog-content pre {
        padding: 15px;
        font-size: 12px;
    }

    .blog-author-bio {
        padding: 15px;
    }

    .blog-share h4 {
        font-size: 1.2rem;
    }

    .blog-share .btn {
        width: 35px;
        height: 35px;
        font-size: 14px;
    }

    /* Stack blog cards in single column on mobile */
    .blog-section .col-md-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    /* Improve pagination on mobile */
    .pagination {
        flex-wrap: wrap;
        justify-content: center;
    }

    .pagination .page-item {
        margin: 2px;
    }

    .pagination .page-link {
        padding: 8px 12px;
        font-size: 14px;
    }

    /* Improve sidebar widgets on mobile */
    .blog-section .col-lg-4 .card {
        margin-bottom: 20px;
    }

    /* Better spacing for mobile */
    .blog-section .container {
        padding-left: 15px;
        padding-right: 15px;
    }

    /* Improve breadcrumb on mobile */
    .breadcrumb {
        font-size: 14px;
        flex-wrap: wrap;
    }

    .breadcrumb-item {
        margin-bottom: 5px;
    }
}

/* Additional responsive improvements */
@media (max-width: 480px) {
    .blog-section {
        padding: 20px 0;
    }

    .blog-card .card-img-top {
        height: 120px;
    }

    .blog-content {
        font-size: 13px;
    }

    .blog-content h1 { font-size: 1.4rem; }
    .blog-content h2 { font-size: 1.3rem; }
    .blog-content h3 { font-size: 1.2rem; }
    .blog-content h4 { font-size: 1.1rem; }
    .blog-content h5 { font-size: 1rem; }
    .blog-content h6 { font-size: 0.9rem; }

    .blog-share .btn {
        width: 30px;
        height: 30px;
        font-size: 12px;
    }

    .blog-author-bio {
        padding: 10px;
    }

    .blog-author-bio img {
        width: 60px;
        height: 60px;
    }

    /* Ultra-mobile optimizations */
    .container {
        padding-left: 10px;
        padding-right: 10px;
    }

    .card-body {
        padding: 15px;
    }

    .card-footer {
        padding: 10px 15px;
    }

    .btn-sm {
        padding: 4px 8px;
        font-size: 12px;
    }

    .badge {
        font-size: 10px;
        padding: 3px 6px;
    }
}