@model Technoloway.Web.Areas.Admin.Models.TechnologyViewModel

@{
    ViewData["Title"] = "Edit Technology";
    Layout = "_AdminLayout";
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Edit Technology</h1>
    <div>
        <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-sm btn-info shadow-sm">
            <i class="fas fa-eye fa-sm text-white-50"></i> View Details
        </a>
        <a asp-action="Index" class="btn btn-sm btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to List
        </a>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Technology Details</h6>
    </div>
    <div class="card-body">
        <form asp-action="Edit" enctype="multipart/form-data">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="Id" />
            <input type="hidden" asp-for="CreatedAt" />

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <label asp-for="Name" class="control-label"></label>
                        <input asp-for="Name" class="form-control" placeholder="e.g., React, Node.js, Python" />
                        <span asp-validation-for="Name" class="text-danger"></span>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <label asp-for="DisplayOrder" class="control-label"></label>
                        <input asp-for="DisplayOrder" class="form-control" type="number" min="0" />
                        <span asp-validation-for="DisplayOrder" class="text-danger"></span>
                        <small class="form-text text-muted">Lower numbers appear first</small>
                    </div>
                </div>
            </div>

            <div class="form-group mb-3">
                <label asp-for="IconFile" class="control-label">Icon</label>
                <input asp-for="IconFile" class="form-control" type="file" accept="image/*" />
                <span asp-validation-for="IconFile" class="text-danger"></span>
                <small class="form-text text-muted">
                    Upload a new icon to replace the current one. Supported formats: JPG, PNG, GIF, WebP. Maximum size: 5MB. Recommended size: 80x80px or larger.
                </small>

                @if (!string.IsNullOrEmpty(Model.IconUrl))
                {
                    <div class="mt-2">
                        <small class="text-muted">Current icon:</small><br>
                        <img src="@Model.IconUrl" alt="@Model.Name" style="height: 60px; width: 60px; object-fit: contain;" />
                    </div>
                }

                <div id="icon-preview" class="mt-2" style="display: none;">
                    <small class="text-muted">New icon preview:</small><br>
                    <img id="preview-img" style="height: 60px; width: 60px; object-fit: contain;" />
                </div>

                <input type="hidden" asp-for="IconUrl" />
            </div>

            <div class="form-group mb-3">
                <label asp-for="Description" class="control-label"></label>
                <textarea asp-for="Description" class="form-control" rows="4" placeholder="Brief description of the technology and how we use it..."></textarea>
                <span asp-validation-for="Description" class="text-danger"></span>
            </div>

            <div class="form-group mt-4">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Update Technology
                </button>
                <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-info">View Details</a>
                <a asp-action="Index" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script>
        // Preview icon when file is selected
        $('#IconFile').on('change', function() {
            var file = this.files[0];
            var preview = $('#icon-preview');
            var previewImg = $('#preview-img');

            if (file) {
                // Validate file type
                var validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
                if (!validTypes.includes(file.type)) {
                    alert('Please select a valid image file (JPG, PNG, GIF, WebP)');
                    $(this).val('');
                    preview.hide();
                    return;
                }

                // Validate file size (5MB)
                if (file.size > 5 * 1024 * 1024) {
                    alert('File size must be less than 5MB');
                    $(this).val('');
                    preview.hide();
                    return;
                }

                // Show preview
                var reader = new FileReader();
                reader.onload = function(e) {
                    previewImg.attr('src', e.target.result);
                    preview.show();
                };
                reader.readAsDataURL(file);
            } else {
                preview.hide();
            }
        });

        // Drag and drop functionality
        var dropArea = $('#IconFile').parent();

        dropArea.on('dragover', function(e) {
            e.preventDefault();
            $(this).addClass('border-primary');
        });

        dropArea.on('dragleave', function(e) {
            e.preventDefault();
            $(this).removeClass('border-primary');
        });

        dropArea.on('drop', function(e) {
            e.preventDefault();
            $(this).removeClass('border-primary');

            var files = e.originalEvent.dataTransfer.files;
            if (files.length > 0) {
                $('#IconFile')[0].files = files;
                $('#IconFile').trigger('change');
            }
        });
    </script>
}
