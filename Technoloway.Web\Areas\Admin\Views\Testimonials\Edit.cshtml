@model Technoloway.Web.Areas.Admin.Models.TestimonialViewModel

@{
    ViewData["Title"] = "Edit Testimonial";
    Layout = "_AdminLayout";
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Edit Testimonial</h1>
    <div>
        <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-sm btn-info shadow-sm">
            <i class="fas fa-eye fa-sm text-white-50"></i> View Details
        </a>
        <a asp-action="Index" class="btn btn-sm btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to List
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Edit Testimonial Information</h6>
            </div>
            <div class="card-body">
                <form asp-action="Edit" method="post" enctype="multipart/form-data">
                    <input asp-for="Id" type="hidden" />
                    <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="ClientName" class="form-label">Client Name *</label>
                                <input asp-for="ClientName" class="form-control" placeholder="Enter client name" />
                                <span asp-validation-for="ClientName" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="ClientTitle" class="form-label">Client Title *</label>
                                <input asp-for="ClientTitle" class="form-control" placeholder="e.g., CEO, Marketing Director" />
                                <span asp-validation-for="ClientTitle" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="ClientCompany" class="form-label">Company *</label>
                                <input asp-for="ClientCompany" class="form-control" placeholder="Enter company name" />
                                <span asp-validation-for="ClientCompany" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="PhotoFile" class="form-label">Client Photo</label>
                                <input asp-for="PhotoFile" class="form-control" type="file" accept="image/*" />
                                <span asp-validation-for="PhotoFile" class="text-danger"></span>
                                <small class="form-text text-muted">
                                    Optional: Upload new client photo. Supported formats: JPG, PNG, GIF, WebP. Maximum size: 5MB. Recommended size: 200x200px.
                                </small>

                                @if (!string.IsNullOrEmpty(Model.ClientPhotoUrl) && Model.ClientPhotoUrl.StartsWith("/images/"))
                                {
                                    <div class="mt-2">
                                        <small class="text-muted">Current photo:</small>
                                        <div class="d-flex align-items-center mt-1">
                                            <img src="@Model.ClientPhotoUrl" alt="Current Photo" style="height: 60px; width: 60px; object-fit: cover; border-radius: 50%;" />
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="Content" class="form-label">Testimonial Content *</label>
                        <textarea asp-for="Content" class="form-control" rows="4"
                                  placeholder="Enter the testimonial content..."></textarea>
                        <span asp-validation-for="Content" class="text-danger"></span>
                        <small class="form-text text-muted">10-1000 characters</small>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label asp-for="Rating" class="form-label">Rating *</label>
                                <select asp-for="Rating" class="form-control">
                                    <option value="">Select Rating</option>
                                    <option value="1">⭐ 1 Star</option>
                                    <option value="2">⭐⭐ 2 Stars</option>
                                    <option value="3">⭐⭐⭐ 3 Stars</option>
                                    <option value="4">⭐⭐⭐⭐ 4 Stars</option>
                                    <option value="5">⭐⭐⭐⭐⭐ 5 Stars</option>
                                </select>
                                <span asp-validation-for="Rating" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label asp-for="DisplayOrder" class="form-label">Display Order</label>
                                <input asp-for="DisplayOrder" class="form-control" type="number" min="0"
                                       placeholder="0" />
                                <span asp-validation-for="DisplayOrder" class="text-danger"></span>
                                <small class="form-text text-muted">Lower numbers appear first</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label class="form-label">Status</label>
                                <div class="form-check">
                                    <input asp-for="IsActive" class="form-check-input" type="checkbox" />
                                    <label asp-for="IsActive" class="form-check-label">
                                        Active (visible on website)
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Update Testimonial
                        </button>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Preview</h6>
            </div>
            <div class="card-body">
                <div class="testimonial-preview text-center">
                    <div class="mb-3">
                        <img id="preview-photo" src="@(string.IsNullOrEmpty(Model.ClientPhotoUrl) ? "/images/avatar-placeholder.jpg" : Model.ClientPhotoUrl)"
                             alt="Client Photo" class="rounded-circle" style="width: 80px; height: 80px; object-fit: cover;" />
                    </div>
                    <div id="preview-rating" class="mb-3">
                        @for (int i = 0; i < Model.Rating; i++)
                        {
                            <i class="fas fa-star text-warning"></i>
                        }
                        @for (int i = Model.Rating; i < 5; i++)
                        {
                            <i class="far fa-star text-muted"></i>
                        }
                    </div>
                    <p id="preview-content" class="mb-3">
                        @Model.Content
                    </p>
                    <h6 id="preview-name" class="mb-1">@Model.ClientName</h6>
                    <p id="preview-title" class="text-muted small">@Model.ClientTitle, @Model.ClientCompany</p>
                </div>
            </div>
        </div>

        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Testimonial Info</h6>
            </div>
            <div class="card-body">
                <p><strong>Created:</strong> @Model.CreatedAt.ToString("d")</p>
                <p><strong>Last Updated:</strong> @Model.UpdatedAt?.ToString("d")</p>
                <p><strong>Status:</strong>
                    <span class="badge @(Model.IsActive ? "bg-success" : "bg-secondary")">
                        @(Model.IsActive ? "Active" : "Inactive")
                    </span>
                </p>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script>
        $(document).ready(function() {
            // Update preview when form fields change
            $('#ClientName').on('input', function() {
                $('#preview-name').text($(this).val() || 'Client Name');
            });

            $('#ClientTitle, #ClientCompany').on('input', function() {
                var title = $('#ClientTitle').val() || 'Title';
                var company = $('#ClientCompany').val() || 'Company';
                $('#preview-title').text(title + ', ' + company);
            });

            $('#Content').on('input', function() {
                $('#preview-content').text($(this).val() || 'Testimonial content will appear here...');
            });

            $('#Rating').on('change', function() {
                var rating = parseInt($(this).val());
                var stars = '';
                if (rating > 0) {
                    for (var i = 0; i < rating; i++) {
                        stars += '<i class="fas fa-star text-warning"></i>';
                    }
                    for (var i = rating; i < 5; i++) {
                        stars += '<i class="far fa-star text-muted"></i>';
                    }
                    $('#preview-rating').html(stars);
                } else {
                    $('#preview-rating').html('<span class="text-muted">No rating selected</span>');
                }
            });

            $('#PhotoFile').on('change', function() {
                var file = this.files[0];
                if (file) {
                    var reader = new FileReader();
                    reader.onload = function(e) {
                        $('#preview-photo').attr('src', e.target.result);
                    };
                    reader.readAsDataURL(file);
                }
                // Note: Don't reset to placeholder if no file selected, keep current image
            });
        });
    </script>
}
