using Technoloway.Core.Common;
using System.ComponentModel.DataAnnotations;

namespace Technoloway.Core.Entities;

public class Contract : BaseEntity
{
    [Required]
    public int ContID { get; set; }
    
    [Required]
    [StringLength(40)]
    public string ContName { get; set; } = string.Empty;
    
    [Required]
    public int ProjID { get; set; }
    
    [Required]
    public int ClientID { get; set; }
    
    [Required]
    public int OrderID { get; set; }
    
    [StringLength(40)]
    public string? ContManager { get; set; }
    
    [StringLength(10)]
    public string? ContServType { get; set; }
    
    [StringLength(10)]
    public string? ContLang { get; set; }
    
    public string? AgreementDesc { get; set; }
    
    public decimal? ContValue { get; set; }
    
    [StringLength(10)]
    public string? ContValueCurr { get; set; }
    
    [StringLength(10)]
    public string? BillingType { get; set; }
    
    public DateTime? NextBillDate { get; set; }
    
    public DateTime? ContIssueDate { get; set; }
    
    [StringLength(10)]
    public string? ContSignMethod { get; set; }
    
    public DateTime? ContSignedDate { get; set; }
    
    public DateTime? ContExecutedDate { get; set; }
    
    public DateTime? ContExpiryDate { get; set; }
    
    [StringLength(10)]
    public string? ContStatus { get; set; }
    
    public DateTime? LastUpdateDate { get; set; }
    
    [StringLength(40)]
    public string? LastUpdateUser { get; set; }
    
    public byte[]? ContFile { get; set; }
    
    public DateTime? FileUploadDate { get; set; }
    
    public string? Comments { get; set; }
    
    public string? Notes { get; set; }
    
    // Navigation properties
    public Project Project { get; set; } = null!;
    public Client Client { get; set; } = null!;
    public Order Order { get; set; } = null!;
    public ICollection<Invoice> Invoices { get; set; } = new List<Invoice>();
}
