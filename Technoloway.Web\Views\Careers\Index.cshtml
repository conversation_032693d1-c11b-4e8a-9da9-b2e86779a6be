@model IEnumerable<Technoloway.Core.Entities.JobListing>

@{
    ViewData["Title"] = "Careers";
    ViewData["MetaDescription"] = "Explore career opportunities at Technoloway. Join our team of talented professionals and work on exciting projects.";
    ViewData["MetaKeywords"] = "careers, jobs, employment, software development, tech jobs, IT careers";
}

@section Styles {
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="~/css/modern-homepage.css" asp-append-version="true" />
}

<!-- Scroll Progress Indicator -->
<div class="scroll-indicator">
    <div class="scroll-progress"></div>
</div>

<!-- Dynamic Hero Section -->
<partial name="_HeroSection" />

<!-- Fallback Header if no hero section -->
@if (ViewBag.HeroSection == null)
{
    <div class="modern-page-header fixed-height-hero">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-12 text-center">
                    <div class="page-header-content">
                        <div class="page-breadcrumb">
                            <a href="/" class="breadcrumb-link">
                                <i class="fas fa-home"></i>
                                <span>Home</span>
                            </a>
                            <i class="fas fa-chevron-right breadcrumb-separator"></i>
                            <span class="breadcrumb-current">Careers</span>
                        </div>
                        <h1 class="page-title">
                            <span class="title-highlight">Join Our</span> Team
                        </h1>
                        <p class="page-subtitle">
                            Explore career opportunities at Technoloway. Join our team of talented professionals and work on exciting projects that make a difference.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
}

<!-- Careers Section - Modern Design -->
<section class="modern-section">
    <div class="container">
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <div class="section-header animate-on-scroll">
                    <h2 class="section-title">
                        <span class="title-highlight">Current</span> Openings
                    </h2>
                    <p class="section-subtitle">
                        Join our team of talented professionals and work on exciting projects that make a difference.
                    </p>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-8 mb-4 mb-lg-0">
                @if (Model != null && Model.Any())
                {
                    int jobIndex = 0;
                    foreach (var job in Model)
                    {
                        <div class="modern-card job-card animate-on-scroll mb-4" data-delay="@((jobIndex % 3 + 1) * 100)">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-3">
                                    <h3 class="card-title">@job.Title</h3>
                                    <div class="d-flex gap-2">
                                        <a asp-action="Details" asp-route-id="@job.Id" class="modern-btn secondary small">
                                            <span class="btn-text">Details</span>
                                            <span class="btn-icon">
                                                <i class="fas fa-eye"></i>
                                            </span>
                                        </a>
                                        <a asp-action="Apply" asp-route-id="@job.Id" class="modern-btn primary small">
                                            <span class="btn-text">Apply</span>
                                            <span class="btn-icon">
                                                <i class="fas fa-paper-plane"></i>
                                            </span>
                                        </a>
                                    </div>
                                </div>

                                <div class="job-badges mb-4">
                                    <span class="job-badge employment-type">
                                        @job.EmploymentType
                                    </span>
                                    <span class="job-badge location">
                                        <i class="fas fa-map-marker-alt me-1"></i>@job.Location
                                    </span>
                                    @if (job.IsRemote)
                                    {
                                        <span class="job-badge remote">
                                            <i class="fas fa-laptop-house me-1"></i>Remote
                                        </span>
                                    }
                                    @if (job.SalaryMin.HasValue && job.SalaryMax.HasValue)
                                    {
                                        <span class="job-badge salary">
                                            <i class="fas fa-money-bill-wave me-1"></i>
                                            @job.SalaryMin.Value.ToString("C0") - @job.SalaryMax.Value.ToString("C0") @job.SalaryCurrency
                                        </span>
                                    }
                                </div>

                                <p class="card-subtitle">
                                    @job.Description.Substring(0, Math.Min(job.Description.Length, 200))...
                                </p>
                            </div>
                        </div>
                        jobIndex++;
                    }
                }
                else
                {
                    <div class="modern-card empty-state-card animate-on-scroll">
                        <div class="card-body text-center">
                            <div class="empty-state-icon">
                                <i class="fas fa-briefcase"></i>
                            </div>
                            <h3 class="card-title">No Open Positions</h3>
                            <p class="card-subtitle">We don't have any open positions at the moment.</p>
                            <p class="card-subtitle mb-4">Please check back later or send your resume to us.</p>
                            <a href="mailto:<EMAIL>" class="modern-btn primary">
                                <span class="btn-text">Send Your Resume</span>
                                <span class="btn-icon">
                                    <i class="fas fa-paper-plane"></i>
                                </span>
                            </a>
                        </div>
                    </div>
                }
            </div>

            <!-- Sidebar - Modern Design -->
            <div class="col-lg-4">
                <div class="modern-card benefits-card animate-on-scroll mb-4" data-delay="100">
                    <div class="card-header">
                        <h3 class="card-title">
                            <span class="title-highlight">Why Join</span> Us?
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="benefits-list">
                            <div class="benefit-item">
                                <div class="benefit-icon">
                                    <i class="fas fa-laptop-code"></i>
                                </div>
                                <div class="benefit-content">
                                    <h5 class="benefit-title">Cutting-Edge Technology</h5>
                                    <p class="benefit-description">Work with the latest technologies and tools in the industry.</p>
                                </div>
                            </div>
                            <div class="benefit-item">
                                <div class="benefit-icon">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="benefit-content">
                                    <h5 class="benefit-title">Growth Opportunities</h5>
                                    <p class="benefit-description">Continuous learning and career advancement paths.</p>
                                </div>
                            </div>
                            <div class="benefit-item">
                                <div class="benefit-icon">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="benefit-content">
                                    <h5 class="benefit-title">Collaborative Culture</h5>
                                    <p class="benefit-description">Work with talented professionals in a supportive environment.</p>
                                </div>
                            </div>
                            <div class="benefit-item">
                                <div class="benefit-icon">
                                    <i class="fas fa-medal"></i>
                                </div>
                                <div class="benefit-content">
                                    <h5 class="benefit-title">Competitive Benefits</h5>
                                    <p class="benefit-description">Attractive compensation, health benefits, and work-life balance.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modern-card contact-hr-card animate-on-scroll" data-delay="200">
                    <div class="card-header">
                        <h3 class="card-title">
                            <span class="title-highlight">Contact</span> HR
                        </h3>
                        <p class="card-subtitle">Have questions about our openings or the application process?</p>
                    </div>
                    <div class="card-body">
                        <a href="mailto:<EMAIL>" class="modern-btn primary w-100">
                            <span class="btn-text">Email HR Department</span>
                            <span class="btn-icon">
                                <i class="fas fa-envelope"></i>
                            </span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section - Modern Design -->
<section class="modern-section">
    <div class="container">
        <div class="modern-card cta-card animate-on-scroll">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-lg-8 mb-4 mb-lg-0 text-center text-lg-start">
                        <h2 class="section-title mb-3">
                            <span class="title-highlight">Don't See a Position</span> That Fits?
                        </h2>
                        <p class="section-subtitle mb-0">
                            We're always looking for talented individuals to join our team. Send us your resume and we'll keep it on file for future opportunities.
                        </p>
                    </div>
                    <div class="col-lg-4 text-center text-lg-end">
                        <div class="d-flex gap-3 justify-content-center justify-content-lg-end flex-wrap">
                            <a href="mailto:<EMAIL>" class="modern-btn primary">
                                <span class="btn-text">Send Your Resume</span>
                                <span class="btn-icon">
                                    <i class="fas fa-paper-plane"></i>
                                </span>
                            </a>
                            <a href="#careers" class="modern-btn secondary">
                                <span class="btn-text">View Openings</span>
                                <span class="btn-icon">
                                    <i class="fas fa-arrow-up"></i>
                                </span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Scroll progress indicator
            const scrollProgress = document.querySelector('.scroll-progress');

            function updateScrollProgress() {
                const scrollTop = window.pageYOffset;
                const docHeight = document.body.scrollHeight - window.innerHeight;
                const scrollPercent = (scrollTop / docHeight) * 100;
                scrollProgress.style.width = scrollPercent + '%';
            }

            window.addEventListener('scroll', updateScrollProgress);

            // Floating elements animation
            const floatingElements = document.querySelectorAll('.floating-element');

            floatingElements.forEach((element, index) => {
                element.style.animationDelay = `${index * 0.5}s`;
            });

            // Job card hover effects
            const jobCards = document.querySelectorAll('.job-card');

            jobCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // Benefit item hover effects
            const benefitItems = document.querySelectorAll('.benefit-item');

            benefitItems.forEach(item => {
                item.addEventListener('mouseenter', function() {
                    const icon = this.querySelector('.benefit-icon');
                    icon.style.transform = 'scale(1.1) rotate(5deg)';
                });

                item.addEventListener('mouseleave', function() {
                    const icon = this.querySelector('.benefit-icon');
                    icon.style.transform = 'scale(1) rotate(0deg)';
                });
            });
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const delay = entry.target.dataset.delay || 0;
                    setTimeout(() => {
                        entry.target.classList.add('animate-in');
                    }, delay);
                }
            });
        }, observerOptions);

        // Observe all elements with animation classes
        document.querySelectorAll('.animate-on-scroll').forEach(el => {
            observer.observe(el);
        });
    </script>
}
