@model Technoloway.Web.Areas.Admin.Models.ServiceOptionFeatureViewModel

<div class="modal-header">
    <h5 class="modal-title">
        <i class="fas fa-star me-2"></i>Add Feature
    </h5>
    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
</div>

<form id="serviceOptionFeatureForm" asp-action="CreateServiceOptionFeature" method="post">
    @Html.AntiForgeryToken()
    <input asp-for="OptID" type="hidden" />
    <input asp-for="ServiceOptionId" type="hidden" />
    
    <div class="modal-body">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            Adding feature for: <strong>@ViewBag.ServiceName</strong> → <strong>@ViewBag.OptionName</strong>
        </div>

        <div class="mb-3">
            <label asp-for="FeatName" class="form-label">Feature Name</label>
            <input asp-for="FeatName" class="form-control" placeholder="e.g., User Authentication, Payment Gateway" />
            <span asp-validation-for="FeatName" class="text-danger"></span>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label asp-for="FeatCost" class="form-label">Cost</label>
                    <div class="input-group">
                        <span class="input-group-text">$</span>
                        <input asp-for="FeatCost" class="form-control" placeholder="0.00" />
                    </div>
                    <span asp-validation-for="FeatCost" class="text-danger"></span>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label asp-for="FeatDiscountRate" class="form-label">Discount Rate (%)</label>
                    <input asp-for="FeatDiscountRate" class="form-control" placeholder="0" />
                    <span asp-validation-for="FeatDiscountRate" class="text-danger"></span>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label asp-for="FeatTotalDiscount" class="form-label">Total Discount</label>
                    <div class="input-group">
                        <span class="input-group-text">$</span>
                        <input asp-for="FeatTotalDiscount" class="form-control" placeholder="0.00" />
                    </div>
                    <span asp-validation-for="FeatTotalDiscount" class="text-danger"></span>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label asp-for="FeatAvailability" class="form-label">Availability</label>
                    <select asp-for="FeatAvailability" class="form-select">
                        <option value="Included">Included</option>
                        <option value="Optional">Optional</option>
                        <option value="Premium">Premium</option>
                        <option value="Unavailable">Unavailable</option>
                    </select>
                    <span asp-validation-for="FeatAvailability" class="text-danger"></span>
                </div>
            </div>
        </div>

        <div class="mb-3">
            <label asp-for="FeatDesc" class="form-label">Description</label>
            <textarea asp-for="FeatDesc" class="form-control" rows="3" placeholder="Describe what this feature provides"></textarea>
            <span asp-validation-for="FeatDesc" class="text-danger"></span>
        </div>
    </div>

    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="submit" class="btn btn-primary">
            <i class="fas fa-save me-1"></i>Create Feature
        </button>
    </div>
</form>

<script>
    $(document).ready(function() {
        // Focus on the first input
        $('#FeatName').focus();
        
        // Auto-calculate total discount
        $('#FeatCost, #FeatDiscountRate').on('input', function() {
            var cost = parseFloat($('#FeatCost').val()) || 0;
            var rate = parseFloat($('#FeatDiscountRate').val()) || 0;
            var discount = (cost * rate / 100).toFixed(2);
            $('#FeatTotalDiscount').val(discount);
        });
        
        // Initialize validation
        $('#serviceOptionFeatureForm').validate({
            rules: {
                FeatName: {
                    required: true,
                    maxlength: 50
                },
                FeatCost: {
                    min: 0
                },
                FeatDiscountRate: {
                    min: 0,
                    max: 100
                },
                FeatTotalDiscount: {
                    min: 0
                }
            }
        });
    });
</script>
