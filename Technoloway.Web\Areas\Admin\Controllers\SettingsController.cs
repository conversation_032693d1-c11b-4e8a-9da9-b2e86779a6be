using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Technoloway.Core.Entities;
using Technoloway.Core.Interfaces;
using Technoloway.Web.Areas.Admin.Models;
using Technoloway.Web.Services;

namespace Technoloway.Web.Areas.Admin.Controllers;

[Area("Admin")]
[Authorize(Policy = "RequireAdminRole")]
[Route("Admin/[controller]/[action]")]
public class SettingsController : Controller
{
    private readonly IRepository<SiteSetting> _siteSettingRepository;
    private readonly IFileUploadService _fileUploadService;
    private readonly IWebHostEnvironment _webHostEnvironment;

    public SettingsController(IRepository<SiteSetting> siteSettingRepository, IFileUploadService fileUploadService, IWebHostEnvironment webHostEnvironment)
    {
        _siteSettingRepository = siteSettingRepository;
        _fileUploadService = fileUploadService;
        _webHostEnvironment = webHostEnvironment;
    }

    public async Task<IActionResult> Index()
    {
        var settings = await _siteSettingRepository.GetAll()
            .Where(s => !s.IsDeleted)
            .OrderBy(s => s.Group)
            .ThenBy(s => s.Key)
            .ToListAsync();

        // Group settings by category
        var groupedSettings = settings.GroupBy(s => s.Group).ToList();

        return View(groupedSettings);
    }

    public async Task<IActionResult> Edit(int id)
    {
        var setting = await _siteSettingRepository.GetByIdAsync(id);
        if (setting == null)
        {
            return NotFound();
        }

        var viewModel = SiteSettingViewModel.FromEntity(setting);
        return View(viewModel);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Edit(int id, SiteSettingViewModel viewModel)
    {
        if (id != viewModel.Id)
        {
            return NotFound();
        }

        try
        {
            var existingSetting = await _siteSettingRepository.GetByIdAsync(id);
            if (existingSetting == null)
            {
                return NotFound();
            }

            // Handle file upload if provided
            if (viewModel.IconFile != null && viewModel.IconFile.Length > 0)
            {
                if (!_fileUploadService.IsValidImageFile(viewModel.IconFile))
                {
                    ModelState.AddModelError("IconFile", "Please upload a valid image file (JPG, PNG, GIF, WebP). Maximum size: 5MB.");
                }
                else
                {
                    // Get the old file name for deletion
                    string? oldFileName = null;
                    if (!string.IsNullOrEmpty(existingSetting.Icon) && existingSetting.Icon.StartsWith("/images/"))
                    {
                        oldFileName = Path.GetFileName(existingSetting.Icon);
                    }

                    viewModel.Icon = await _fileUploadService.UploadImageAsync(viewModel.IconFile, "settings", oldFileName);
                }
            }
            else
            {
                // Keep existing icon if no new file uploaded
                viewModel.Icon = existingSetting.Icon;
            }

            if (ModelState.IsValid)
            {
                viewModel.UpdateEntity(existingSetting);
                existingSetting.UpdatedAt = DateTime.UtcNow;

                await _siteSettingRepository.UpdateAsync(existingSetting);
                TempData["SuccessMessage"] = "Setting updated successfully.";
                return RedirectToAction(nameof(Index));
            }
        }
        catch (DbUpdateConcurrencyException)
        {
            if (!await SettingExists(viewModel.Id))
            {
                return NotFound();
            }
            else
            {
                throw;
            }
        }
        catch (Exception ex)
        {
            ModelState.AddModelError("", $"Error updating setting: {ex.Message}");
        }

        return View(viewModel);
    }

    public IActionResult Create()
    {
        Console.WriteLine("=== CREATE GET ACTION CALLED ===");
        var viewModel = new SiteSettingViewModel
        {
            Group = "General",
            IsPublic = true,
            Icon = string.Empty // Initialize Icon field to prevent validation errors
        };
        Console.WriteLine($"Returning Create view with default Group: {viewModel.Group}");
        return View(viewModel);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(SiteSettingViewModel viewModel)
    {
        try
        {
            // Debug: Log basic form data
            Console.WriteLine($"=== CREATE SETTING DEBUG ===");
            Console.WriteLine($"Key: '{viewModel.Key}'");
            Console.WriteLine($"Value: '{viewModel.Value}'");
            Console.WriteLine($"Group: '{viewModel.Group}'");
            Console.WriteLine($"Description: '{viewModel.Description}'");
            Console.WriteLine($"IsPublic: {viewModel.IsPublic}");
            Console.WriteLine($"IconFile: {viewModel.IconFile?.FileName ?? "null"}");
            Console.WriteLine($"Icon: '{viewModel.Icon}'");

            // Check for duplicate key
            var existingSetting = await _siteSettingRepository.GetAll()
                .Where(s => s.Key == viewModel.Key && !s.IsDeleted)
                .FirstOrDefaultAsync();
            if (existingSetting != null)
            {
                Console.WriteLine($"Duplicate key found: {viewModel.Key}");
                ModelState.AddModelError("Key", "A setting with this key already exists.");
                return View(viewModel);
            }

            Console.WriteLine("No duplicate key found, proceeding...");

            // Check ModelState before processing
            Console.WriteLine($"ModelState.IsValid: {ModelState.IsValid}");
            if (!ModelState.IsValid)
            {
                Console.WriteLine("ModelState errors:");
                foreach (var error in ModelState)
                {
                    if (error.Value.Errors.Count > 0)
                    {
                        Console.WriteLine($"  {error.Key}: {string.Join(", ", error.Value.Errors.Select(e => e.ErrorMessage))}");
                    }
                }
                return View(viewModel);
            }

            // Handle file upload if provided
            if (viewModel.IconFile != null && viewModel.IconFile.Length > 0)
            {
                Console.WriteLine($"Processing file upload: {viewModel.IconFile.FileName}");

                if (!_fileUploadService.IsValidImageFile(viewModel.IconFile))
                {
                    Console.WriteLine("File validation failed");
                    ModelState.AddModelError("IconFile", "Please upload a valid image file (JPG, PNG, GIF, WebP). Maximum size: 5MB.");
                    return View(viewModel);
                }
                else
                {
                    Console.WriteLine("File validation passed, uploading...");
                    viewModel.Icon = await _fileUploadService.UploadImageAsync(viewModel.IconFile, "settings");
                    Console.WriteLine($"File uploaded successfully: '{viewModel.Icon}'");
                }
            }
            else
            {
                // Ensure Icon is empty string if no file uploaded (database requires non-null)
                viewModel.Icon = string.Empty;
                Console.WriteLine("No icon file uploaded, setting Icon to empty string");
            }

            // Create the entity
            var siteSetting = viewModel.ToEntity();
            siteSetting.CreatedAt = DateTime.UtcNow;
            siteSetting.UpdatedAt = DateTime.UtcNow;

            Console.WriteLine($"Creating setting entity:");
            Console.WriteLine($"  Key: '{siteSetting.Key}'");
            Console.WriteLine($"  Value: '{siteSetting.Value}'");
            Console.WriteLine($"  Group: '{siteSetting.Group}'");
            Console.WriteLine($"  Icon: '{siteSetting.Icon}'");

            // Save to database
            await _siteSettingRepository.AddAsync(siteSetting);
            Console.WriteLine("Setting saved to database successfully");

            TempData["SuccessMessage"] = "Setting created successfully.";
            return RedirectToAction(nameof(Index));
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Exception in Create: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
            ModelState.AddModelError("", $"Error creating setting: {ex.Message}");
        }

        return View(viewModel);
    }

    public async Task<IActionResult> Delete(int id)
    {
        var setting = await _siteSettingRepository.GetByIdAsync(id);
        if (setting == null)
        {
            return NotFound();
        }

        return View(setting);
    }

    [HttpPost, ActionName("Delete")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DeleteConfirmed(int id)
    {
        var setting = await _siteSettingRepository.GetByIdAsync(id);
        if (setting == null)
        {
            return NotFound();
        }

        // Soft delete
        await _siteSettingRepository.DeleteAsync(setting);
        TempData["SuccessMessage"] = "Setting deleted successfully.";

        return RedirectToAction(nameof(Index));
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> UpdateMultiple(Dictionary<int, string> settingValues, Dictionary<int, string> settingIcons)
    {
        try
        {
            // Debug: Log what we received
            Console.WriteLine($"Received settingValues: {settingValues?.Count ?? 0} items");
            Console.WriteLine($"Received settingIcons: {settingIcons?.Count ?? 0} items");

            if (settingIcons != null)
            {
                foreach (var kvp in settingIcons)
                {
                    Console.WriteLine($"Icon {kvp.Key}: '{kvp.Value}'");
                }
            }

            // Get all unique setting IDs from both dictionaries
            var allSettingIds = settingValues.Keys.Union(settingIcons?.Keys ?? Enumerable.Empty<int>()).Distinct();

            foreach (var settingId in allSettingIds)
            {
                var setting = await _siteSettingRepository.GetByIdAsync(settingId);
                if (setting != null)
                {
                    Console.WriteLine($"Processing setting {settingId}: {setting.Key}");

                    // Update value if provided
                    if (settingValues.ContainsKey(settingId))
                    {
                        setting.Value = settingValues[settingId] ?? string.Empty;
                        Console.WriteLine($"Updated value to: '{setting.Value}'");
                    }

                    // Update icon if provided (including empty values)
                    if (settingIcons != null && settingIcons.ContainsKey(settingId))
                    {
                        var oldIcon = setting.Icon;
                        setting.Icon = settingIcons[settingId] ?? string.Empty;
                        Console.WriteLine($"Updated icon from '{oldIcon}' to '{setting.Icon}'");
                    }

                    await _siteSettingRepository.UpdateAsync(setting);
                }
            }

            TempData["SuccessMessage"] = "Settings updated successfully.";
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
            TempData["ErrorMessage"] = "An error occurred while updating settings.";
        }

        return RedirectToAction(nameof(Index));
    }

    [HttpPost]
    public async Task<IActionResult> UploadFile(IFormFile file)
    {
        try
        {
            if (file == null || file.Length == 0)
            {
                return Json(new { success = false, message = "No file selected" });
            }

            // Check if it's an image or video file
            var isImage = _fileUploadService.IsValidImageFile(file);
            var isVideo = IsValidVideoFile(file);

            if (!isImage && !isVideo)
            {
                return Json(new { success = false, message = "Invalid file type. Please upload an image file (JPG, PNG, GIF, WebP) or video file (MP4, WebM, OGV)." });
            }

            // Upload the file
            string uploadedUrl;
            if (isImage)
            {
                uploadedUrl = await _fileUploadService.UploadImageAsync(file, "uploads");
            }
            else
            {
                uploadedUrl = await UploadVideoAsync(file, "uploads");
            }

            return Json(new { success = true, url = uploadedUrl });
        }
        catch (Exception ex)
        {
            return Json(new { success = false, message = $"Upload failed: {ex.Message}" });
        }
    }

    private bool IsValidVideoFile(IFormFile file)
    {
        if (file == null || file.Length == 0)
            return false;

        var maxFileSize = 50 * 1024 * 1024; // 50MB for videos
        if (file.Length > maxFileSize)
            return false;

        var allowedExtensions = new[] { ".mp4", ".webm", ".ogv" };
        var extension = Path.GetExtension(file.FileName).ToLowerInvariant();
        if (!allowedExtensions.Contains(extension))
            return false;

        var validMimeTypes = new[] { "video/mp4", "video/webm", "video/ogg" };
        return validMimeTypes.Contains(file.ContentType.ToLowerInvariant());
    }

    private async Task<string> UploadVideoAsync(IFormFile file, string folder)
    {
        // Create upload directory if it doesn't exist
        var uploadPath = Path.Combine(_webHostEnvironment.WebRootPath, "videos", folder);
        if (!Directory.Exists(uploadPath))
        {
            Directory.CreateDirectory(uploadPath);
        }

        // Generate unique filename
        var extension = Path.GetExtension(file.FileName);
        var nameWithoutExtension = Path.GetFileNameWithoutExtension(file.FileName);
        nameWithoutExtension = string.Join("", nameWithoutExtension.Split(Path.GetInvalidFileNameChars()));
        nameWithoutExtension = nameWithoutExtension.Replace(" ", "-").ToLowerInvariant();
        var timestamp = DateTime.Now.ToString("yyyyMMdd-HHmmss");
        var fileName = $"{nameWithoutExtension}-{timestamp}{extension}";
        var filePath = Path.Combine(uploadPath, fileName);

        // Save the file
        using (var stream = new FileStream(filePath, FileMode.Create))
        {
            await file.CopyToAsync(stream);
        }

        // Return the relative URL
        return $"/videos/{folder}/{fileName}";
    }

    private async Task<bool> SettingExists(int id)
    {
        var setting = await _siteSettingRepository.GetByIdAsync(id);
        return setting != null && !setting.IsDeleted;
    }
}
