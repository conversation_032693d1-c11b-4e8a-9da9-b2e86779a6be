using System.ComponentModel.DataAnnotations;
using Technoloway.Core.Entities;

namespace Technoloway.Web.Areas.Admin.Models;

public class BlogPostViewModel
{
    public BlogPostViewModel()
    {
        Title = string.Empty;
        Content = string.Empty;
        Slug = string.Empty;
        FeaturedImageUrl = string.Empty;
        Excerpt = string.Empty;
        Categories = string.Empty;
        MetaTitle = string.Empty;
        MetaDescription = string.Empty;
        MetaKeywords = string.Empty;
        AuthorId = string.Empty;
        AuthorName = string.Empty;
        IsPublished = false;
    }

    public int Id { get; set; }

    [Required(ErrorMessage = "Title is required")]
    [StringLength(200, ErrorMessage = "Title cannot exceed 200 characters")]
    public string Title { get; set; } = string.Empty;

    [Required(ErrorMessage = "Content is required")]
    public string Content { get; set; } = string.Empty;

    [StringLength(200, ErrorMessage = "Slug cannot exceed 200 characters")]
    public string Slug { get; set; } = string.Empty;

    // Current featured image URL - NOT required for form validation
    public string? FeaturedImageUrl { get; set; } = string.Empty;

    // For file upload
    [Display(Name = "Featured Image")]
    public IFormFile? FeaturedImageFile { get; set; }

    [StringLength(500, ErrorMessage = "Excerpt cannot exceed 500 characters")]
    public string Excerpt { get; set; } = string.Empty;

    public bool IsPublished { get; set; } = false;

    public DateTime? PublishedAt { get; set; }

    [StringLength(200, ErrorMessage = "Categories cannot exceed 200 characters")]
    public string Categories { get; set; } = string.Empty; // Comma-separated list of categories

    // SEO properties
    [StringLength(200, ErrorMessage = "Meta title cannot exceed 200 characters")]
    public string MetaTitle { get; set; } = string.Empty;

    [StringLength(500, ErrorMessage = "Meta description cannot exceed 500 characters")]
    public string MetaDescription { get; set; } = string.Empty;

    [StringLength(200, ErrorMessage = "Meta keywords cannot exceed 200 characters")]
    public string MetaKeywords { get; set; } = string.Empty;

    // Navigation properties
    public string AuthorId { get; set; } = string.Empty;
    public string AuthorName { get; set; } = string.Empty;

    // For tracking
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }

    // Convert from Entity to ViewModel
    public static BlogPostViewModel FromEntity(BlogPost blogPost)
    {
        return new BlogPostViewModel
        {
            Id = blogPost.Id,
            Title = blogPost.Title,
            Content = blogPost.Content,
            Slug = blogPost.Slug,
            FeaturedImageUrl = blogPost.FeaturedImageUrl,
            Excerpt = blogPost.Excerpt,
            IsPublished = blogPost.IsPublished,
            PublishedAt = blogPost.PublishedAt,
            Categories = blogPost.Categories,
            MetaTitle = blogPost.Title,
            MetaDescription = blogPost.Excerpt,
            MetaKeywords = blogPost.Categories,
            AuthorId = "admin",
            AuthorName = "Admin",
            CreatedAt = blogPost.CreatedAt,
            UpdatedAt = blogPost.UpdatedAt
        };
    }

    // Convert from ViewModel to Entity
    public BlogPost ToEntity()
    {
        return new BlogPost
        {
            Id = this.Id,
            Title = this.Title ?? string.Empty,
            Content = this.Content ?? string.Empty,
            Slug = this.Slug ?? string.Empty,
            FeaturedImageUrl = this.FeaturedImageUrl ?? string.Empty,
            Excerpt = this.Excerpt ?? string.Empty,
            IsPublished = this.IsPublished,
            PublishedAt = this.PublishedAt,
            Categories = this.Categories ?? string.Empty,
            // Meta properties and Author properties removed from BlogPost entity
            CreatedAt = this.CreatedAt,
            UpdatedAt = this.UpdatedAt
        };
    }

    // Update existing entity with ViewModel data
    public void UpdateEntity(BlogPost blogPost)
    {
        blogPost.Title = this.Title ?? string.Empty;
        blogPost.Content = this.Content ?? string.Empty;
        blogPost.Slug = this.Slug ?? string.Empty;
        blogPost.FeaturedImageUrl = this.FeaturedImageUrl ?? string.Empty;
        blogPost.Excerpt = this.Excerpt ?? string.Empty;
        blogPost.IsPublished = this.IsPublished;
        blogPost.PublishedAt = this.PublishedAt;
        blogPost.Categories = this.Categories ?? string.Empty;
        // Meta properties and Author properties removed from BlogPost entity
    }
}
