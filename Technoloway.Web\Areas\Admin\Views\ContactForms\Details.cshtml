@model Technoloway.Core.Entities.ContactForm

@{
    ViewData["Title"] = "Contact Form Details";
    Layout = "~/Areas/Admin/Views/Shared/_AdminLayout.cshtml";
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Contact Form Details</h1>
    <div class="d-sm-flex">
        <a asp-action="Index" class="btn btn-secondary btn-sm me-2">
            <i class="fas fa-arrow-left me-2"></i>Back to List
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">Message Details</h6>
                <div>
                    @if (!Model.IsRead)
                    {
                        <span class="badge bg-warning me-2">
                            <i class="fas fa-envelope"></i> Unread
                        </span>
                    }
                    else
                    {
                        <span class="badge bg-success me-2">
                            <i class="fas fa-envelope-open"></i> Read
                        </span>
                    }
                    <span class="badge @(Model.Status switch {
                        "New" => "bg-primary",
                        "Read" => "bg-info",
                        "Replied" => "bg-success",
                        "Archived" => "bg-secondary",
                        _ => "bg-primary"
                    })">@Model.Status</span>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <h5 class="fw-bold">From:</h5>
                        <p class="mb-1"><strong>@Model.Name</strong></p>
                        <p class="mb-1">
                            <i class="fas fa-envelope me-2"></i>
                            <a href="mailto:@Model.Email" class="text-decoration-none">@Model.Email</a>
                        </p>
                        @if (!string.IsNullOrEmpty(Model.Phone))
                        {
                            <p class="mb-1">
                                <i class="fas fa-phone me-2"></i>
                                <a href="tel:@Model.Phone" class="text-decoration-none">@Model.Phone</a>
                            </p>
                        }
                    </div>
                    <div class="col-md-6">
                        <h5 class="fw-bold">Date & Time:</h5>
                        <p class="mb-1">
                            <i class="fas fa-calendar me-2"></i>
                            @Model.CreatedAt.ToString("MMMM dd, yyyy")
                        </p>
                        <p class="mb-1">
                            <i class="fas fa-clock me-2"></i>
                            @Model.CreatedAt.ToString("h:mm tt")
                        </p>
                        @if (Model.ReadAt.HasValue)
                        {
                            <p class="mb-1 text-muted">
                                <i class="fas fa-eye me-2"></i>
                                Read on @Model.ReadAt.Value.ToString("MMM dd, yyyy 'at' h:mm tt")
                            </p>
                        }
                    </div>
                </div>

                <div class="mb-4">
                    <h5 class="fw-bold">Subject:</h5>
                    <p class="lead">@Model.Subject</p>
                </div>

                <div class="mb-4">
                    <h5 class="fw-bold">Message:</h5>
                    <div class="border rounded p-3 bg-light">
                        <p class="mb-0" style="white-space: pre-wrap;">@Model.Message</p>
                    </div>
                </div>

                <!-- Quick Reply Section -->
                <div class="border-top pt-4">
                    <h5 class="fw-bold mb-3">Quick Actions:</h5>
                    <div class="d-flex flex-wrap gap-2">
                        <a href="mailto:@Model.Email?subject=Re: @Model.Subject" class="btn btn-primary">
                            <i class="fas fa-reply me-2"></i>Reply via Email
                        </a>
                        @if (!string.IsNullOrEmpty(Model.Phone))
                        {
                            <a href="tel:@Model.Phone" class="btn btn-success">
                                <i class="fas fa-phone me-2"></i>Call
                            </a>
                        }
                        <button class="btn btn-info" onclick="copyToClipboard('@Model.Email')">
                            <i class="fas fa-copy me-2"></i>Copy Email
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Status Management</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label fw-bold">Update Status:</label>
                    <select class="form-select" id="statusSelect" onchange="updateStatus(@Model.Id, this.value)">
                        <option value="New" selected="@(Model.Status == "New")">New</option>
                        <option value="Read" selected="@(Model.Status == "Read")">Read</option>
                        <option value="Replied" selected="@(Model.Status == "Replied")">Replied</option>
                        <option value="Archived" selected="@(Model.Status == "Archived")">Archived</option>
                    </select>
                </div>

                <div class="mb-3">
                    <button class="btn @(Model.IsRead ? "btn-warning" : "btn-success") w-100"
                            onclick="toggleRead(@Model.Id, this)">
                        <i class="fas @(Model.IsRead ? "fa-envelope" : "fa-envelope-open") me-2"></i>
                        @(Model.IsRead ? "Mark as Unread" : "Mark as Read")
                    </button>
                </div>

                <div class="border-top pt-3">
                    <h6 class="fw-bold">Contact Information:</h6>
                    <p class="mb-1"><strong>ID:</strong> #@Model.Id</p>
                    <p class="mb-1"><strong>Created:</strong> @Model.CreatedAt.ToString("MMM dd, yyyy")</p>
                    @if (Model.UpdatedAt.HasValue)
                    {
                        <p class="mb-1"><strong>Updated:</strong> @Model.UpdatedAt.Value.ToString("MMM dd, yyyy")</p>
                    }
                </div>
            </div>
        </div>

        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Actions</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a asp-action="Delete" asp-route-id="@Model.Id" class="btn btn-danger">
                        <i class="fas fa-trash me-2"></i>Delete Message
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function updateStatus(id, status) {
            $.post('@Url.Action("UpdateStatus")', { id: id, status: status })
                .done(function(data) {
                    if (data.success) {
                        // Update the status badge
                        location.reload();
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .fail(function() {
                    alert('Error updating status');
                });
        }

        function toggleRead(id, button) {
            $.post('@Url.Action("ToggleRead")', { id: id })
                .done(function(data) {
                    if (data.success) {
                        location.reload(); // Reload to update the UI
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .fail(function() {
                    alert('Error updating read status');
                });
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                // Show success message
                const btn = event.target.closest('button');
                const originalText = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-check me-2"></i>Copied!';
                btn.classList.remove('btn-info');
                btn.classList.add('btn-success');

                setTimeout(function() {
                    btn.innerHTML = originalText;
                    btn.classList.remove('btn-success');
                    btn.classList.add('btn-info');
                }, 2000);
            }).catch(function(err) {
                alert('Failed to copy email address');
            });
        }
    </script>
}
