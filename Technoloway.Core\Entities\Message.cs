using Technoloway.Core.Common;

namespace Technoloway.Core.Entities;

public class Message : BaseEntity
{
    public string Content { get; set; } = string.Empty;
    public string SenderName { get; set; } = string.Empty;
    public string SenderRole { get; set; } = string.Empty; // Admin, Client
    public string SenderId { get; set; } = string.Empty; // Identity user ID
    public bool IsRead { get; set; } = false;
    public DateTime? ReadAt { get; set; }
    
    // Navigation properties
    public int ProjectId { get; set; }
    public Project Project { get; set; } = null!;
}
