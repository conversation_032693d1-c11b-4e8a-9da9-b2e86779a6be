﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Technoloway.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddChatbotEntities : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "ChatbotIntents",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Name = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    DisplayName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false),
                    DisplayOrder = table.Column<int>(type: "INTEGER", nullable: false),
                    IconClass = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    IsDeleted = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ChatbotIntents", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "ChatbotKeywords",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Keyword = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    Synonyms = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    Weight = table.Column<int>(type: "INTEGER", nullable: false),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false),
                    MatchType = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    IsCaseSensitive = table.Column<bool>(type: "INTEGER", nullable: false),
                    ChatbotIntentId = table.Column<int>(type: "INTEGER", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    IsDeleted = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ChatbotKeywords", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ChatbotKeywords_ChatbotIntents_ChatbotIntentId",
                        column: x => x.ChatbotIntentId,
                        principalTable: "ChatbotIntents",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ChatbotResponses",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Title = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    Content = table.Column<string>(type: "TEXT", maxLength: 2000, nullable: false),
                    ResponseType = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false),
                    DisplayOrder = table.Column<int>(type: "INTEGER", nullable: false),
                    Conditions = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false),
                    TemplateVariables = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: false),
                    ChatbotIntentId = table.Column<int>(type: "INTEGER", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    IsDeleted = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ChatbotResponses", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ChatbotResponses_ChatbotIntents_ChatbotIntentId",
                        column: x => x.ChatbotIntentId,
                        principalTable: "ChatbotIntents",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ChatbotQuickActions",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Label = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    Value = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    IconClass = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    ActionType = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    Url = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false),
                    DisplayOrder = table.Column<int>(type: "INTEGER", nullable: false),
                    CssClass = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    ChatbotResponseId = table.Column<int>(type: "INTEGER", nullable: true),
                    ChatbotIntentId = table.Column<int>(type: "INTEGER", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    IsDeleted = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ChatbotQuickActions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ChatbotQuickActions_ChatbotIntents_ChatbotIntentId",
                        column: x => x.ChatbotIntentId,
                        principalTable: "ChatbotIntents",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_ChatbotQuickActions_ChatbotResponses_ChatbotResponseId",
                        column: x => x.ChatbotResponseId,
                        principalTable: "ChatbotResponses",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_ChatbotKeywords_ChatbotIntentId",
                table: "ChatbotKeywords",
                column: "ChatbotIntentId");

            migrationBuilder.CreateIndex(
                name: "IX_ChatbotQuickActions_ChatbotIntentId",
                table: "ChatbotQuickActions",
                column: "ChatbotIntentId");

            migrationBuilder.CreateIndex(
                name: "IX_ChatbotQuickActions_ChatbotResponseId",
                table: "ChatbotQuickActions",
                column: "ChatbotResponseId");

            migrationBuilder.CreateIndex(
                name: "IX_ChatbotResponses_ChatbotIntentId",
                table: "ChatbotResponses",
                column: "ChatbotIntentId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ChatbotKeywords");

            migrationBuilder.DropTable(
                name: "ChatbotQuickActions");

            migrationBuilder.DropTable(
                name: "ChatbotResponses");

            migrationBuilder.DropTable(
                name: "ChatbotIntents");
        }
    }
}
