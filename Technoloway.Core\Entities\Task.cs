using Technoloway.Core.Common;
using System.ComponentModel.DataAnnotations;

namespace Technoloway.Core.Entities;

public class ProjectTask : BaseEntity
{
    [Required]
    public int TaskID { get; set; }
    
    [Required]
    public int ProjNo { get; set; }
    
    [Required]
    public int TeamMemberID { get; set; }
    
    public string? TaskDes { get; set; }
    
    public DateTime? TaskStartDate { get; set; }
    
    public DateTime? TaskEndDate { get; set; }
    
    public int? WorkHours { get; set; }
    
    public decimal? PayRate { get; set; }
    
    [StringLength(10)]
    public string? Status { get; set; }
    
    public string? Notes { get; set; }
    
    // Navigation properties
    public Project Project { get; set; } = null!;
    public TeamMember TeamMember { get; set; } = null!;
}
