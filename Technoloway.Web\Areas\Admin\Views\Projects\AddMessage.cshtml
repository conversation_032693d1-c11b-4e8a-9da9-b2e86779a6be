@model Technoloway.Core.Entities.Message

@{
    ViewData["Title"] = "Add Message";
    Layout = "_AdminLayout";
    var project = ViewBag.Project as Technoloway.Core.Entities.Project;
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Add Message for @(project != null ? project.Name : "Project")</h1>
    <div>
        <a asp-action="Messages" asp-route-id="@(project != null ? project.Id : 0)" class="btn btn-sm btn-warning shadow-sm">
            <i class="fas fa-comments fa-sm text-white-50"></i> View Messages
        </a>
        <a asp-action="Details" asp-route-id="@(project != null ? project.Id : 0)" class="btn btn-sm btn-info shadow-sm">
            <i class="fas fa-eye fa-sm text-white-50"></i> View Project
        </a>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Message Details</h6>
    </div>
    <div class="card-body">
        <form asp-action="AddMessage" method="post">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="ProjectId" />
            
            <div class="form-group mb-3">
                <label asp-for="Content" class="control-label">Message</label>
                <textarea asp-for="Content" class="form-control" rows="5" required></textarea>
                <span asp-validation-for="Content" class="text-danger"></span>
            </div>
            
            <div class="alert alert-info">
                <p class="mb-0">
                    <i class="fas fa-info-circle"></i> 
                    This message will be sent as an admin. The client will be able to see this message in their project dashboard.
                </p>
            </div>
            
            <div class="form-group">
                <button type="submit" class="btn btn-primary">Send Message</button>
                <a asp-action="Messages" asp-route-id="@(project != null ? project.Id : 0)" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
