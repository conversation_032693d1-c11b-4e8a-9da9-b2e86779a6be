@model IEnumerable<Technoloway.Core.Entities.Client>

@{
    ViewData["Title"] = "Clients";
    Layout = "_AdminLayout";
}

<div class="container-fluid p-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800 fw-bold">Client Management</h1>
            <p class="text-muted mb-0">Manage your client relationships and business partnerships</p>
        </div>
        <div class="d-flex gap-2">
            <button class="btn-modern-admin secondary">
                <i class="fas fa-filter"></i>
                Filter
            </button>
            <a asp-action="Reports" class="btn-modern-admin secondary">
                <i class="fas fa-chart-line"></i>
                Reports
            </a>
            <a asp-action="Create" class="btn-modern-admin primary">
                <i class="fas fa-plus"></i>
                Add New Client
            </a>
        </div>
    </div>

    <!-- Client Stats Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon me-3">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Total Clients</p>
                        <h3 class="admin-stat-number">@Model.Count()</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card success h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon success me-3">
                        <i class="fas fa-image"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">With Logos</p>
                        <h3 class="admin-stat-number">@Model.Count(c => !string.IsNullOrEmpty(c.LogoUrl))</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card info h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon info me-3">
                        <i class="fas fa-globe"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Countries</p>
                        <h3 class="admin-stat-number">@Model.Where(c => !string.IsNullOrEmpty(c.Country)).Select(c => c.Country).Distinct().Count()</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card warning h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon warning me-3">
                        <i class="fas fa-calendar-plus"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">This Month</p>
                        <h3 class="admin-stat-number">@Model.Count(c => c.CreatedAt.Month == DateTime.Now.Month && c.CreatedAt.Year == DateTime.Now.Year)</h3>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Client Management Card -->
    <div class="admin-card">
        <div class="card-header bg-white border-bottom d-flex align-items-center justify-content-between p-4">
            <div>
                <h5 class="mb-0 fw-bold text-gray-800">All Clients</h5>
                <p class="text-muted mb-0 small">@Model.Count() total clients</p>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-download me-1"></i>
                    Export
                </button>
                <button class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-sync me-1"></i>
                    Refresh
                </button>
            </div>
        </div>
        <div class="card-body p-0">
            @if (Model.Any())
            {
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="dataTable">
                        <thead>
                            <tr>
                                <th class="border-0 fw-semibold">Company</th>
                                <th class="border-0 fw-semibold">Contact</th>
                                <th class="border-0 fw-semibold">Email</th>
                                <th class="border-0 fw-semibold">Phone</th>
                                <th class="border-0 fw-semibold">Location</th>
                                <th class="border-0 fw-semibold">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model)
                            {
                                <tr>
                                    <td class="border-0">
                                        <div class="d-flex align-items-center">
                                            <div class="me-3">
                                                @if (!string.IsNullOrEmpty(item.LogoUrl))
                                                {
                                                    <img src="@item.LogoUrl" alt="@item.CompanyName Logo"
                                                         class="rounded" style="height: 40px; width: 40px; object-fit: contain; border: 2px solid var(--admin-border);" />
                                                }
                                                else
                                                {
                                                    <div class="admin-stat-icon d-flex align-items-center justify-content-center"
                                                         style="width: 40px; height: 40px;">
                                                        <i class="fas fa-building text-white"></i>
                                                    </div>
                                                }
                                            </div>
                                            <div>
                                                <div class="fw-semibold text-gray-800">@item.CompanyName</div>
                                                <div class="text-muted small">Client Company</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="border-0">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-user me-2 text-muted"></i>
                                            <span class="fw-semibold">@item.ContactName</span>
                                        </div>
                                    </td>
                                    <td class="border-0">
                                        @if (!string.IsNullOrEmpty(item.ContactEmail))
                                        {
                                            <a href="mailto:@item.ContactEmail" class="text-decoration-none">
                                                <i class="fas fa-envelope me-1"></i>@item.ContactEmail
                                            </a>
                                        }
                                        else
                                        {
                                            <span class="text-muted fst-italic">
                                                <i class="fas fa-envelope me-1"></i>Not provided
                                            </span>
                                        }
                                    </td>
                                    <td class="border-0">
                                        @if (!string.IsNullOrEmpty(item.ContactPhone))
                                        {
                                            <a href="tel:@item.ContactPhone" class="text-decoration-none">
                                                <i class="fas fa-phone me-1"></i>@item.ContactPhone
                                            </a>
                                        }
                                        else
                                        {
                                            <span class="text-muted fst-italic">
                                                <i class="fas fa-phone me-1"></i>Not provided
                                            </span>
                                        }
                                    </td>
                                    <td class="border-0">
                                        @if (!string.IsNullOrEmpty(item.City) && !string.IsNullOrEmpty(item.Country))
                                        {
                                            <span class="badge bg-light text-dark">
                                                <i class="fas fa-map-marker-alt me-1"></i>@item.City, @item.Country
                                            </span>
                                        }
                                        else if (!string.IsNullOrEmpty(item.City))
                                        {
                                            <span class="badge bg-light text-dark">
                                                <i class="fas fa-map-marker-alt me-1"></i>@item.City
                                            </span>
                                        }
                                        else if (!string.IsNullOrEmpty(item.Country))
                                        {
                                            <span class="badge bg-light text-dark">
                                                <i class="fas fa-globe me-1"></i>@item.Country
                                            </span>
                                        }
                                        else
                                        {
                                            <span class="text-muted fst-italic">
                                                <i class="fas fa-map-marker-alt me-1"></i>Not specified
                                            </span>
                                        }
                                    </td>
                                    <td class="border-0">
                                        <div class="d-flex gap-1">
                                            <a asp-action="Edit" asp-route-id="@item.Id"
                                               class="btn btn-outline-primary btn-sm" title="Edit Client">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a asp-action="Details" asp-route-id="@item.Id"
                                               class="btn btn-outline-info btn-sm" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a asp-action="Projects" asp-route-id="@item.Id"
                                               class="btn btn-outline-success btn-sm" title="View Projects">
                                                <i class="fas fa-project-diagram"></i>
                                            </a>
                                            <a asp-action="Invoices" asp-route-id="@item.Id"
                                               class="btn btn-outline-warning btn-sm" title="View Invoices">
                                                <i class="fas fa-file-invoice-dollar"></i>
                                            </a>
                                            <a asp-action="Delete" asp-route-id="@item.Id"
                                               class="btn btn-outline-danger btn-sm" title="Delete Client"
                                               onclick="return confirm('Are you sure you want to delete this client?')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
            else
            {
                <div class="text-center py-5">
                    <i class="fas fa-users text-muted mb-3" style="font-size: 3rem;"></i>
                    <h5 class="text-muted">No clients found</h5>
                    <p class="text-muted">Add your first client to start managing business relationships.</p>
                    <a asp-action="Create" class="btn-modern-admin primary">
                        <i class="fas fa-plus me-2"></i>
                        Add First Client
                    </a>
                </div>
            }
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#dataTable').DataTable({
                "pageLength": 10,
                "responsive": true,
                "order": [[0, "asc"]], // Sort by Company name by default
                "columnDefs": [
                    { "orderable": false, "targets": [0, 5] } // Disable sorting for Company and Actions columns
                ]
            });
        });

        // Add hover effects to stat cards
        $('.admin-stat-card').hover(
            function() {
                $(this).addClass('shadow-lg');
            },
            function() {
                $(this).removeClass('shadow-lg');
            }
        );

        // Add client statistics calculations
        function updateClientStats() {
            const totalClients = @Model.Count();
            const withLogos = @Model.Count(c => !string.IsNullOrEmpty(c.LogoUrl));
            const countries = @Model.Where(c => !string.IsNullOrEmpty(c.Country)).Select(c => c.Country).Distinct().Count();
            const thisMonth = @Model.Count(c => c.CreatedAt.Month == DateTime.Now.Month && c.CreatedAt.Year == DateTime.Now.Year);

            if (totalClients > 0) {
                const logoPercentage = Math.round((withLogos / totalClients) * 100);
                console.log(`Clients with logos: ${logoPercentage}%`);
                console.log(`Countries represented: ${countries}`);
                console.log(`New clients this month: ${thisMonth}`);
            }
        }

        updateClientStats();

        // Enhanced confirmation for delete
        $('a[asp-action="Delete"]').click(function(e) {
            const companyName = $(this).closest('tr').find('.fw-semibold').first().text();
            if (!confirm(`Are you sure you want to delete the client "${companyName}"?`)) {
                e.preventDefault();
                return false;
            }
        });

        // Add logo hover effects
        $('img[alt*="Logo"]').hover(
            function() {
                $(this).css('transform', 'scale(1.1)');
                $(this).css('transition', 'transform 0.3s ease');
            },
            function() {
                $(this).css('transform', 'scale(1)');
            }
        );

        // Add contact link hover effects
        $('a[href^="mailto:"], a[href^="tel:"]').hover(
            function() {
                $(this).css('transform', 'translateX(3px)');
                $(this).css('transition', 'transform 0.2s ease');
            },
            function() {
                $(this).css('transform', 'translateX(0)');
            }
        );

        // Add location badge hover effects
        $('.badge').hover(
            function() {
                $(this).css('transform', 'scale(1.05)');
                $(this).css('transition', 'transform 0.2s ease');
            },
            function() {
                $(this).css('transform', 'scale(1)');
            }
        );

        // Add building icon hover effects
        $('.fa-building').parent().hover(
            function() {
                $(this).css('transform', 'rotate(5deg) scale(1.1)');
                $(this).css('transition', 'transform 0.3s ease');
            },
            function() {
                $(this).css('transform', 'rotate(0deg) scale(1)');
            }
        );
    </script>
}
