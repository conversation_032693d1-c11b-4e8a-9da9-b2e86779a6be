@model Technoloway.Web.Areas.Admin.Models.SiteSettingViewModel
@using System.IO

@{
    ViewData["Title"] = "Edit Setting";
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Edit Setting</h1>
    <a asp-action="Index" class="btn btn-secondary shadow-sm">
        <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Settings
    </a>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-edit me-2"></i>Edit Setting: @Model.Key
                </h6>
            </div>
            <div class="card-body">
                <form asp-action="Edit" method="post" enctype="multipart/form-data">
                    <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                    <input type="hidden" asp-for="Id" />
                    <input type="hidden" asp-for="CreatedAt" />

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Key" class="form-label">Setting Key</label>
                                <input asp-for="Key" class="form-control" readonly />
                                <span asp-validation-for="Key" class="text-danger"></span>
                                <small class="form-text text-muted">The unique identifier for this setting.</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Group" class="form-label">Group</label>
                                <select asp-for="Group" class="form-control">
                                    <option value="General">General</option>
                                    <option value="Contact">Contact</option>
                                    <option value="Social">Social</option>
                                    <option value="QuickLinks">QuickLinks</option>
                                    <option value="Integration">Integration</option>
                                    <option value="Payment">Payment</option>
                                    <option value="SEO">SEO</option>
                                    <option value="Email">Email</option>
                                    <option value="Security">Security</option>
                                </select>
                                <span asp-validation-for="Group" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="Value" class="form-label">Value</label>
                        @if (Model.Key.ToLower().Contains("password") || Model.Key.ToLower().Contains("secret") || Model.Key.ToLower().Contains("key"))
                        {
                            <input asp-for="Value" type="password" class="form-control" />
                        }
                        else if (Model.Value.Length > 100)
                        {
                            <textarea asp-for="Value" class="form-control" rows="4"></textarea>
                        }
                        else
                        {
                            <input asp-for="Value" class="form-control" />
                        }
                        <span asp-validation-for="Value" class="text-danger"></span>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="Description" class="form-label">Description</label>
                        <textarea asp-for="Description" class="form-control" rows="2" placeholder="Brief description of what this setting controls"></textarea>
                        <span asp-validation-for="Description" class="text-danger"></span>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="IconFile" class="form-label">Setting Icon</label>
                        <input asp-for="IconFile" class="form-control" type="file" accept="image/*" />
                        <span asp-validation-for="IconFile" class="text-danger"></span>
                        <small class="form-text text-muted">
                            Upload a new setting icon image. Supported formats: JPG, PNG, GIF, WebP. Maximum size: 5MB. Recommended size: 32x32px or 64x64px.
                        </small>

                        @if (!string.IsNullOrEmpty(Model.Icon) && Model.Icon.StartsWith("/images/"))
                        {
                            <div class="mt-2">
                                <small class="text-muted">Current icon:</small>
                                <div class="d-flex align-items-center mt-1">
                                    <img src="@Model.Icon" alt="Current Icon" style="height: 24px; width: 24px; object-fit: contain;" />
                                </div>
                            </div>
                        }

                        <div id="icon-preview" class="mt-2" style="display: none;">
                            <small class="text-muted">New icon preview:</small><br>
                            <img id="preview-img" style="height: 32px; width: 32px; object-fit: contain; border: 1px solid #ddd; border-radius: 4px; padding: 4px;" />
                        </div>

                        <input type="hidden" asp-for="Icon" />
                    </div>

                    <div class="form-check mb-3">
                        <input asp-for="IsPublic" class="form-check-input" type="checkbox" />
                        <label asp-for="IsPublic" class="form-check-label">
                            Is Public Setting
                        </label>
                        <small class="form-text text-muted d-block">
                            Public settings can be accessed by the frontend. Private settings are for admin use only.
                        </small>
                        <span asp-validation-for="IsPublic" class="text-danger"></span>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Save Changes
                        </button>
                        <a asp-action="Index" class="btn btn-secondary ms-2">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                        <a asp-action="Delete" asp-route-id="@Model.Id" class="btn btn-danger ms-2">
                            <i class="fas fa-trash me-2"></i>Delete
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-info">
                    <i class="fas fa-info-circle me-2"></i>Setting Information
                </h6>
            </div>
            <div class="card-body">
                <p><strong>Group:</strong> @Model.Group</p>
                <p><strong>Created:</strong> @Model.CreatedAt.ToString("MMM dd, yyyy")</p>
                @if (Model.UpdatedAt.HasValue)
                {
                    <p><strong>Last Updated:</strong> @Model.UpdatedAt.Value.ToString("MMM dd, yyyy")</p>
                }
                <p><strong>Visibility:</strong>
                    @if (Model.IsPublic)
                    {
                        <span class="badge bg-success">Public</span>
                    }
                    else
                    {
                        <span class="badge bg-warning text-dark">Private</span>
                    }
                </p>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        $(document).ready(function() {
            // File upload preview
            $('#IconFile').on('change', function() {
                var file = this.files[0];
                var preview = $('#icon-preview');
                var previewImg = $('#preview-img');

                if (file) {
                    // Validate file type
                    var validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
                    if (!validTypes.includes(file.type)) {
                        alert('Please select a valid image file (JPG, PNG, GIF, WebP)');
                        $(this).val('');
                        preview.hide();
                        return;
                    }

                    // Validate file size (5MB)
                    if (file.size > 5 * 1024 * 1024) {
                        alert('File size must be less than 5MB');
                        $(this).val('');
                        preview.hide();
                        return;
                    }

                    // Show preview
                    var reader = new FileReader();
                    reader.onload = function(e) {
                        previewImg.attr('src', e.target.result);
                        preview.show();
                    };
                    reader.readAsDataURL(file);
                } else {
                    preview.hide();
                }
            });

            // Drag and drop functionality for file upload
            var dropArea = $('#IconFile').parent();

            dropArea.on('dragover', function(e) {
                e.preventDefault();
                $(this).addClass('border-primary');
            });

            dropArea.on('dragleave', function(e) {
                e.preventDefault();
                $(this).removeClass('border-primary');
            });

            dropArea.on('drop', function(e) {
                e.preventDefault();
                $(this).removeClass('border-primary');

                var files = e.originalEvent.dataTransfer.files;
                if (files.length > 0) {
                    $('#IconFile')[0].files = files;
                    $('#IconFile').trigger('change');
                }
            });
        });
    </script>
}
