using System.ComponentModel.DataAnnotations;

namespace Technoloway.Web.Areas.Admin.Models
{
    public class ChatbotResponseViewModel
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "Response text is required")]
        [StringLength(2000, ErrorMessage = "Response text cannot exceed 2000 characters")]
        [Display(Name = "Response Text")]
        public string ResponseText { get; set; } = string.Empty;

        [StringLength(50, ErrorMessage = "Response type cannot exceed 50 characters")]
        [Display(Name = "Response Type")]
        public string ResponseType { get; set; } = "text";

        [Display(Name = "Is Active")]
        public bool IsActive { get; set; } = true;

        [Range(0, int.MaxValue, ErrorMessage = "Display order must be a positive number")]
        [Display(Name = "Display Order")]
        public int DisplayOrder { get; set; }

        [StringLength(500, ErrorMessage = "Conditions cannot exceed 500 characters")]
        [Display(Name = "Conditions (JSON)")]
        public string? Conditions { get; set; }

        [StringLength(1000, ErrorMessage = "Template variables cannot exceed 1000 characters")]
        [Display(Name = "Template Variables (JSON)")]
        public string? TemplateVariables { get; set; }

        [Required(ErrorMessage = "Please select an intent")]
        [Display(Name = "Intent")]
        public int ChatbotIntentId { get; set; }
    }
}
