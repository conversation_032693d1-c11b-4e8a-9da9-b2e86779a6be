using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Technoloway.Core.Interfaces;
using Technoloway.Core.Entities;

namespace Technoloway.Web.Areas.Client.Controllers;

[Area("Client")]
[Authorize(Policy = "RequireClient")]
public class DocumentsController : Controller
{
    private readonly IRepository<ProjectDocument> _documentRepository;
    private readonly IRepository<Project> _projectRepository;
    private readonly UserManager<IdentityUser> _userManager;
    private readonly IRepository<Core.Entities.Client> _clientRepository;

    public DocumentsController(
        IRepository<ProjectDocument> documentRepository,
        IRepository<Project> projectRepository,
        UserManager<IdentityUser> userManager,
        IRepository<Core.Entities.Client> clientRepository)
    {
        _documentRepository = documentRepository;
        _projectRepository = projectRepository;
        _userManager = userManager;
        _clientRepository = clientRepository;
    }

    public async Task<IActionResult> Index(int? projectId = null, string? category = null)
    {
        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return Challenge();
        }

        var client = (await _clientRepository.ListAsync(c => c.UserId == user.Id)).FirstOrDefault();
        if (client == null)
        {
            return RedirectToAction("Create", "Profile");
        }

        var clientProjects = await _projectRepository.ListAsync(p => p.ClientId == client.Id);
        var projectIds = clientProjects.Select(p => p.Id).ToList();

        var query = _documentRepository.GetAll()
            .Where(d => projectIds.Contains(d.ProjectId) && !d.IsDeleted);

        if (projectId.HasValue)
        {
            query = query.Where(d => d.ProjectId == projectId.Value);
        }

        if (!string.IsNullOrEmpty(category))
        {
            query = query.Where(d => d.FileType.ToLower().Contains(category.ToLower()));
        }

        var documents = await query
            .OrderByDescending(d => d.CreatedAt)
            .ToListAsync();

        ViewBag.Projects = clientProjects.OrderBy(p => p.Name).ToList();
        ViewBag.SelectedProjectId = projectId;
        ViewBag.SelectedCategory = category;
        ViewBag.TotalDocuments = documents.Count;
        ViewBag.TotalSize = documents.Sum(d => d.FileSize);

        // Group documents by file type for category filter
        ViewBag.Categories = documents
            .GroupBy(d => d.FileType.ToLower())
            .Select(g => new { Type = g.Key, Count = g.Count() })
            .OrderByDescending(x => x.Count)
            .ToList();

        return View(documents);
    }

    public async Task<IActionResult> Details(int id)
    {
        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return Challenge();
        }

        var client = (await _clientRepository.ListAsync(c => c.UserId == user.Id)).FirstOrDefault();
        if (client == null)
        {
            return RedirectToAction("Create", "Profile");
        }

        var document = await _documentRepository.GetAll()
            .Where(d => d.Id == id && !d.IsDeleted)
            .Include(d => d.Project)
            .FirstOrDefaultAsync();

        if (document == null)
        {
            return NotFound();
        }

        // Verify the document belongs to a project owned by this client
        if (document.Project?.ClientId != client.Id)
        {
            return Forbid();
        }

        return View(document);
    }

    public async Task<IActionResult> Download(int id)
    {
        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return Challenge();
        }

        var client = (await _clientRepository.ListAsync(c => c.UserId == user.Id)).FirstOrDefault();
        if (client == null)
        {
            return RedirectToAction("Create", "Profile");
        }

        var document = await _documentRepository.GetAll()
            .Where(d => d.Id == id && !d.IsDeleted)
            .Include(d => d.Project)
            .FirstOrDefaultAsync();

        if (document == null)
        {
            return NotFound();
        }

        // Verify the document belongs to a project owned by this client
        if (document.Project?.ClientId != client.Id)
        {
            return Forbid();
        }

        // Check if file exists
        var filePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", document.FileUrl.TrimStart('/'));
        if (!System.IO.File.Exists(filePath))
        {
            TempData["Error"] = "File not found on server.";
            return RedirectToAction("Index");
        }

        var fileBytes = await System.IO.File.ReadAllBytesAsync(filePath);
        var contentType = GetContentType(document.FileType);

        return File(fileBytes, contentType, document.FileName);
    }

    public async Task<IActionResult> Preview(int id)
    {
        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return Challenge();
        }

        var client = (await _clientRepository.ListAsync(c => c.UserId == user.Id)).FirstOrDefault();
        if (client == null)
        {
            return RedirectToAction("Create", "Profile");
        }

        var document = await _documentRepository.GetAll()
            .Where(d => d.Id == id && !d.IsDeleted)
            .Include(d => d.Project)
            .FirstOrDefaultAsync();

        if (document == null)
        {
            return NotFound();
        }

        // Verify the document belongs to a project owned by this client
        if (document.Project?.ClientId != client.Id)
        {
            return Forbid();
        }

        // Check if file type supports preview
        var previewableTypes = new[] { "pdf", "jpg", "jpeg", "png", "gif", "txt", "md" };
        var fileExtension = Path.GetExtension(document.FileName).TrimStart('.').ToLower();

        if (!previewableTypes.Contains(fileExtension))
        {
            TempData["Error"] = "File type does not support preview.";
            return RedirectToAction("Details", new { id });
        }

        ViewBag.Document = document;
        ViewBag.IsImage = new[] { "jpg", "jpeg", "png", "gif" }.Contains(fileExtension);
        ViewBag.IsPdf = fileExtension == "pdf";
        ViewBag.IsText = new[] { "txt", "md" }.Contains(fileExtension);

        if (ViewBag.IsText)
        {
            var filePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", document.FileUrl.TrimStart('/'));
            if (System.IO.File.Exists(filePath))
            {
                ViewBag.TextContent = await System.IO.File.ReadAllTextAsync(filePath);
            }
        }

        return View(document);
    }

    private string GetContentType(string fileType)
    {
        return fileType.ToLower() switch
        {
            "pdf" => "application/pdf",
            "doc" => "application/msword",
            "docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "xls" => "application/vnd.ms-excel",
            "xlsx" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "ppt" => "application/vnd.ms-powerpoint",
            "pptx" => "application/vnd.openxmlformats-officedocument.presentationml.presentation",
            "jpg" or "jpeg" => "image/jpeg",
            "png" => "image/png",
            "gif" => "image/gif",
            "txt" => "text/plain",
            "zip" => "application/zip",
            "rar" => "application/x-rar-compressed",
            _ => "application/octet-stream"
        };
    }
}
