@model Technoloway.Web.Areas.Admin.ViewModels.InvoiceAnalyticsViewModel
@{
    ViewData["Title"] = "Invoice Analytics";
    Layout = "_AdminLayout";
}

<div class="container-fluid p-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800 fw-bold">Invoice Analytics</h1>
            <p class="text-muted mb-0">Comprehensive insights into your billing performance</p>
        </div>
        <div class="d-flex gap-2">
            <button class="btn-modern-admin secondary" onclick="exportAnalytics()">
                <i class="fas fa-download"></i>
                Export Report
            </button>
            <a asp-action="Index" class="btn-modern-admin primary">
                <i class="fas fa-arrow-left"></i>
                Back to Invoices
            </a>
        </div>
    </div>

    <!-- Key Metrics Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon me-3">
                        <i class="fas fa-file-invoice-dollar"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Total Revenue</p>
                        <h3 class="admin-stat-number mb-0">$@Model.TotalAmount.ToString("N2")</h3>
                        <small class="text-muted">@Model.TotalInvoices invoices</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card success h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon success me-3">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Collected</p>
                        <h3 class="admin-stat-number mb-0">$@Model.PaidAmount.ToString("N2")</h3>
                        <small class="text-success">@Model.PaymentRate.ToString("F1")% collection rate</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card warning h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon warning me-3">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Outstanding</p>
                        <h3 class="admin-stat-number mb-0">$@Model.OutstandingAmount.ToString("N2")</h3>
                        <small class="text-warning">@(Model.TotalInvoices - Model.PaidInvoices) pending invoices</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card info h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon info me-3">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Average Invoice</p>
                        <h3 class="admin-stat-number mb-0">$@Model.AverageInvoiceAmount.ToString("N2")</h3>
                        <small class="text-info">@Model.AverageDaysToPayment.ToString("F0") avg days to pay</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Growth Metrics -->
    <div class="row g-4 mb-4">
        <div class="col-md-6">
            <div class="admin-card h-100">
                <div class="card-header bg-white border-bottom p-4">
                    <h5 class="mb-0 fw-bold">Growth Metrics</h5>
                </div>
                <div class="card-body p-4">
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="@(Model.MonthOverMonthGrowth >= 0 ? "text-success" : "text-danger")">
                                    @(Model.MonthOverMonthGrowth >= 0 ? "+" : "")@Model.MonthOverMonthGrowth.ToString("F1")%
                                </h4>
                                <p class="text-muted mb-0">Month over Month</p>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="@(Model.YearOverYearGrowth >= 0 ? "text-success" : "text-danger")">
                                    @(Model.YearOverYearGrowth >= 0 ? "+" : "")@Model.YearOverYearGrowth.ToString("F1")%
                                </h4>
                                <p class="text-muted mb-0">Year over Year</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="admin-card h-100">
                <div class="card-header bg-white border-bottom p-4">
                    <h5 class="mb-0 fw-bold">Status Distribution</h5>
                </div>
                <div class="card-body p-4">
                    <canvas id="statusChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row g-4 mb-4">
        <div class="col-lg-8">
            <div class="admin-card h-100">
                <div class="card-header bg-white border-bottom p-4">
                    <h5 class="mb-0 fw-bold">Monthly Revenue Trend</h5>
                </div>
                <div class="card-body p-4">
                    <canvas id="monthlyChart" width="800" height="400"></canvas>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="admin-card h-100">
                <div class="card-header bg-white border-bottom p-4">
                    <h5 class="mb-0 fw-bold">Top Clients by Revenue</h5>
                </div>
                <div class="card-body p-4">
                    @if (Model.ClientData.Any())
                    {
                        @foreach (var client in Model.ClientData.Take(5))
                        {
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div>
                                    <h6 class="mb-1">@client.ClientName</h6>
                                    <small class="text-muted">@client.InvoiceCount invoices</small>
                                </div>
                                <div class="text-end">
                                    <strong>$@client.TotalRevenue.ToString("N2")</strong>
                                    <br>
                                    <small class="text-success">$@client.PaidAmount.ToString("N2") paid</small>
                                </div>
                            </div>
                        }
                    }
                    else
                    {
                        <p class="text-muted text-center">No client data available</p>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row g-4">
        <div class="col-12">
            <div class="admin-card">
                <div class="card-header bg-white border-bottom p-4">
                    <h5 class="mb-0 fw-bold">Recent Invoice Activity</h5>
                </div>
                <div class="card-body p-0">
                    @if (Model.RecentInvoices.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th class="border-0">Invoice</th>
                                        <th class="border-0">Client</th>
                                        <th class="border-0">Amount</th>
                                        <th class="border-0">Status</th>
                                        <th class="border-0">Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var invoice in Model.RecentInvoices)
                                    {
                                        <tr>
                                            <td>
                                                <a asp-action="Details" asp-route-id="@invoice.Id" class="fw-semibold text-decoration-none">
                                                    @invoice.InvoiceNumber
                                                </a>
                                            </td>
                                            <td>@(invoice.Client?.CompanyName ?? "Unknown")</td>
                                            <td class="fw-semibold">$@invoice.TotalAmount.ToString("N2")</td>
                                            <td>
                                                <span class="badge bg-@(invoice.Status == "Paid" ? "success" : invoice.Status == "Pending" ? "warning" : invoice.Status == "Overdue" ? "danger" : "info")">
                                                    @invoice.Status
                                                </span>
                                            </td>
                                            <td>@invoice.IssueDate.ToString("MMM dd, yyyy")</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="p-4 text-center text-muted">
                            <i class="fas fa-file-invoice fa-3x mb-3"></i>
                            <p>No recent invoices found</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Monthly Revenue Chart
        const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
        const monthlyChart = new Chart(monthlyCtx, {
            type: 'line',
            data: {
                labels: @Html.Raw(Json.Serialize(Model.MonthlyData.Select(m => m.Month))),
                datasets: [{
                    label: 'Total Revenue',
                    data: @Html.Raw(Json.Serialize(Model.MonthlyData.Select(m => m.TotalAmount))),
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: 'Paid Revenue',
                    data: @Html.Raw(Json.Serialize(Model.MonthlyData.Select(m => m.PaidAmount))),
                    borderColor: 'rgb(34, 197, 94)',
                    backgroundColor: 'rgba(34, 197, 94, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    title: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '$' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });

        // Status Distribution Chart
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        const statusChart = new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: @Html.Raw(Json.Serialize(Model.StatusData.Select(s => s.Status))),
                datasets: [{
                    data: @Html.Raw(Json.Serialize(Model.StatusData.Select(s => s.Amount))),
                    backgroundColor: [
                        '#22c55e', // Paid - Green
                        '#f59e0b', // Pending - Yellow
                        '#3b82f6', // Partially Paid - Blue
                        '#ef4444'  // Overdue - Red
                    ],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = '$' + context.parsed.toLocaleString();
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed / total) * 100).toFixed(1);
                                return label + ': ' + value + ' (' + percentage + '%)';
                            }
                        }
                    }
                }
            }
        });

        // Export functionality
        function exportAnalytics() {
            // Create a simple CSV export
            const csvData = [
                ['Metric', 'Value'],
                ['Total Invoices', '@Model.TotalInvoices'],
                ['Total Revenue', '$@Model.TotalAmount.ToString("N2")'],
                ['Paid Amount', '$@Model.PaidAmount.ToString("N2")'],
                ['Outstanding Amount', '$@Model.OutstandingAmount.ToString("N2")'],
                ['Collection Rate', '@Model.CollectionRate.ToString("F1")%'],
                ['Average Invoice Amount', '$@Model.AverageInvoiceAmount.ToString("N2")'],
                ['Average Days to Payment', '@Model.AverageDaysToPayment.ToString("F0")'],
                ['Month over Month Growth', '@Model.MonthOverMonthGrowth.ToString("F1")%'],
                ['Year over Year Growth', '@Model.YearOverYearGrowth.ToString("F1")%']
            ];

            const csvContent = csvData.map(row => row.join(',')).join('\n');
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'invoice-analytics-' + new Date().toISOString().split('T')[0] + '.csv';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }

        // Auto-refresh data every 5 minutes
        setInterval(function() {
            location.reload();
        }, 300000);
    </script>
}
