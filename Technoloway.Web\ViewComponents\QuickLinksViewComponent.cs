using Microsoft.AspNetCore.Mvc;
using Technoloway.Core.Entities;
using Technoloway.Core.Interfaces;

namespace Technoloway.Web.ViewComponents;

public class QuickLinksViewComponent : ViewComponent
{
    private readonly IRepository<SiteSetting> _siteSettingRepository;

    public QuickLinksViewComponent(IRepository<SiteSetting> siteSettingRepository)
    {
        _siteSettingRepository = siteSettingRepository;
    }

    public async Task<IViewComponentResult> InvokeAsync()
    {
        try
        {
            // Get quick links settings
            var quickLinksSettings = await _siteSettingRepository.ListAsync(s => s.Group == "QuickLinks" && s.IsPublic);

            var quickLinksInfo = new QuickLinksViewModel
            {
                QuickLinksSettings = quickLinksSettings.ToList()
            };

            return View(quickLinksInfo);
        }
        catch (Exception ex)
        {
            // Log error and return default values
            Console.WriteLine($"Error loading quick links: {ex.Message}");

            var defaultQuickLinks = new QuickLinksViewModel
            {
                QuickLinksSettings = new List<SiteSetting>()
            };

            return View(defaultQuickLinks);
        }
    }
}

public class QuickLinksViewModel
{
    public List<SiteSetting> QuickLinksSettings { get; set; } = new List<SiteSetting>();
}
