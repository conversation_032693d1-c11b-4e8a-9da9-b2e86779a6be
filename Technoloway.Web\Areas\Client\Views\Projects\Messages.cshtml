@model IEnumerable<Technoloway.Core.Entities.Message>
@{
    ViewData["Title"] = "Project Messages";
    var project = ViewBag.Project as Technoloway.Core.Entities.Project;
}

<div class="container-fluid p-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="d-flex align-items-center mb-2">
                        <a asp-action="Details" asp-route-id="@project.Id" class="btn btn-outline-secondary btn-sm me-3">
                            <i class="fas fa-arrow-left me-1"></i> Back to Project
                        </a>
                        <h1 class="mb-0">Project Messages</h1>
                    </div>
                    <p class="text-muted mb-0">
                        <i class="fas fa-project-diagram me-1"></i>
                        @project.Name
                    </p>
                </div>
                <div class="d-flex align-items-center gap-2">
                    <span class="badge bg-success">@Model.Count() Messages</span>
                    <button class="btn btn-outline-primary btn-sm" onclick="scrollToBottom()">
                        <i class="fas fa-arrow-down me-1"></i> Latest
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card chat-container">
                <!-- Messages Area -->
                <div class="card-body p-0">
                    <div class="messages-container" id="messagesContainer">
                        @if (Model.Any())
                        {
                            @foreach (var message in Model)
                            {
                                <div class="message-wrapper @(message.SenderRole == "Client" ? "message-sent" : "message-received")">
                                    <div class="message-bubble @(message.SenderRole == "Client" ? "client" : "admin")">
                                        <div class="message-header">
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm bg-@(message.SenderRole == "Client" ? "primary" : "success") rounded-circle d-flex align-items-center justify-content-center me-2">
                                                    <i class="fas fa-@(message.SenderRole == "Client" ? "user" : "user-tie") text-white"></i>
                                                </div>
                                                <div>
                                                    <h6 class="mb-0">@message.SenderName</h6>
                                                    <small class="text-muted">@message.SenderRole</small>
                                                </div>
                                            </div>
                                            <small class="text-muted">@message.CreatedAt.ToString("MMM dd, HH:mm")</small>
                                        </div>
                                        <div class="message-content">
                                            @message.Content
                                        </div>
                                        @if (message.IsRead)
                                        {
                                            <div class="message-status">
                                                <small class="text-muted">
                                                    <i class="fas fa-check-double"></i> Read
                                                </small>
                                            </div>
                                        }
                                    </div>
                                </div>
                            }
                        }
                        else
                        {
                            <div class="empty-messages text-center py-5">
                                <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No messages yet</h5>
                                <p class="text-muted">Start the conversation by sending your first message!</p>
                            </div>
                        }
                    </div>
                </div>

                <!-- Message Input -->
                <div class="card-footer bg-light border-0">
                    <form asp-action="SendMessage" method="post" id="messageForm" class="d-flex gap-2">
                        <input type="hidden" name="projectId" value="@project.Id" />
                        <div class="flex-grow-1">
                            <div class="input-group">
                                <textarea name="content" id="messageInput" class="form-control" rows="1"
                                         placeholder="Type your message..." required
                                         style="resize: none; min-height: 42px; max-height: 120px;"></textarea>
                                <button type="button" class="btn btn-outline-secondary" onclick="toggleEmojiPicker()">
                                    <i class="fas fa-smile"></i>
                                </button>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary" id="sendButton">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </form>

                    <!-- Typing Indicator -->
                    <div id="typingIndicator" class="typing-indicator mt-2" style="display: none;">
                        <small class="text-muted">
                            <i class="fas fa-circle typing-dot"></i>
                            <i class="fas fa-circle typing-dot"></i>
                            <i class="fas fa-circle typing-dot"></i>
                            Team is typing...
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.chat-container {
    height: calc(100vh - 200px);
    display: flex;
    flex-direction: column;
}

.messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.message-wrapper {
    margin-bottom: 1.5rem;
    display: flex;
}

.message-wrapper.message-sent {
    justify-content: flex-end;
}

.message-wrapper.message-received {
    justify-content: flex-start;
}

.message-bubble {
    max-width: 70%;
    padding: 1rem 1.25rem;
    border-radius: 1.5rem;
    position: relative;
    word-wrap: break-word;
    animation: messageSlideIn 0.3s ease-out;
}

.message-bubble.client {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    border-bottom-right-radius: 0.5rem;
}

.message-bubble.admin {
    background: var(--card-bg);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-bottom-left-radius: 0.5rem;
    box-shadow: var(--shadow-sm);
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.message-bubble.admin .message-header {
    border-bottom-color: var(--border-color);
}

.message-content {
    line-height: 1.5;
    margin-bottom: 0.5rem;
}

.message-status {
    text-align: right;
    margin-top: 0.5rem;
}

.message-bubble.admin .message-status {
    color: var(--text-secondary);
}

.empty-messages {
    margin: 2rem 0;
}

.typing-indicator {
    animation: fadeIn 0.3s ease-in;
}

.typing-dot {
    animation: typingDots 1.4s infinite;
    font-size: 0.5rem;
    margin: 0 1px;
}

.typing-dot:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
    animation-delay: 0.4s;
}

@@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@@keyframes typingDots {
    0%, 60%, 100% {
        opacity: 0.3;
    }
    30% {
        opacity: 1;
    }
}

@@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Auto-resize textarea */
#messageInput {
    transition: height 0.2s ease;
}

/* Scrollbar styling */
.messages-container::-webkit-scrollbar {
    width: 6px;
}

.messages-container::-webkit-scrollbar-track {
    background: transparent;
}

.messages-container::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const messageInput = document.getElementById('messageInput');
    const messageForm = document.getElementById('messageForm');
    const messagesContainer = document.getElementById('messagesContainer');

    // Auto-resize textarea
    messageInput.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = Math.min(this.scrollHeight, 120) + 'px';
    });

    // Handle Enter key (send message)
    messageInput.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            messageForm.submit();
        }
    });

    // Handle form submission
    messageForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const content = messageInput.value.trim();
        if (!content) return;

        const sendButton = document.getElementById('sendButton');
        const originalContent = sendButton.innerHTML;

        // Show loading state
        sendButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        sendButton.disabled = true;

        // Send message via AJAX
        fetch(this.action, {
            method: 'POST',
            body: new FormData(this),
            headers: {
                'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Add message to UI immediately
                addMessageToUI(data.messageData);
                messageInput.value = '';
                messageInput.style.height = 'auto';
                scrollToBottom();
            } else {
                alert('Error sending message: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error sending message. Please try again.');
        })
        .finally(() => {
            sendButton.innerHTML = originalContent;
            sendButton.disabled = false;
            messageInput.focus();
        });
    });

    // Scroll to bottom on load
    scrollToBottom();

    // Focus on input
    messageInput.focus();
});

function addMessageToUI(messageData) {
    const messagesContainer = document.getElementById('messagesContainer');

    const messageWrapper = document.createElement('div');
    messageWrapper.className = 'message-wrapper message-sent';

    messageWrapper.innerHTML = `
        <div class="message-bubble client">
            <div class="message-header">
                <div class="d-flex align-items-center">
                    <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                        <i class="fas fa-user text-white"></i>
                    </div>
                    <div>
                        <h6 class="mb-0">${messageData.senderName}</h6>
                        <small class="text-white-50">Client</small>
                    </div>
                </div>
                <small class="text-white-50">${messageData.createdAt}</small>
            </div>
            <div class="message-content">
                ${messageData.content}
            </div>
        </div>
    `;

    messagesContainer.appendChild(messageWrapper);
}

function scrollToBottom() {
    const messagesContainer = document.getElementById('messagesContainer');
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

function toggleEmojiPicker() {
    // Placeholder for emoji picker functionality
    console.log('Emoji picker would open here');
}

// Simulate typing indicator (in real app, this would be triggered by WebSocket)
function showTypingIndicator() {
    document.getElementById('typingIndicator').style.display = 'block';
    setTimeout(() => {
        document.getElementById('typingIndicator').style.display = 'none';
    }, 3000);
}
</script>
