using Microsoft.AspNetCore.Http;
using System.ComponentModel.DataAnnotations;

namespace Technoloway.Core.Models;

public class DataUploadViewModel
{
    [Required(ErrorMessage = "Please select a file to upload")]
    public IFormFile? UploadFile { get; set; }

    [Required(ErrorMessage = "Please select a target entity")]
    public string TargetEntity { get; set; } = string.Empty;

    [Required(ErrorMessage = "Please select an operation")]
    public string Operation { get; set; } = string.Empty;

    public bool HasHeaders { get; set; } = true;
    public string? Delimiter { get; set; } = ",";
    public Dictionary<string, string> FieldMappings { get; set; } = new();
    public bool ValidateData { get; set; } = true;
    public bool CreateBackup { get; set; } = true;
}

public class FilePreviewModel
{
    public string FileName { get; set; } = string.Empty;
    public string FileType { get; set; } = string.Empty;
    public long FileSizeBytes { get; set; }
    public int TotalRows { get; set; }
    public List<string> Headers { get; set; } = new();
    public List<Dictionary<string, object>> SampleData { get; set; } = new();
    public List<string> DetectedIssues { get; set; } = new();
}

public class FieldMappingModel
{
    public string SourceField { get; set; } = string.Empty;
    public string TargetField { get; set; } = string.Empty;
    public string DataType { get; set; } = string.Empty;
    public bool IsRequired { get; set; }
    public bool IsIdentity { get; set; }
    public string? DefaultValue { get; set; }
    public string? ValidationRule { get; set; }
}

public class DatabaseEntityInfo
{
    public string EntityName { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public List<EntityFieldInfo> Fields { get; set; } = new();
    public bool SupportsInsert { get; set; } = true;
    public bool SupportsUpdate { get; set; } = true;
    public bool SupportsUpsert { get; set; } = true;
    public bool SupportsTruncate { get; set; } = true;
}

public class EntityFieldInfo
{
    public string FieldName { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public string DataType { get; set; } = string.Empty;
    public bool IsRequired { get; set; }
    public bool IsIdentity { get; set; }
    public bool IsPrimaryKey { get; set; }
    public bool IsForeignKey { get; set; }
    public int? MaxLength { get; set; }
    public string? DefaultValue { get; set; }
    public List<string>? AllowedValues { get; set; }
}

public class DataUploadResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public int RecordsProcessed { get; set; }
    public int RecordsSuccessful { get; set; }
    public int RecordsFailed { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public string? LogId { get; set; }
    public bool CanRollback { get; set; }
}

public class ValidationResult
{
    public bool IsValid { get; set; }
    public List<ValidationError> Errors { get; set; } = new();
    public List<ValidationWarning> Warnings { get; set; } = new();
}

public class ValidationError
{
    public int RowNumber { get; set; }
    public string FieldName { get; set; } = string.Empty;
    public string ErrorMessage { get; set; } = string.Empty;
    public string? Value { get; set; }
}

public class ValidationWarning
{
    public int RowNumber { get; set; }
    public string FieldName { get; set; } = string.Empty;
    public string WarningMessage { get; set; } = string.Empty;
    public string? Value { get; set; }
}

public enum DataUploadOperation
{
    Insert,
    Update,
    Upsert,
    TruncateAndInsert
}

public enum DataUploadStatus
{
    Pending,
    Processing,
    Completed,
    Failed,
    RolledBack
}

public static class SupportedFileTypes
{
    public static readonly Dictionary<string, string[]> FileExtensions = new()
    {
        { "CSV", new[] { ".csv" } },
        { "Excel", new[] { ".xlsx", ".xls" } },
        { "JSON", new[] { ".json" } },
        { "XML", new[] { ".xml" } },
        { "SQL", new[] { ".sql" } },
        { "TSV", new[] { ".tsv", ".tab" } }
    };

    public static readonly Dictionary<string, string> MimeTypes = new()
    {
        { ".csv", "text/csv" },
        { ".xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" },
        { ".xls", "application/vnd.ms-excel" },
        { ".json", "application/json" },
        { ".xml", "application/xml" },
        { ".sql", "text/plain" },
        { ".tsv", "text/tab-separated-values" },
        { ".tab", "text/tab-separated-values" }
    };
}
