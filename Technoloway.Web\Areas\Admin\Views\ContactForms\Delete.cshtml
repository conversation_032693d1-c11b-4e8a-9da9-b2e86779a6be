@model Technoloway.Core.Entities.ContactForm

@{
    ViewData["Title"] = "Delete Contact Form";
    Layout = "~/Areas/Admin/Views/Shared/_AdminLayout.cshtml";
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Delete Contact Form</h1>
    <div class="d-sm-flex">
        <a asp-action="Index" class="btn btn-secondary btn-sm me-2">
            <i class="fas fa-arrow-left me-2"></i>Back to List
        </a>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header bg-danger text-white py-3">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-exclamation-triangle me-2"></i>Confirm Deletion
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-warning" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Warning!</strong> Are you sure you want to delete this contact form submission? This action cannot be undone.
                </div>

                <div class="border rounded p-4 bg-light mb-4">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6 class="fw-bold">From:</h6>
                            <p class="mb-1"><strong>@Model.Name</strong></p>
                            <p class="mb-1">
                                <i class="fas fa-envelope me-2"></i>@Model.Email
                            </p>
                            @if (!string.IsNullOrEmpty(Model.Phone))
                            {
                                <p class="mb-1">
                                    <i class="fas fa-phone me-2"></i>@Model.Phone
                                </p>
                            }
                        </div>
                        <div class="col-md-6">
                            <h6 class="fw-bold">Date:</h6>
                            <p class="mb-1">
                                <i class="fas fa-calendar me-2"></i>
                                @Model.CreatedAt.ToString("MMMM dd, yyyy 'at' h:mm tt")
                            </p>
                            <h6 class="fw-bold mt-3">Status:</h6>
                            <span class="badge @(Model.Status switch {
                                "New" => "bg-primary",
                                "Read" => "bg-info",
                                "Replied" => "bg-success",
                                "Archived" => "bg-secondary",
                                _ => "bg-primary"
                            })">@Model.Status</span>
                            @if (!Model.IsRead)
                            {
                                <span class="badge bg-warning ms-1">Unread</span>
                            }
                        </div>
                    </div>

                    <div class="mb-3">
                        <h6 class="fw-bold">Subject:</h6>
                        <p class="mb-0">@Model.Subject</p>
                    </div>

                    <div class="mb-3">
                        <h6 class="fw-bold">Message:</h6>
                        <div class="border rounded p-3 bg-white">
                            <p class="mb-0" style="white-space: pre-wrap;">@Model.Message</p>
                        </div>
                    </div>
                </div>

                <form asp-action="Delete" method="post">
                    <input type="hidden" asp-for="Id" />
                    <div class="d-flex justify-content-between">
                        <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-secondary">
                            <i class="fas fa-eye me-2"></i>View Details Instead
                        </a>
                        <div>
                            <a asp-action="Index" class="btn btn-outline-secondary me-2">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-danger" onclick="return confirm('Are you absolutely sure you want to delete this contact form submission?')">
                                <i class="fas fa-trash me-2"></i>Delete Permanently
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Alternative Actions Card -->
        <div class="card shadow mt-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Alternative Actions</h6>
            </div>
            <div class="card-body">
                <p class="text-muted mb-3">Instead of deleting, you might want to:</p>
                <div class="d-flex flex-wrap gap-2">
                    <button class="btn btn-outline-warning btn-sm" onclick="archiveMessage(@Model.Id)">
                        <i class="fas fa-archive me-2"></i>Archive Message
                    </button>
                    <a href="mailto:@Model.Email?subject=Re: @Model.Subject" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-reply me-2"></i>Reply First
                    </a>
                    <button class="btn btn-outline-info btn-sm" onclick="markAsReplied(@Model.Id)">
                        <i class="fas fa-check me-2"></i>Mark as Replied
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function archiveMessage(id) {
            $.post('@Url.Action("UpdateStatus")', { id: id, status: 'Archived' })
                .done(function(data) {
                    if (data.success) {
                        window.location.href = '@Url.Action("Index")';
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .fail(function() {
                    alert('Error archiving message');
                });
        }

        function markAsReplied(id) {
            $.post('@Url.Action("UpdateStatus")', { id: id, status: 'Replied' })
                .done(function(data) {
                    if (data.success) {
                        window.location.href = '@Url.Action("Index")';
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .fail(function() {
                    alert('Error updating status');
                });
        }
    </script>
}
