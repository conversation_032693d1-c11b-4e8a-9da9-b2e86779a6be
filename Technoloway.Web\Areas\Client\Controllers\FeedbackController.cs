using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Technoloway.Core.Interfaces;
using Technoloway.Core.Entities;

namespace Technoloway.Web.Areas.Client.Controllers;

[Area("Client")]
[Authorize(Policy = "RequireClient")]
public class FeedbackController : Controller
{
    private readonly IRepository<Feedback> _feedbackRepository;
    private readonly IRepository<Core.Entities.Client> _clientRepository;
    private readonly IRepository<Project> _projectRepository;
    private readonly UserManager<IdentityUser> _userManager;

    public FeedbackController(
        IRepository<Feedback> feedbackRepository,
        IRepository<Core.Entities.Client> clientRepository,
        IRepository<Project> projectRepository,
        UserManager<IdentityUser> userManager)
    {
        _feedbackRepository = feedbackRepository;
        _clientRepository = clientRepository;
        _projectRepository = projectRepository;
        _userManager = userManager;
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Submit(string subject, string message, string feedbackType, int? rating, int? projectId)
    {
        try
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return Json(new { success = false, message = "User not authenticated" });
            }

            var client = (await _clientRepository.ListAsync(c => c.UserId == user.Id)).FirstOrDefault();
            if (client == null)
            {
                return Json(new { success = false, message = "Client profile not found" });
            }

            // Validation
            if (string.IsNullOrWhiteSpace(subject))
            {
                return Json(new { success = false, message = "Subject is required" });
            }

            if (string.IsNullOrWhiteSpace(message))
            {
                return Json(new { success = false, message = "Message is required" });
            }

            if (string.IsNullOrWhiteSpace(feedbackType))
            {
                return Json(new { success = false, message = "Feedback type is required" });
            }

            if (subject.Length > 200)
            {
                return Json(new { success = false, message = "Subject cannot exceed 200 characters" });
            }

            if (message.Length > 2000)
            {
                return Json(new { success = false, message = "Message cannot exceed 2000 characters" });
            }

            if (message.Length < 10)
            {
                return Json(new { success = false, message = "Message must be at least 10 characters" });
            }

            if (rating.HasValue && (rating < 1 || rating > 5))
            {
                return Json(new { success = false, message = "Rating must be between 1 and 5" });
            }

            // Validate project if provided
            Project? project = null;
            if (projectId.HasValue)
            {
                project = await _projectRepository.GetAll()
                    .Where(p => p.Id == projectId.Value && p.ClientId == client.Id && !p.IsDeleted)
                    .FirstOrDefaultAsync();

                if (project == null)
                {
                    return Json(new { success = false, message = "Invalid project selected" });
                }
            }

            // Determine priority based on feedback type
            string priority = feedbackType switch
            {
                "Bug Report" => "High",
                "Complaint" => "High",
                "Feature Request" => "Medium",
                "General Feedback" => "Low",
                "Compliment" => "Low",
                _ => "Medium"
            };

            // Create feedback
            var feedback = new Feedback
            {
                Subject = subject.Trim(),
                Message = message.Trim(),
                FeedbackType = feedbackType,
                Rating = rating,
                ClientName = client.ContactName,
                ClientEmail = client.ContactEmail,
                Priority = priority,
                Status = "New",
                IsRead = false,
                ClientId = client.Id,
                ProjectId = projectId,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            await _feedbackRepository.AddAsync(feedback);

            return Json(new
            {
                success = true,
                message = "Thank you for your feedback! We'll review it and get back to you soon.",
                feedbackId = feedback.Id
            });
        }
        catch (Exception ex)
        {
            // Log the error (in a real application, use proper logging)
            Console.WriteLine($"Error submitting feedback: {ex.Message}");

            return Json(new { success = false, message = "An error occurred while submitting your feedback. Please try again." });
        }
    }

    [HttpGet]
    public async Task<IActionResult> Index()
    {
        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return Challenge();
        }

        var client = (await _clientRepository.ListAsync(c => c.UserId == user.Id)).FirstOrDefault();
        if (client == null)
        {
            return RedirectToAction("Create", "Profile");
        }

        var feedbacks = await _feedbackRepository.GetAll()
            .Where(f => f.ClientId == client.Id && !f.IsDeleted)
            .Include(f => f.Project)
            .OrderByDescending(f => f.CreatedAt)
            .ToListAsync();

        ViewBag.TotalFeedbacks = feedbacks.Count;
        ViewBag.PendingFeedbacks = feedbacks.Count(f => f.Status == "New" || f.Status == "In Review" || f.Status == "In Progress");
        ViewBag.ResolvedFeedbacks = feedbacks.Count(f => f.Status == "Resolved" || f.Status == "Closed");

        return View(feedbacks);
    }

    [HttpGet]
    public async Task<IActionResult> Details(int id)
    {
        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return Challenge();
        }

        var client = (await _clientRepository.ListAsync(c => c.UserId == user.Id)).FirstOrDefault();
        if (client == null)
        {
            return RedirectToAction("Create", "Profile");
        }

        var feedback = await _feedbackRepository.GetAll()
            .Where(f => f.Id == id && f.ClientId == client.Id && !f.IsDeleted)
            .Include(f => f.Project)
            .FirstOrDefaultAsync();

        if (feedback == null)
        {
            return NotFound();
        }

        return View(feedback);
    }

    [HttpGet]
    public async Task<IActionResult> GetClientProjects()
    {
        try
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return Json(new { success = false, projects = new List<object>() });
            }

            var client = (await _clientRepository.ListAsync(c => c.UserId == user.Id)).FirstOrDefault();
            if (client == null)
            {
                return Json(new { success = false, projects = new List<object>() });
            }

            var projects = await _projectRepository.ListAsync(p => p.ClientId == client.Id && !p.IsDeleted);

            var projectList = projects.Select(p => new { id = p.Id, name = p.Name }).ToList();

            return Json(new { success = true, projects = projectList });
        }
        catch
        {
            return Json(new { success = false, projects = new List<object>() });
        }
    }
}
