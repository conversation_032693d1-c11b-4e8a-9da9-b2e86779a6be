using Technoloway.Core.Entities;

namespace Technoloway.Web.Areas.Admin.ViewModels
{
    public class JobStatsViewModel
    {
        public List<JobListing> JobListings { get; set; } = new List<JobListing>();
        public Dictionary<int, int> JobApplicationCounts { get; set; } = new Dictionary<int, int>();
        public int TotalApplications { get; set; }
        public int PendingApplications { get; set; }
        public int ReviewedApplications { get; set; }
        public int HiredApplications { get; set; }
        public int RejectedApplications { get; set; }
    }
}
