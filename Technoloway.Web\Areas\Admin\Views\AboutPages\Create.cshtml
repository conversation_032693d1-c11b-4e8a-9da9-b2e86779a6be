@model Technoloway.Web.Areas.Admin.Models.AboutPageViewModel

@{
    ViewData["Title"] = "Create About Page";
    ViewData["PageTitle"] = "Create About Page";
    ViewData["PageDescription"] = "Create a new about page with content and sections";
}

@section Styles {
    <style>
        .form-card {
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            margin-bottom: 1.5rem;
        }

        .form-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 0.5rem 0.5rem 0 0;
        }

        .form-body {
            padding: 1.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.5rem;
        }

        .form-control, .form-select {
            border: 1px solid #e1e5e9;
            border-radius: 0.375rem;
            padding: 0.75rem;
            transition: all 0.15s ease-in-out;
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(102, 126, 234, 0.25);
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 0.25rem 0.5rem rgba(102, 126, 234, 0.35);
        }

        .char-counter {
            font-size: 0.875rem;
            color: #6c757d;
            text-align: right;
            margin-top: 0.25rem;
        }

        .char-counter.warning {
            color: #fd7e14;
        }

        .char-counter.danger {
            color: #dc3545;
        }

        .nav-tabs .nav-link {
            border: none;
            border-bottom: 2px solid transparent;
            color: #6c757d;
            font-weight: 500;
        }

        .nav-tabs .nav-link.active {
            border-bottom-color: #667eea;
            color: #667eea;
            background: none;
        }

        .tab-content {
            padding-top: 1.5rem;
        }

        .required {
            color: #dc3545;
        }

        .info-box {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }

        .info-box .info-icon {
            color: #1976d2;
            font-size: 1.25rem;
            margin-right: 0.5rem;
        }
    </style>
}

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-1">
                <i class="fas fa-plus-circle me-2 text-primary"></i>Create About Page
            </h1>
            <p class="text-muted mb-0">Create a new about page with comprehensive content sections</p>
        </div>
        <div class="btn-group">
            <a asp-action="Index" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to List
            </a>
        </div>
    </div>

    <!-- Info Box -->
    <div class="info-box">
        <div class="d-flex align-items-start">
            <i class="fas fa-info-circle info-icon"></i>
            <div>
                <h6 class="mb-2">About Page Creation Guide</h6>
                <p class="mb-2">Create a comprehensive about page that tells your company's story. Fill in the required fields marked with <span class="required">*</span> to get started.</p>
                <ul class="mb-0 small">
                    <li>Use the <strong>Hero Section</strong> to create an impactful first impression</li>
                    <li>Tell your company's story in the <strong>Story Section</strong> with rich content</li>
                    <li>Add impressive <strong>Statistics</strong> to build credibility</li>
                    <li>Define your <strong>Mission, Vision & Values</strong> to connect with visitors</li>
                    <li>Include a compelling <strong>Call to Action</strong> to drive engagement</li>
                    <li>Add custom <strong>Sections</strong> for additional content like team values or features</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Validation Summary -->
    @if (!ViewData.ModelState.IsValid)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <h6><i class="fas fa-exclamation-triangle me-2"></i>Please correct the following errors:</h6>
            <div asp-validation-summary="All" class="mb-0"></div>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <form asp-action="Create" method="post" id="aboutPageForm">
        <!-- Navigation Tabs -->
        <ul class="nav nav-tabs" id="aboutPageTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="basic-tab" data-bs-toggle="tab" data-bs-target="#basic" type="button" role="tab">
                    <i class="fas fa-info-circle me-1"></i>Basic Info
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="hero-tab" data-bs-toggle="tab" data-bs-target="#hero" type="button" role="tab">
                    <i class="fas fa-star me-1"></i>Hero Section
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="story-tab" data-bs-toggle="tab" data-bs-target="#story" type="button" role="tab">
                    <i class="fas fa-book me-1"></i>Story
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="stats-tab" data-bs-toggle="tab" data-bs-target="#stats" type="button" role="tab">
                    <i class="fas fa-chart-bar me-1"></i>Statistics
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="mission-tab" data-bs-toggle="tab" data-bs-target="#mission" type="button" role="tab">
                    <i class="fas fa-bullseye me-1"></i>Mission & Vision
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="cta-tab" data-bs-toggle="tab" data-bs-target="#cta" type="button" role="tab">
                    <i class="fas fa-mouse-pointer me-1"></i>Call to Action
                </button>
            </li>
        </ul>

        <div class="tab-content" id="aboutPageTabContent">
            <!-- Basic Information Tab -->
            <div class="tab-pane fade show active" id="basic" role="tabpanel">
                <div class="form-card">
                    <div class="form-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>Basic Information
                        </h5>
                    </div>
                    <div class="form-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Title" class="form-label">
                                        Page Title <span class="required">*</span>
                                    </label>
                                    <input asp-for="Title" class="form-control" maxlength="100" placeholder="About Us" />
                                    <span asp-validation-for="Title" class="text-danger"></span>
                                    <div class="char-counter">
                                        <span id="titleCounter">0</span>/100 characters
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="IsActive" class="form-label">Status</label>
                                    <select asp-for="IsActive" class="form-select">
                                        <option value="true">Active</option>
                                        <option value="false">Inactive</option>
                                    </select>
                                    <span asp-validation-for="IsActive" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label asp-for="MetaDescription" class="form-label">Meta Description</label>
                            <textarea asp-for="MetaDescription" class="form-control" rows="3" maxlength="200"
                                      placeholder="Brief description for search engines (recommended: 150-160 characters)"></textarea>
                            <span asp-validation-for="MetaDescription" class="text-danger"></span>
                            <div class="char-counter">
                                <span id="metaDescCounter">0</span>/200 characters
                            </div>
                        </div>

                        <div class="form-group">
                            <label asp-for="MetaKeywords" class="form-label">Meta Keywords</label>
                            <input asp-for="MetaKeywords" class="form-control" maxlength="500"
                                   placeholder="Comma-separated keywords for SEO" />
                            <span asp-validation-for="MetaKeywords" class="text-danger"></span>
                            <div class="char-counter">
                                <span id="metaKeywordsCounter">0</span>/500 characters
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Hero Section Tab -->
            <div class="tab-pane fade" id="hero" role="tabpanel">
                <div class="form-card">
                    <div class="form-header">
                        <h5 class="mb-0">
                            <i class="fas fa-star me-2"></i>Hero Section
                        </h5>
                    </div>
                    <div class="form-body">
                        <div class="form-group">
                            <label asp-for="HeroTitle" class="form-label">
                                Hero Title <span class="required">*</span>
                            </label>
                            <input asp-for="HeroTitle" class="form-control" maxlength="200" placeholder="About Technoloway" />
                            <span asp-validation-for="HeroTitle" class="text-danger"></span>
                            <div class="char-counter">
                                <span id="heroTitleCounter">0</span>/200 characters
                            </div>
                        </div>

                        <div class="form-group">
                            <label asp-for="HeroSubtitle" class="form-label">
                                Hero Subtitle <span class="required">*</span>
                            </label>
                            <textarea asp-for="HeroSubtitle" class="form-control" rows="3" maxlength="500"
                                      placeholder="Learn about our mission, values, and the passionate team behind our innovative software solutions."></textarea>
                            <span asp-validation-for="HeroSubtitle" class="text-danger"></span>
                            <div class="char-counter">
                                <span id="heroSubtitleCounter">0</span>/500 characters
                            </div>
                        </div>

                        <div class="form-group">
                            <label asp-for="HeroImageUrl" class="form-label">Hero Image URL</label>
                            <input asp-for="HeroImageUrl" class="form-control" maxlength="500"
                                   placeholder="https://example.com/hero-image.jpg" />
                            <span asp-validation-for="HeroImageUrl" class="text-danger"></span>
                            <small class="form-text text-muted">
                                Recommended size: 1920x1080px. Leave empty to use default image.
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Story Section Tab -->
            <div class="tab-pane fade" id="story" role="tabpanel">
                <div class="form-card">
                    <div class="form-header">
                        <h5 class="mb-0">
                            <i class="fas fa-book me-2"></i>Story Section
                        </h5>
                    </div>
                    <div class="form-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="StoryTitle" class="form-label">
                                        Story Title <span class="required">*</span>
                                    </label>
                                    <input asp-for="StoryTitle" class="form-control" maxlength="100" placeholder="Our Story" />
                                    <span asp-validation-for="StoryTitle" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="StoryImageUrl" class="form-label">Story Image URL</label>
                                    <input asp-for="StoryImageUrl" class="form-control" maxlength="500"
                                           placeholder="https://example.com/story-image.jpg" />
                                    <span asp-validation-for="StoryImageUrl" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label asp-for="StorySubtitle" class="form-label">
                                Story Subtitle <span class="required">*</span>
                            </label>
                            <textarea asp-for="StorySubtitle" class="form-control" rows="2" maxlength="300"
                                      placeholder="Technoloway was founded with a mission to deliver innovative software solutions."></textarea>
                            <span asp-validation-for="StorySubtitle" class="text-danger"></span>
                            <div class="char-counter">
                                <span id="storySubtitleCounter">0</span>/300 characters
                            </div>
                        </div>

                        <div class="form-group">
                            <label asp-for="StoryContent" class="form-label">
                                Story Content <span class="required">*</span>
                            </label>
                            <textarea asp-for="StoryContent" class="form-control" rows="8"
                                      placeholder="Tell your company's story here. You can use HTML tags for formatting."></textarea>
                            <span asp-validation-for="StoryContent" class="text-danger"></span>
                            <small class="form-text text-muted">
                                You can use HTML tags for formatting. Use &lt;p&gt; for paragraphs, &lt;strong&gt; for bold text, etc.
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics Tab -->
            <div class="tab-pane fade" id="stats" role="tabpanel">
                <div class="form-card">
                    <div class="form-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>Statistics Section
                        </h5>
                    </div>
                    <div class="form-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Stat1Number" class="form-label">Statistic 1 Number</label>
                                    <input asp-for="Stat1Number" class="form-control" maxlength="50" placeholder="8+" />
                                    <span asp-validation-for="Stat1Number" class="text-danger"></span>
                                </div>
                                <div class="form-group">
                                    <label asp-for="Stat1Label" class="form-label">Statistic 1 Label</label>
                                    <input asp-for="Stat1Label" class="form-control" maxlength="100" placeholder="Years Experience" />
                                    <span asp-validation-for="Stat1Label" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Stat2Number" class="form-label">Statistic 2 Number</label>
                                    <input asp-for="Stat2Number" class="form-control" maxlength="50" placeholder="100+" />
                                    <span asp-validation-for="Stat2Number" class="text-danger"></span>
                                </div>
                                <div class="form-group">
                                    <label asp-for="Stat2Label" class="form-label">Statistic 2 Label</label>
                                    <input asp-for="Stat2Label" class="form-control" maxlength="100" placeholder="Projects Completed" />
                                    <span asp-validation-for="Stat2Label" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Stat3Number" class="form-label">Statistic 3 Number</label>
                                    <input asp-for="Stat3Number" class="form-control" maxlength="50" placeholder="50+" />
                                    <span asp-validation-for="Stat3Number" class="text-danger"></span>
                                </div>
                                <div class="form-group">
                                    <label asp-for="Stat3Label" class="form-label">Statistic 3 Label</label>
                                    <input asp-for="Stat3Label" class="form-control" maxlength="100" placeholder="Happy Clients" />
                                    <span asp-validation-for="Stat3Label" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Stat4Number" class="form-label">Statistic 4 Number</label>
                                    <input asp-for="Stat4Number" class="form-control" maxlength="50" placeholder="24/7" />
                                    <span asp-validation-for="Stat4Number" class="text-danger"></span>
                                </div>
                                <div class="form-group">
                                    <label asp-for="Stat4Label" class="form-label">Statistic 4 Label</label>
                                    <input asp-for="Stat4Label" class="form-control" maxlength="100" placeholder="Support Available" />
                                    <span asp-validation-for="Stat4Label" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Mission & Vision Tab -->
            <div class="tab-pane fade" id="mission" role="tabpanel">
                <div class="form-card">
                    <div class="form-header">
                        <h5 class="mb-0">
                            <i class="fas fa-bullseye me-2"></i>Mission, Vision & Values
                        </h5>
                    </div>
                    <div class="form-body">
                        <div class="form-group">
                            <label asp-for="MissionTitle" class="form-label">Mission Title</label>
                            <input asp-for="MissionTitle" class="form-control" maxlength="100" placeholder="Our Mission" />
                            <span asp-validation-for="MissionTitle" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="MissionContent" class="form-label">Mission Content</label>
                            <textarea asp-for="MissionContent" class="form-control" rows="4"
                                      placeholder="Describe your company's mission and purpose."></textarea>
                            <span asp-validation-for="MissionContent" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="VisionTitle" class="form-label">Vision Title</label>
                            <input asp-for="VisionTitle" class="form-control" maxlength="100" placeholder="Our Vision" />
                            <span asp-validation-for="VisionTitle" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="VisionContent" class="form-label">Vision Content</label>
                            <textarea asp-for="VisionContent" class="form-control" rows="4"
                                      placeholder="Describe your company's vision for the future."></textarea>
                            <span asp-validation-for="VisionContent" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="ValuesTitle" class="form-label">Values Title</label>
                            <input asp-for="ValuesTitle" class="form-control" maxlength="100" placeholder="Our Values" />
                            <span asp-validation-for="ValuesTitle" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="ValuesContent" class="form-label">Values Content</label>
                            <textarea asp-for="ValuesContent" class="form-control" rows="4"
                                      placeholder="Describe your company's core values and principles."></textarea>
                            <span asp-validation-for="ValuesContent" class="text-danger"></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Call to Action Tab -->
            <div class="tab-pane fade" id="cta" role="tabpanel">
                <div class="form-card">
                    <div class="form-header">
                        <h5 class="mb-0">
                            <i class="fas fa-mouse-pointer me-2"></i>Call to Action Section
                        </h5>
                    </div>
                    <div class="form-body">
                        <div class="form-group">
                            <label asp-for="CtaTitle" class="form-label">CTA Title</label>
                            <input asp-for="CtaTitle" class="form-control" maxlength="100" placeholder="Ready to Work With Us?" />
                            <span asp-validation-for="CtaTitle" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="CtaSubtitle" class="form-label">CTA Subtitle</label>
                            <textarea asp-for="CtaSubtitle" class="form-control" rows="3" maxlength="300"
                                      placeholder="Let's discuss how we can help your business succeed with innovative software solutions."></textarea>
                            <span asp-validation-for="CtaSubtitle" class="text-danger"></span>
                            <div class="char-counter">
                                <span id="ctaSubtitleCounter">0</span>/300 characters
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="CtaPrimaryButtonText" class="form-label">Primary Button Text</label>
                                    <input asp-for="CtaPrimaryButtonText" class="form-control" maxlength="50" placeholder="Get Started" />
                                    <span asp-validation-for="CtaPrimaryButtonText" class="text-danger"></span>
                                </div>
                                <div class="form-group">
                                    <label asp-for="CtaPrimaryButtonUrl" class="form-label">Primary Button URL</label>
                                    <input asp-for="CtaPrimaryButtonUrl" class="form-control" maxlength="200" placeholder="/contact" />
                                    <span asp-validation-for="CtaPrimaryButtonUrl" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="CtaSecondaryButtonText" class="form-label">Secondary Button Text</label>
                                    <input asp-for="CtaSecondaryButtonText" class="form-control" maxlength="50" placeholder="View Portfolio" />
                                    <span asp-validation-for="CtaSecondaryButtonText" class="text-danger"></span>
                                </div>
                                <div class="form-group">
                                    <label asp-for="CtaSecondaryButtonUrl" class="form-label">Secondary Button URL</label>
                                    <input asp-for="CtaSecondaryButtonUrl" class="form-control" maxlength="200" placeholder="/projects" />
                                    <span asp-validation-for="CtaSecondaryButtonUrl" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="d-flex justify-content-between align-items-center mt-4">
            <div>
                <a asp-action="Index" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>Cancel
                </a>
            </div>
            <div>
                <button type="submit" class="btn btn-primary btn-lg">
                    <i class="fas fa-save me-2"></i>Create About Page
                </button>
            </div>
        </div>
    </form>
</div>

@section Scripts {
    <script>
        // Character counters
        function setupCharCounters() {
            const counters = [
                { input: '#Title', counter: '#titleCounter', max: 100 },
                { input: '#MetaDescription', counter: '#metaDescCounter', max: 200 },
                { input: '#MetaKeywords', counter: '#metaKeywordsCounter', max: 500 },
                { input: '#HeroTitle', counter: '#heroTitleCounter', max: 200 },
                { input: '#HeroSubtitle', counter: '#heroSubtitleCounter', max: 500 },
                { input: '#StorySubtitle', counter: '#storySubtitleCounter', max: 300 },
                { input: '#CtaSubtitle', counter: '#ctaSubtitleCounter', max: 300 }
            ];

            counters.forEach(item => {
                const input = document.querySelector(item.input);
                const counter = document.querySelector(item.counter);

                if (input && counter) {
                    function updateCounter() {
                        const length = input.value.length;
                        counter.textContent = length;

                        const counterElement = counter.parentElement;
                        counterElement.classList.remove('warning', 'danger');

                        if (length > item.max * 0.9) {
                            counterElement.classList.add('danger');
                        } else if (length > item.max * 0.8) {
                            counterElement.classList.add('warning');
                        }
                    }

                    input.addEventListener('input', updateCounter);
                    updateCounter(); // Initial count
                }
            });
        }

        // Form validation
        function validateForm() {
            const requiredFields = document.querySelectorAll('input[required], textarea[required]');
            let isValid = true;

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                }
            });

            return isValid;
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            setupCharCounters();

            // Form submission validation
            document.getElementById('aboutPageForm').addEventListener('submit', function(e) {
                if (!validateForm()) {
                    e.preventDefault();

                    // Show first tab with errors
                    const firstError = document.querySelector('.is-invalid');
                    if (firstError) {
                        const tabPane = firstError.closest('.tab-pane');
                        if (tabPane) {
                            const tabId = tabPane.getAttribute('id');
                            const tabButton = document.querySelector(`[data-bs-target="#${tabId}"]`);
                            if (tabButton) {
                                new bootstrap.Tab(tabButton).show();
                            }
                        }
                    }

                    // Show error message
                    const alertHtml = `
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Please fill in all required fields</h6>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    `;

                    const container = document.querySelector('.container-fluid');
                    const existingAlert = container.querySelector('.alert-danger');
                    if (existingAlert) {
                        existingAlert.remove();
                    }

                    container.insertAdjacentHTML('afterbegin', alertHtml);

                    // Scroll to top
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                }
            });
        });
    </script>
}
