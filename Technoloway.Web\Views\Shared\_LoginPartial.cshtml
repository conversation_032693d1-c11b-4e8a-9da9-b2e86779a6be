﻿@using Microsoft.AspNetCore.Identity
@inject SignInManager<IdentityUser> SignInManager
@inject UserManager<IdentityUser> UserManager

<ul class="navbar-nav">
@if (SignInManager.IsSignedIn(User))
{
    @if (User.IsInRole("Client"))
    {
        <li class="nav-item">
            <a class="nav-link btn btn-primary text-white me-2" asp-area="Client" asp-controller="Home" asp-action="Index" title="Client Portal">
                <i class="fas fa-tachometer-alt me-1"></i> My Portal
            </a>
        </li>
    }
    @if (User.IsInRole("SuperAdmin") || User.IsInRole("ContentManager") || User.IsInRole("HRManager"))
    {
        <li class="nav-item">
            <a class="nav-link btn btn-success text-white me-2" asp-area="Admin" asp-controller="Home" asp-action="Index" title="Admin Panel">
                <i class="fas fa-cog me-1"></i> Admin Panel
            </a>
        </li>
    }
    <li class="nav-item dropdown">
        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
            <i class="fas fa-user-circle me-1"></i> @User.Identity?.Name
        </a>
        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
            <li><a class="dropdown-item" asp-area="Identity" asp-page="/Account/Manage/Index">
                <i class="fas fa-user me-2"></i> Profile
            </a></li>
            <li><hr class="dropdown-divider"></li>
            <li>
                <form asp-area="Identity" asp-page="/Account/Logout" asp-route-returnUrl="@Url.Action("Index", "Home", new { area = "" })" method="post">
                    <button type="submit" class="dropdown-item">
                        <i class="fas fa-sign-out-alt me-2"></i> Logout
                    </button>
                </form>
            </li>
        </ul>
    </li>
}
else
{
    <!-- Prominent Client Login Button -->
    <li class="nav-item">
        <a class="nav-link btn btn-primary text-white me-2" asp-area="Identity" asp-page="/Account/Login" asp-route-returnUrl="@Url.Action("Index", "Home", new { area = "Client" })">
            <i class="fas fa-user me-1"></i> Client Login
        </a>
    </li>

    <!-- Secondary Options -->
    <li class="nav-item dropdown">
        <a class="nav-link dropdown-toggle btn btn-outline-secondary" href="#" id="moreOptionsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
            <i class="fas fa-ellipsis-h me-1"></i>
        </a>
        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="moreOptionsDropdown">
            <li><a class="dropdown-item" asp-area="Admin" asp-controller="Account" asp-action="Login" asp-route-returnUrl="@Url.Action("Index", "Home", new { area = "Admin" })">
                <i class="fas fa-user-shield me-2 text-success"></i> Admin Login
            </a></li>
            <li><hr class="dropdown-divider"></li>
            <li><a class="dropdown-item" asp-area="Identity" asp-page="/Account/Register">
                <i class="fas fa-user-plus me-2 text-info"></i> Register
            </a></li>
        </ul>
    </li>
}
</ul>
