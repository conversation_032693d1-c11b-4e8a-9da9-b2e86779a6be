@model IEnumerable<Technoloway.Core.Entities.LegalPage>

@{
    ViewData["Title"] = "Legal Pages Management";
    Layout = "~/Areas/Admin/Views/Shared/_AdminLayout.cshtml";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">Legal Pages Management</h1>
                    <p class="text-muted">Manage Terms of Service, Privacy Policy, and other legal pages</p>
                </div>
                <a href="@Url.Action("Create")" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Add New Legal Page
                </a>
            </div>

            <!-- Success/Error Messages -->
            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>@TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            @if (TempData["ErrorMessage"] != null)
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i>@TempData["ErrorMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            <!-- Legal Pages Table -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-file-contract me-2"></i>Legal Pages
                    </h6>
                </div>
                <div class="card-body">
                    @if (Model.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover" id="legalPagesTable">
                                <thead class="table-light">
                                    <tr>
                                        <th>Title</th>
                                        <th>Slug</th>
                                        <th>Status</th>
                                        <th>Sections</th>
                                        <th>Last Modified</th>
                                        <th>Modified By</th>
                                        <th width="200">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var page in Model)
                                    {
                                        <tr>
                                            <td>
                                                <strong>@(page.Title)</strong>
                                                @if (!string.IsNullOrEmpty(page.MetaDescription))
                                                {
                                                    <br><small class="text-muted">@(page.MetaDescription)</small>
                                                }
                                            </td>
                                            <td>
                                                <code>/@(page.Slug)</code>
                                            </td>
                                            <td>
                                                <span class="badge @(page.IsActive ? "bg-success" : "bg-secondary")">
                                                    @(page.IsActive ? "Active" : "Inactive")
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-info">@(page.Sections.Count) sections</span>
                                            </td>
                                            <td>
                                                <small>
                                                    @(page.LastModified.ToString("MMM dd, yyyy"))
                                                    <br>@(page.LastModified.ToString("hh:mm tt"))
                                                </small>
                                            </td>
                                            <td>
                                                <small>@(page.ModifiedBy ?? "System")</small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="@Url.Action("Details", new { id = page.Id })"
                                                       class="btn btn-sm btn-outline-info" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="@Url.Action("Edit", new { id = page.Id })"
                                                       class="btn btn-sm btn-outline-primary" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button"
                                                            class="btn btn-sm btn-outline-@(page.IsActive ? "warning" : "success")"
                                                            onclick="toggleStatus(@(page.Id), @(page.IsActive.ToString().ToLower()))"
                                                            title="@(page.IsActive ? "Deactivate" : "Activate")">
                                                        <i class="fas fa-@(page.IsActive ? "pause" : "play")"></i>
                                                    </button>
                                                    <button type="button"
                                                            class="btn btn-sm btn-outline-danger"
                                                            onclick="deletePage(@(page.Id), '@(page.Title)')"
                                                            title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-file-contract fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Legal Pages Found</h5>
                            <p class="text-muted">Create your first legal page to get started.</p>
                            <a href="@Url.Action("Create")" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Add Legal Page
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the legal page "<span id="pageTitle"></span>"?</p>
                <p class="text-danger"><strong>This action cannot be undone.</strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Initialize DataTable
        $(document).ready(function() {
            $('#legalPagesTable').DataTable({
                "pageLength": 10,
                "order": [[4, "desc"]], // Sort by last modified date
                "columnDefs": [
                    { "orderable": false, "targets": [6] } // Disable sorting for actions column
                ]
            });
        });

        // Toggle page status
        function toggleStatus(pageId, isActive) {
            $.post('@Url.Action("ToggleStatus")', {
                id: pageId,
                __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
            })
            .done(function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('Error: ' + response.message);
                }
            })
            .fail(function() {
                alert('An error occurred while updating the page status.');
            });
        }

        // Delete page
        function deletePage(pageId, pageTitle) {
            $('#pageTitle').text(pageTitle);
            $('#deleteForm').attr('action', '@Url.Action("Delete")/' + pageId);
            $('#deleteModal').modal('show');
        }
    </script>
}

@section Styles {
    <style>
        .table th {
            border-top: none;
            font-weight: 600;
            color: #5a5c69;
        }

        .btn-group .btn {
            margin-right: 2px;
        }

        .btn-group .btn:last-child {
            margin-right: 0;
        }

        code {
            background-color: #f8f9fc;
            color: #5a5c69;
            padding: 2px 6px;
            border-radius: 3px;
        }
    </style>
}
