@model IEnumerable<Technoloway.Core.Entities.InvoiceItem>

@{
    ViewData["Title"] = "Invoice Items";
    Layout = "_AdminLayout";
    var invoice = ViewBag.Invoice as Technoloway.Core.Entities.Invoice;
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Items for Invoice #@(invoice != null ? invoice.InvoiceNumber : "")</h1>
    <div>
        <a asp-action="AddItem" asp-route-id="@(invoice != null ? invoice.Id : 0)" class="btn btn-sm btn-success shadow-sm">
            <i class="fas fa-plus fa-sm text-white-50"></i> Add Item
        </a>
        <a asp-action="Details" asp-route-id="@(invoice != null ? invoice.Id : 0)" class="btn btn-sm btn-info shadow-sm">
            <i class="fas fa-eye fa-sm text-white-50"></i> View Invoice
        </a>
        <a asp-action="Index" class="btn btn-sm btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Invoices
        </a>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Invoice Items</h6>
    </div>
    <div class="card-body">
        @if (!Model.Any())
        {
            <div class="alert alert-info">
                <p class="mb-0">No items have been added to this invoice yet.</p>
            </div>
            
            <div class="text-center mt-3">
                <a asp-action="AddItem" asp-route-id="@(invoice != null ? invoice.Id : 0)" class="btn btn-success">
                    <i class="fas fa-plus"></i> Add First Item
                </a>
            </div>
        }
        else
        {
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Description</th>
                            <th>Quantity</th>
                            <th>Unit Price</th>
                            <th>Total Price</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model)
                        {
                            <tr>
                                <td>@item.Description</td>
                                <td>@item.Quantity</td>
                                <td>@item.UnitPrice.ToString("C")</td>
                                <td>@item.TotalPrice.ToString("C")</td>
                                <td>@item.CreatedAt.ToString("yyyy-MM-dd")</td>
                                <td>
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-danger btn-sm" data-toggle="modal" data-target="#<EMAIL>">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                    
                                    <!-- Delete Modal -->
                                    <div class="modal fade" id="<EMAIL>" tabindex="-1" role="dialog" aria-labelledby="<EMAIL>" aria-hidden="true">
                                        <div class="modal-dialog" role="document">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title" id="<EMAIL>">Confirm Delete</h5>
                                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                        <span aria-hidden="true">&times;</span>
                                                    </button>
                                                </div>
                                                <div class="modal-body">
                                                    <p>Are you sure you want to delete this item?</p>
                                                    <p><strong>Description:</strong> @item.Description</p>
                                                    <p><strong>Amount:</strong> @item.TotalPrice.ToString("C")</p>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                                    <form asp-action="DeleteItem" asp-route-id="@item.Id" method="post">
                                                        <button type="submit" class="btn btn-danger">Delete</button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                    <tfoot>
                        <tr>
                            <th colspan="3" class="text-right">Subtotal:</th>
                            <th>@(invoice != null ? invoice.Amount.ToString("C") : "$0.00")</th>
                            <th colspan="2"></th>
                        </tr>
                        <tr>
                            <th colspan="3" class="text-right">Tax (@(invoice != null ? invoice.TaxRate.ToString("F2") : "0.00")%):</th>
                            <th>@(invoice != null ? invoice.TaxAmount.ToString("C") : "$0.00")</th>
                            <th colspan="2"></th>
                        </tr>
                        <tr>
                            <th colspan="3" class="text-right">Total:</th>
                            <th>@(invoice != null ? invoice.TotalAmount.ToString("C") : "$0.00")</th>
                            <th colspan="2"></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        }
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#dataTable').DataTable();
        });
    </script>
}
