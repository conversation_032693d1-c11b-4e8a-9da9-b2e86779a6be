@model Technoloway.Web.Areas.Admin.Models.AboutPageViewModel

@{
    ViewData["Title"] = "Edit About Page";
    ViewData["PageTitle"] = "Edit About Page";
    ViewData["PageDescription"] = "Update about page content and sections";
}

@section Styles {
    <style>
        .form-card {
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            margin-bottom: 1.5rem;
        }

        .form-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 0.5rem 0.5rem 0 0;
        }

        .form-body {
            padding: 1.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.5rem;
        }

        .form-control, .form-select {
            border: 1px solid #e1e5e9;
            border-radius: 0.375rem;
            padding: 0.75rem;
            transition: all 0.15s ease-in-out;
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(102, 126, 234, 0.25);
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 0.25rem 0.5rem rgba(102, 126, 234, 0.35);
        }

        .section-item {
            border: 1px solid #e9ecef;
            border-radius: 0.375rem;
            margin-bottom: 1rem;
            background: #f8f9fa;
        }

        .section-header {
            padding: 0.75rem 1rem;
            background: #e9ecef;
            border-bottom: 1px solid #dee2e6;
            border-radius: 0.375rem 0.375rem 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .section-body {
            padding: 1rem;
            background: white;
            border-radius: 0 0 0.375rem 0.375rem;
        }

        .char-counter {
            font-size: 0.875rem;
            color: #6c757d;
            text-align: right;
            margin-top: 0.25rem;
        }

        .char-counter.warning {
            color: #fd7e14;
        }

        .char-counter.danger {
            color: #dc3545;
        }

        .nav-tabs .nav-link {
            border: none;
            border-bottom: 2px solid transparent;
            color: #6c757d;
            font-weight: 500;
        }

        .nav-tabs .nav-link.active {
            border-bottom-color: #667eea;
            color: #667eea;
            background: none;
        }

        .tab-content {
            padding-top: 1.5rem;
        }

        .required {
            color: #dc3545;
        }

        .btn {
            cursor: pointer;
            transition: all 0.15s ease-in-out;
        }

        .btn:disabled {
            cursor: not-allowed;
            opacity: 0.65;
        }

        .btn-outline-secondary:hover {
            background-color: #6c757d;
            border-color: #6c757d;
            color: white;
        }

        .btn-outline-info:hover {
            background-color: #0dcaf0;
            border-color: #0dcaf0;
            color: white;
        }

        .btn-outline-danger:hover {
            background-color: #dc3545;
            border-color: #dc3545;
            color: white;
        }

        .nav-tabs .nav-link:hover {
            border-bottom-color: #667eea;
            color: #667eea;
        }

        .form-control.is-invalid {
            border-color: #dc3545;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
        }

        /* Tab styling enhancements */
        .nav-tabs {
            border-bottom: 2px solid #e9ecef;
            margin-bottom: 1.5rem;
        }

        .nav-tabs .nav-link {
            border: none;
            border-radius: 0;
            color: #6c757d;
            font-weight: 500;
            padding: 1rem 1.5rem;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-tabs .nav-link:hover {
            border-color: transparent;
            color: #667eea;
            background-color: rgba(102, 126, 234, 0.1);
        }

        .nav-tabs .nav-link.active {
            color: #667eea;
            background-color: transparent;
            border-color: transparent;
            font-weight: 600;
        }

        .nav-tabs .nav-link.active::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 2px 2px 0 0;
        }

        .tab-content {
            min-height: 400px;
        }

        .tab-pane {
            animation: fadeIn 0.3s ease-in-out;
        }

        @@keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* CKEditor 5 Styling */
        .ck-editor {
            margin-bottom: 1rem;
        }

        .ck-editor__editable {
            min-height: 200px;
            border-radius: 0.375rem;
            border: 1px solid #ced4da;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }

        .ck-editor__editable:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .ck-toolbar {
            border-radius: 0.375rem 0.375rem 0 0;
            border: 1px solid #ced4da;
            border-bottom: none;
            background: #f8f9fa;
        }

        .ck-editor__main > .ck-editor__editable {
            border-radius: 0 0 0.375rem 0.375rem;
            border-top: none;
        }

        /* CKEditor content styling */
        .ck-content {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            line-height: 1.6;
        }

        .ck-content h1 { font-size: 2rem; margin: 1rem 0 0.5rem 0; }
        .ck-content h2 { font-size: 1.75rem; margin: 1rem 0 0.5rem 0; }
        .ck-content h3 { font-size: 1.5rem; margin: 1rem 0 0.5rem 0; }
        .ck-content h4 { font-size: 1.25rem; margin: 1rem 0 0.5rem 0; }
        .ck-content p { margin: 0.5rem 0; }
        .ck-content ul, .ck-content ol { margin: 0.5rem 0; padding-left: 2rem; }
        .ck-content blockquote {
            border-left: 4px solid #667eea;
            padding-left: 1rem;
            margin: 1rem 0;
            font-style: italic;
            color: #6c757d;
        }

        /* Hide original textareas when CKEditor is active */
        .ck-editor + textarea {
            display: none;
        }
    </style>
}

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-1">
                <i class="fas fa-edit me-2 text-primary"></i>Edit About Page
            </h1>
            <p class="text-muted mb-0">Update about page content and configuration</p>
        </div>
        <div class="btn-group">
            <a asp-action="Index" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to List
            </a>
            <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-outline-info">
                <i class="fas fa-eye me-1"></i>View Details
            </a>
        </div>
    </div>

    <!-- Validation Summary -->
    @if (!ViewData.ModelState.IsValid)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <h6><i class="fas fa-exclamation-triangle me-2"></i>Please correct the following errors:</h6>
            <div asp-validation-summary="All" class="mb-0"></div>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <form asp-action="Edit" method="post" id="aboutPageForm">
        <input asp-for="Id" type="hidden" />

        <!-- Navigation Tabs -->
        <ul class="nav nav-tabs" id="aboutPageTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="basic-tab" data-bs-toggle="tab" data-bs-target="#basic" type="button" role="tab">
                    <i class="fas fa-info-circle me-1"></i>Basic Info
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="hero-tab" data-bs-toggle="tab" data-bs-target="#hero" type="button" role="tab">
                    <i class="fas fa-star me-1"></i>Hero Section
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="story-tab" data-bs-toggle="tab" data-bs-target="#story" type="button" role="tab">
                    <i class="fas fa-book me-1"></i>Story
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="stats-tab" data-bs-toggle="tab" data-bs-target="#stats" type="button" role="tab">
                    <i class="fas fa-chart-bar me-1"></i>Statistics
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="mission-tab" data-bs-toggle="tab" data-bs-target="#mission" type="button" role="tab">
                    <i class="fas fa-bullseye me-1"></i>Mission & Vision
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="cta-tab" data-bs-toggle="tab" data-bs-target="#cta" type="button" role="tab">
                    <i class="fas fa-mouse-pointer me-1"></i>Call to Action
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="sections-tab" data-bs-toggle="tab" data-bs-target="#sections" type="button" role="tab">
                    <i class="fas fa-list me-1"></i>Sections
                </button>
            </li>
        </ul>

        <div class="tab-content" id="aboutPageTabContent">
            <!-- Basic Information Tab -->
            <div class="tab-pane fade show active" id="basic" role="tabpanel">
                <div class="form-card">
                    <div class="form-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>Basic Information
                        </h5>
                    </div>
                    <div class="form-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Title" class="form-label">
                                        Page Title <span class="required">*</span>
                                    </label>
                                    <input asp-for="Title" class="form-control" maxlength="100" required />
                                    <span asp-validation-for="Title" class="text-danger"></span>
                                    <div class="char-counter">
                                        <span id="titleCounter">0</span>/100 characters
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="IsActive" class="form-label">Status</label>
                                    <select asp-for="IsActive" class="form-select">
                                        <option value="true">Active</option>
                                        <option value="false">Inactive</option>
                                    </select>
                                    <span asp-validation-for="IsActive" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label asp-for="MetaDescription" class="form-label">Meta Description</label>
                            <textarea asp-for="MetaDescription" class="form-control" rows="3" maxlength="200"
                                      placeholder="Brief description for search engines (recommended: 150-160 characters)"></textarea>
                            <span asp-validation-for="MetaDescription" class="text-danger"></span>
                            <div class="char-counter">
                                <span id="metaDescCounter">0</span>/200 characters
                            </div>
                        </div>

                        <div class="form-group">
                            <label asp-for="MetaKeywords" class="form-label">Meta Keywords</label>
                            <input asp-for="MetaKeywords" class="form-control" maxlength="500"
                                   placeholder="Comma-separated keywords for SEO" />
                            <span asp-validation-for="MetaKeywords" class="text-danger"></span>
                            <div class="char-counter">
                                <span id="metaKeywordsCounter">0</span>/500 characters
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Hero Section Tab -->
            <div class="tab-pane fade" id="hero" role="tabpanel">
                <div class="form-card">
                    <div class="form-header">
                        <h5 class="mb-0">
                            <i class="fas fa-star me-2"></i>Hero Section
                        </h5>
                    </div>
                    <div class="form-body">
                        <div class="form-group">
                            <label asp-for="HeroTitle" class="form-label">
                                Hero Title <span class="required">*</span>
                            </label>
                            <input asp-for="HeroTitle" class="form-control" maxlength="200" required />
                            <span asp-validation-for="HeroTitle" class="text-danger"></span>
                            <div class="char-counter">
                                <span id="heroTitleCounter">0</span>/200 characters
                            </div>
                        </div>

                        <div class="form-group">
                            <label asp-for="HeroSubtitle" class="form-label">
                                Hero Subtitle <span class="required">*</span>
                            </label>
                            <textarea asp-for="HeroSubtitle" class="form-control" rows="3" maxlength="500" required></textarea>
                            <span asp-validation-for="HeroSubtitle" class="text-danger"></span>
                            <div class="char-counter">
                                <span id="heroSubtitleCounter">0</span>/500 characters
                            </div>
                        </div>

                        <div class="form-group">
                            <label asp-for="HeroImageUrl" class="form-label">Hero Image URL</label>
                            <div class="input-group">
                                <input asp-for="HeroImageUrl" class="form-control" maxlength="500"
                                       placeholder="https://example.com/image.jpg" />
                                <button type="button" class="btn btn-outline-secondary upload-btn" data-target="HeroImageUrl">
                                    <i class="fas fa-upload"></i>
                                </button>
                            </div>
                            <span asp-validation-for="HeroImageUrl" class="text-danger"></span>
                            <small class="form-text text-muted">
                                Recommended size: 1920x1080px. Leave empty to use default image.
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Story Section Tab -->
            <div class="tab-pane fade" id="story" role="tabpanel">
                <div class="form-card">
                    <div class="form-header">
                        <h5 class="mb-0">
                            <i class="fas fa-book me-2"></i>Story Section
                        </h5>
                    </div>
                    <div class="form-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="StoryTitle" class="form-label">
                                        Story Title <span class="required">*</span>
                                    </label>
                                    <input asp-for="StoryTitle" class="form-control" maxlength="100" required />
                                    <span asp-validation-for="StoryTitle" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="StoryImageUrl" class="form-label">Story Image URL</label>
                                    <div class="input-group">
                                        <input asp-for="StoryImageUrl" class="form-control" maxlength="500"
                                               placeholder="https://example.com/image.jpg" />
                                        <button type="button" class="btn btn-outline-secondary upload-btn" data-target="StoryImageUrl">
                                            <i class="fas fa-upload"></i>
                                        </button>
                                    </div>
                                    <span asp-validation-for="StoryImageUrl" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label asp-for="StorySubtitle" class="form-label">
                                Story Subtitle <span class="required">*</span>
                            </label>
                            <textarea asp-for="StorySubtitle" class="form-control" rows="2" maxlength="300" required></textarea>
                            <span asp-validation-for="StorySubtitle" class="text-danger"></span>
                            <div class="char-counter">
                                <span id="storySubtitleCounter">0</span>/300 characters
                            </div>
                        </div>

                        <div class="form-group">
                            <label asp-for="StoryContent" class="form-label">
                                Story Content <span class="required">*</span>
                            </label>
                            <textarea asp-for="StoryContent" class="form-control" rows="8" id="storyContentEditor" required></textarea>
                            <span asp-validation-for="StoryContent" class="text-danger"></span>
                            <small class="form-text text-muted">
                                Use the rich text editor above to format your content with headings, bold text, lists, links, and more.
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics Tab -->
            <div class="tab-pane fade" id="stats" role="tabpanel">
                <div class="form-card">
                    <div class="form-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>Statistics Section
                        </h5>
                    </div>
                    <div class="form-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Stat1Number" class="form-label">Statistic 1 Number</label>
                                    <input asp-for="Stat1Number" class="form-control" maxlength="50" placeholder="8+" />
                                    <span asp-validation-for="Stat1Number" class="text-danger"></span>
                                </div>
                                <div class="form-group">
                                    <label asp-for="Stat1Label" class="form-label">Statistic 1 Label</label>
                                    <input asp-for="Stat1Label" class="form-control" maxlength="100" placeholder="Years Experience" />
                                    <span asp-validation-for="Stat1Label" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Stat2Number" class="form-label">Statistic 2 Number</label>
                                    <input asp-for="Stat2Number" class="form-control" maxlength="50" placeholder="100+" />
                                    <span asp-validation-for="Stat2Number" class="text-danger"></span>
                                </div>
                                <div class="form-group">
                                    <label asp-for="Stat2Label" class="form-label">Statistic 2 Label</label>
                                    <input asp-for="Stat2Label" class="form-control" maxlength="100" placeholder="Projects Completed" />
                                    <span asp-validation-for="Stat2Label" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Stat3Number" class="form-label">Statistic 3 Number</label>
                                    <input asp-for="Stat3Number" class="form-control" maxlength="50" placeholder="50+" />
                                    <span asp-validation-for="Stat3Number" class="text-danger"></span>
                                </div>
                                <div class="form-group">
                                    <label asp-for="Stat3Label" class="form-label">Statistic 3 Label</label>
                                    <input asp-for="Stat3Label" class="form-control" maxlength="100" placeholder="Happy Clients" />
                                    <span asp-validation-for="Stat3Label" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Stat4Number" class="form-label">Statistic 4 Number</label>
                                    <input asp-for="Stat4Number" class="form-control" maxlength="50" placeholder="24/7" />
                                    <span asp-validation-for="Stat4Number" class="text-danger"></span>
                                </div>
                                <div class="form-group">
                                    <label asp-for="Stat4Label" class="form-label">Statistic 4 Label</label>
                                    <input asp-for="Stat4Label" class="form-control" maxlength="100" placeholder="Support Available" />
                                    <span asp-validation-for="Stat4Label" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Mission & Vision Tab -->
            <div class="tab-pane fade" id="mission" role="tabpanel">
                <div class="form-card">
                    <div class="form-header">
                        <h5 class="mb-0">
                            <i class="fas fa-bullseye me-2"></i>Mission, Vision & Values
                        </h5>
                    </div>
                    <div class="form-body">
                        <div class="form-group">
                            <label asp-for="MissionTitle" class="form-label">Mission Title</label>
                            <input asp-for="MissionTitle" class="form-control" maxlength="100" placeholder="Our Mission" />
                            <span asp-validation-for="MissionTitle" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="MissionContent" class="form-label">Mission Content</label>
                            <textarea asp-for="MissionContent" class="form-control" rows="4" id="missionContentEditor"></textarea>
                            <span asp-validation-for="MissionContent" class="text-danger"></span>
                            <small class="form-text text-muted">
                                Use the rich text editor to format your mission statement with headings, bold text, and lists.
                            </small>
                        </div>

                        <div class="form-group">
                            <label asp-for="VisionTitle" class="form-label">Vision Title</label>
                            <input asp-for="VisionTitle" class="form-control" maxlength="100" placeholder="Our Vision" />
                            <span asp-validation-for="VisionTitle" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="VisionContent" class="form-label">Vision Content</label>
                            <textarea asp-for="VisionContent" class="form-control" rows="4" id="visionContentEditor"></textarea>
                            <span asp-validation-for="VisionContent" class="text-danger"></span>
                            <small class="form-text text-muted">
                                Use the rich text editor to format your vision statement with headings, bold text, and lists.
                            </small>
                        </div>

                        <div class="form-group">
                            <label asp-for="ValuesTitle" class="form-label">Values Title</label>
                            <input asp-for="ValuesTitle" class="form-control" maxlength="100" placeholder="Our Values" />
                            <span asp-validation-for="ValuesTitle" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="ValuesContent" class="form-label">Values Content</label>
                            <textarea asp-for="ValuesContent" class="form-control" rows="4" id="valuesContentEditor"></textarea>
                            <span asp-validation-for="ValuesContent" class="text-danger"></span>
                            <small class="form-text text-muted">
                                Use the rich text editor to format your values content with headings, bold text, and lists.
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Call to Action Tab -->
            <div class="tab-pane fade" id="cta" role="tabpanel">
                <div class="form-card">
                    <div class="form-header">
                        <h5 class="mb-0">
                            <i class="fas fa-mouse-pointer me-2"></i>Call to Action Section
                        </h5>
                    </div>
                    <div class="form-body">
                        <div class="form-group">
                            <label asp-for="CtaTitle" class="form-label">CTA Title</label>
                            <input asp-for="CtaTitle" class="form-control" maxlength="100" placeholder="Ready to Work With Us?" />
                            <span asp-validation-for="CtaTitle" class="text-danger"></span>
                        </div>

                        <div class="form-group">
                            <label asp-for="CtaSubtitle" class="form-label">CTA Subtitle</label>
                            <textarea asp-for="CtaSubtitle" class="form-control" rows="3" maxlength="300"
                                      placeholder="Let's discuss how we can help your business succeed..."></textarea>
                            <span asp-validation-for="CtaSubtitle" class="text-danger"></span>
                            <div class="char-counter">
                                <span id="ctaSubtitleCounter">0</span>/300 characters
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="CtaPrimaryButtonText" class="form-label">Primary Button Text</label>
                                    <input asp-for="CtaPrimaryButtonText" class="form-control" maxlength="50" placeholder="Get Started" />
                                    <span asp-validation-for="CtaPrimaryButtonText" class="text-danger"></span>
                                </div>
                                <div class="form-group">
                                    <label asp-for="CtaPrimaryButtonUrl" class="form-label">Primary Button URL</label>
                                    <input asp-for="CtaPrimaryButtonUrl" class="form-control" maxlength="200" placeholder="/contact" />
                                    <span asp-validation-for="CtaPrimaryButtonUrl" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="CtaSecondaryButtonText" class="form-label">Secondary Button Text</label>
                                    <input asp-for="CtaSecondaryButtonText" class="form-control" maxlength="50" placeholder="View Portfolio" />
                                    <span asp-validation-for="CtaSecondaryButtonText" class="text-danger"></span>
                                </div>
                                <div class="form-group">
                                    <label asp-for="CtaSecondaryButtonUrl" class="form-label">Secondary Button URL</label>
                                    <input asp-for="CtaSecondaryButtonUrl" class="form-control" maxlength="200" placeholder="/projects" />
                                    <span asp-validation-for="CtaSecondaryButtonUrl" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sections Tab -->
            <div class="tab-pane fade" id="sections" role="tabpanel">
                <div class="form-card">
                    <div class="form-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-list me-2"></i>Additional Sections
                            </h5>
                            <button type="button" class="btn btn-light btn-sm" onclick="addSection()">
                                <i class="fas fa-plus me-1"></i>Add Section
                            </button>
                        </div>
                    </div>
                    <div class="form-body">
                        <div id="sectionsContainer">
                            @if (Model.Sections?.Any() == true)
                            {
                                @for (int i = 0; i < Model.Sections.Count; i++)
                                {
                                    <div class="section-item" data-index="@i">
                                        <div class="section-header">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-grip-vertical me-2 text-muted"></i>
                                                <strong>Section @(i + 1)</strong>
                                            </div>
                                            <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeSection(this)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                        <div class="section-body">
                                            <input asp-for="Sections[i].Id" type="hidden" />
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label asp-for="Sections[i].Title" class="form-label">Section Title</label>
                                                        <input asp-for="Sections[i].Title" class="form-control" maxlength="200" />
                                                        <span asp-validation-for="Sections[i].Title" class="text-danger"></span>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="form-group">
                                                        <label asp-for="Sections[i].SectionType" class="form-label">Section Type</label>
                                                        <select asp-for="Sections[i].SectionType" class="form-select">
                                                            <option value="content">Content</option>
                                                            <option value="value">Value</option>
                                                            <option value="feature">Feature</option>
                                                            <option value="service">Service</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="form-group">
                                                        <label asp-for="Sections[i].DisplayOrder" class="form-label">Display Order</label>
                                                        <input asp-for="Sections[i].DisplayOrder" class="form-control" type="number" min="1" />
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label asp-for="Sections[i].IconClass" class="form-label">Icon Class</label>
                                                        <input asp-for="Sections[i].IconClass" class="form-control" maxlength="50" placeholder="fas fa-lightbulb" />
                                                        <small class="form-text text-muted">FontAwesome icon class (e.g., fas fa-lightbulb)</small>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label asp-for="Sections[i].ImageUrl" class="form-label">Image URL</label>
                                                        <div class="input-group">
                                                            <input asp-for="Sections[i].ImageUrl" class="form-control" maxlength="500" />
                                                            <button type="button" class="btn btn-outline-secondary upload-btn" data-target="Sections[@i].ImageUrl">
                                                                <i class="fas fa-upload"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label asp-for="Sections[i].Content" class="form-label">Section Content</label>
                                                <textarea asp-for="Sections[i].Content" class="form-control section-content-editor" rows="4"></textarea>
                                                <span asp-validation-for="Sections[i].Content" class="text-danger"></span>
                                                <small class="form-text text-muted">
                                                    Use the rich text editor to format your section content with headings, bold text, lists, and links.
                                                </small>
                                            </div>
                                            <div class="form-check">
                                                <input asp-for="Sections[i].IsActive" class="form-check-input" />
                                                <label asp-for="Sections[i].IsActive" class="form-check-label">Active</label>
                                            </div>
                                        </div>
                                    </div>
                                }
                            }
                        </div>
                        @if (Model.Sections?.Any() != true)
                        {
                            <div class="text-center py-4" id="noSectionsMessage">
                                <i class="fas fa-list fa-3x text-muted opacity-50 mb-3"></i>
                                <h6 class="text-muted">No additional sections</h6>
                                <p class="text-muted mb-3">Add custom sections to enhance your about page content.</p>
                                <button type="button" class="btn btn-primary" onclick="addSection()">
                                    <i class="fas fa-plus me-1"></i>Add Your First Section
                                </button>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="d-flex justify-content-between align-items-center mt-4">
            <div>
                <a asp-action="Index" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>Cancel
                </a>
            </div>
            <div>
                <button type="submit" class="btn btn-primary btn-lg">
                    <i class="fas fa-save me-2"></i>Save Changes
                </button>
            </div>
        </div>
    </form>
</div>

@section Scripts {
    <!-- CKEditor 5 CDN -->
    <script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>

    <script>
        let sectionIndex = @(Model.Sections?.Count ?? 0);
        let ckEditorInstances = {};

        // Character counters
        function setupCharCounters() {
            const counters = [
                { input: '#Title', counter: '#titleCounter', max: 100 },
                { input: '#MetaDescription', counter: '#metaDescCounter', max: 200 },
                { input: '#MetaKeywords', counter: '#metaKeywordsCounter', max: 500 },
                { input: '#HeroTitle', counter: '#heroTitleCounter', max: 200 },
                { input: '#HeroSubtitle', counter: '#heroSubtitleCounter', max: 500 },
                { input: '#StorySubtitle', counter: '#storySubtitleCounter', max: 300 },
                { input: '#CtaSubtitle', counter: '#ctaSubtitleCounter', max: 300 }
            ];

            counters.forEach(item => {
                const input = document.querySelector(item.input);
                const counter = document.querySelector(item.counter);

                if (input && counter) {
                    function updateCounter() {
                        const length = input.value.length;
                        counter.textContent = length;

                        const counterElement = counter.parentElement;
                        counterElement.classList.remove('warning', 'danger');

                        if (length > item.max * 0.9) {
                            counterElement.classList.add('danger');
                        } else if (length > item.max * 0.8) {
                            counterElement.classList.add('warning');
                        }
                    }

                    input.addEventListener('input', updateCounter);
                    updateCounter(); // Initial count
                }
            });
        }

        // Add new section
        function addSection() {
            const container = document.getElementById('sectionsContainer');
            const noSectionsMessage = document.getElementById('noSectionsMessage');

            if (noSectionsMessage) {
                noSectionsMessage.style.display = 'none';
            }

            const sectionHtml = `
                <div class="section-item" data-index="${sectionIndex}">
                    <div class="section-header">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-grip-vertical me-2 text-muted"></i>
                            <strong>Section ${sectionIndex + 1}</strong>
                        </div>
                        <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeSection(this)">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    <div class="section-body">
                        <input name="Sections[${sectionIndex}].Id" type="hidden" value="0" />
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Section Title</label>
                                    <input name="Sections[${sectionIndex}].Title" class="form-control" maxlength="200" />
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label">Section Type</label>
                                    <select name="Sections[${sectionIndex}].SectionType" class="form-select">
                                        <option value="content">Content</option>
                                        <option value="value">Value</option>
                                        <option value="feature">Feature</option>
                                        <option value="service">Service</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label">Display Order</label>
                                    <input name="Sections[${sectionIndex}].DisplayOrder" class="form-control" type="number" min="1" value="${sectionIndex + 1}" />
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Icon Class</label>
                                    <input name="Sections[${sectionIndex}].IconClass" class="form-control" maxlength="50" placeholder="fas fa-lightbulb" />
                                    <small class="form-text text-muted">FontAwesome icon class (e.g., fas fa-lightbulb)</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Image URL</label>
                                    <div class="input-group">
                                        <input name="Sections[${sectionIndex}].ImageUrl" class="form-control" maxlength="500" />
                                        <button type="button" class="btn btn-outline-secondary upload-btn" data-target="Sections[${sectionIndex}].ImageUrl">
                                            <i class="fas fa-upload"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Section Content</label>
                            <textarea name="Sections[${sectionIndex}].Content" class="form-control section-content-editor" rows="4"></textarea>
                            <small class="form-text text-muted">
                                Use the rich text editor to format your section content with headings, bold text, lists, and links.
                            </small>
                        </div>
                        <div class="form-check">
                            <input name="Sections[${sectionIndex}].IsActive" class="form-check-input" type="checkbox" value="true" checked />
                            <input name="Sections[${sectionIndex}].IsActive" type="hidden" value="false" />
                            <label class="form-check-label">Active</label>
                        </div>
                    </div>
                </div>
            `;

            container.insertAdjacentHTML('beforeend', sectionHtml);

            // Initialize CKEditor for the new section's textarea
            const newTextarea = container.querySelector(`textarea[name="Sections[${sectionIndex}].Content"]`);
            if (newTextarea) {
                setTimeout(() => {
                    initializeSectionCKEditor(newTextarea);
                }, 100);
            }

            sectionIndex++;
        }

        // Remove section
        function removeSection(button) {
            const sectionItem = button.closest('.section-item');

            // Destroy CKEditor instance if it exists
            const textarea = sectionItem.querySelector('.section-content-editor');
            if (textarea && textarea.hasAttribute('data-editor-id')) {
                const editorId = textarea.getAttribute('data-editor-id');
                if (ckEditorInstances[editorId]) {
                    ckEditorInstances[editorId].destroy()
                        .then(() => {
                            delete ckEditorInstances[editorId];
                            console.log(`CKEditor instance ${editorId} destroyed`);
                        })
                        .catch(error => {
                            console.error(`Error destroying CKEditor instance ${editorId}:`, error);
                        });
                }
            }

            sectionItem.remove();

            // Show no sections message if no sections left
            const container = document.getElementById('sectionsContainer');
            const noSectionsMessage = document.getElementById('noSectionsMessage');

            if (container.children.length === 0 && noSectionsMessage) {
                noSectionsMessage.style.display = 'block';
            }

            // Reindex remaining sections
            reindexSections();
        }

        // Reindex sections after removal
        function reindexSections() {
            const sections = document.querySelectorAll('.section-item');
            sections.forEach((section, index) => {
                section.setAttribute('data-index', index);
                section.querySelector('.section-header strong').textContent = `Section ${index + 1}`;

                // Update form field names
                const inputs = section.querySelectorAll('input, select, textarea');
                inputs.forEach(input => {
                    const name = input.getAttribute('name');
                    if (name && name.includes('Sections[')) {
                        const newName = name.replace(/Sections\[\d+\]/, `Sections[${index}]`);
                        input.setAttribute('name', newName);
                    }
                });
            });

            sectionIndex = sections.length;
        }

        // Form validation
        function validateForm() {
            const requiredFields = document.querySelectorAll('input[required], textarea[required]');
            let isValid = true;

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                }
            });

            return isValid;
        }

        // Initialize CKEditor for content fields
        function initializeCKEditor() {
            console.log('Initializing CKEditor...');

            // CKEditor configuration
            const editorConfig = {
                toolbar: {
                    items: [
                        'heading', '|',
                        'bold', 'italic', 'underline', 'strikethrough', '|',
                        'fontSize', 'fontColor', 'fontBackgroundColor', '|',
                        'alignment', '|',
                        'numberedList', 'bulletedList', '|',
                        'outdent', 'indent', '|',
                        'link', 'blockQuote', 'insertTable', '|',
                        'undo', 'redo', '|',
                        'sourceEditing'
                    ]
                },
                heading: {
                    options: [
                        { model: 'paragraph', title: 'Paragraph', class: 'ck-heading_paragraph' },
                        { model: 'heading1', view: 'h1', title: 'Heading 1', class: 'ck-heading_heading1' },
                        { model: 'heading2', view: 'h2', title: 'Heading 2', class: 'ck-heading_heading2' },
                        { model: 'heading3', view: 'h3', title: 'Heading 3', class: 'ck-heading_heading3' },
                        { model: 'heading4', view: 'h4', title: 'Heading 4', class: 'ck-heading_heading4' }
                    ]
                },
                fontSize: {
                    options: [
                        'tiny', 'small', 'default', 'big', 'huge'
                    ]
                },
                table: {
                    contentToolbar: ['tableColumn', 'tableRow', 'mergeTableCells']
                },
                language: 'en'
            };

            // Initialize CKEditor for Story Content
            const storyContentElement = document.querySelector('#storyContentEditor');
            if (storyContentElement) {
                ClassicEditor
                    .create(storyContentElement, editorConfig)
                    .then(editor => {
                        ckEditorInstances['storyContentEditor'] = editor;
                        console.log('Story Content CKEditor initialized');
                    })
                    .catch(error => {
                        console.error('Error initializing Story Content CKEditor:', error);
                    });
            }

            // Initialize CKEditor for Mission Content
            const missionContentElement = document.querySelector('#missionContentEditor');
            if (missionContentElement) {
                ClassicEditor
                    .create(missionContentElement, editorConfig)
                    .then(editor => {
                        ckEditorInstances['missionContentEditor'] = editor;
                        console.log('Mission Content CKEditor initialized');
                    })
                    .catch(error => {
                        console.error('Error initializing Mission Content CKEditor:', error);
                    });
            }

            // Initialize CKEditor for Vision Content
            const visionContentElement = document.querySelector('#visionContentEditor');
            if (visionContentElement) {
                ClassicEditor
                    .create(visionContentElement, editorConfig)
                    .then(editor => {
                        ckEditorInstances['visionContentEditor'] = editor;
                        console.log('Vision Content CKEditor initialized');
                    })
                    .catch(error => {
                        console.error('Error initializing Vision Content CKEditor:', error);
                    });
            }

            // Initialize CKEditor for Values Content
            const valuesContentElement = document.querySelector('#valuesContentEditor');
            if (valuesContentElement) {
                ClassicEditor
                    .create(valuesContentElement, editorConfig)
                    .then(editor => {
                        ckEditorInstances['valuesContentEditor'] = editor;
                        console.log('Values Content CKEditor initialized');
                    })
                    .catch(error => {
                        console.error('Error initializing Values Content CKEditor:', error);
                    });
            }

            // Initialize CKEditor for existing section content editors
            const sectionEditors = document.querySelectorAll('.section-content-editor');
            sectionEditors.forEach((element, index) => {
                const editorId = `sectionEditor_${index}`;
                ClassicEditor
                    .create(element, editorConfig)
                    .then(editor => {
                        ckEditorInstances[editorId] = editor;
                        console.log(`Section Content CKEditor ${index} initialized`);
                    })
                    .catch(error => {
                        console.error(`Error initializing Section Content CKEditor ${index}:`, error);
                    });
            });

            console.log('CKEditor initialization complete');
        }

        // Sync CKEditor data to textareas before form submission
        function syncCKEditorData() {
            console.log('Syncing CKEditor data...');

            Object.keys(ckEditorInstances).forEach(editorId => {
                const editor = ckEditorInstances[editorId];
                if (editor) {
                    try {
                        const data = editor.getData();

                        // Find the corresponding textarea and update its value
                        let textarea = null;

                        if (editorId === 'storyContentEditor') {
                            textarea = document.querySelector('#storyContentEditor');
                        } else if (editorId === 'missionContentEditor') {
                            textarea = document.querySelector('#missionContentEditor');
                        } else if (editorId === 'visionContentEditor') {
                            textarea = document.querySelector('#visionContentEditor');
                        } else if (editorId === 'valuesContentEditor') {
                            textarea = document.querySelector('#valuesContentEditor');
                        } else if (editorId.startsWith('sectionEditor_')) {
                            const index = editorId.replace('sectionEditor_', '');
                            textarea = document.querySelector(`.section-content-editor:nth-of-type(${parseInt(index) + 1})`);
                        } else if (editorId.startsWith('dynamicSectionEditor_')) {
                            textarea = document.querySelector(`[data-editor-id="${editorId}"]`);
                        }

                        if (textarea) {
                            textarea.value = data;
                            console.log(`Synced data for ${editorId}`);
                        }
                    } catch (error) {
                        console.error(`Error syncing data for ${editorId}:`, error);
                    }
                }
            });

            console.log('CKEditor data sync complete');
        }

        // Initialize CKEditor for dynamically added sections
        function initializeSectionCKEditor(textarea) {
            const editorConfig = {
                toolbar: {
                    items: [
                        'heading', '|',
                        'bold', 'italic', 'underline', '|',
                        'fontSize', 'fontColor', '|',
                        'numberedList', 'bulletedList', '|',
                        'link', 'blockQuote', '|',
                        'undo', 'redo'
                    ]
                },
                heading: {
                    options: [
                        { model: 'paragraph', title: 'Paragraph', class: 'ck-heading_paragraph' },
                        { model: 'heading2', view: 'h2', title: 'Heading 2', class: 'ck-heading_heading2' },
                        { model: 'heading3', view: 'h3', title: 'Heading 3', class: 'ck-heading_heading3' }
                    ]
                },
                fontSize: {
                    options: ['small', 'default', 'big']
                }
            };

            ClassicEditor
                .create(textarea, editorConfig)
                .then(editor => {
                    const editorId = `dynamicSectionEditor_${Date.now()}`;
                    ckEditorInstances[editorId] = editor;
                    textarea.setAttribute('data-editor-id', editorId);
                    console.log(`Dynamic Section CKEditor initialized: ${editorId}`);
                })
                .catch(error => {
                    console.error('Error initializing dynamic Section CKEditor:', error);
                });
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            setupCharCounters();
            initializeTabs();

            // Initialize CKEditor after a short delay to ensure DOM is ready
            setTimeout(() => {
                initializeCKEditor();
            }, 500);

            // Form submission validation
            document.getElementById('aboutPageForm').addEventListener('submit', function(e) {
                const submitButton = document.querySelector('button[type="submit"]');

                // Sync all CKEditor data before validation
                syncCKEditorData();

                if (!validateForm()) {
                    e.preventDefault();

                    // Show first tab with errors
                    const firstError = document.querySelector('.is-invalid');
                    if (firstError) {
                        const tabPane = firstError.closest('.tab-pane');
                        if (tabPane) {
                            const tabId = tabPane.getAttribute('id');
                            const tabButton = document.querySelector(`[data-bs-target="#${tabId}"]`);
                            if (tabButton) {
                                const tabInstance = new bootstrap.Tab(tabButton);
                                tabInstance.show();
                            }
                        }
                    }

                    // Show error message
                    const alertHtml = `
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Please fill in all required fields</h6>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    `;

                    const container = document.querySelector('.container-fluid');
                    const existingAlert = container.querySelector('.alert-danger');
                    if (existingAlert) {
                        existingAlert.remove();
                    }

                    container.insertAdjacentHTML('afterbegin', alertHtml);

                    // Scroll to top
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                } else {
                    // Show loading state
                    submitButton.disabled = true;
                    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';
                }
            });
        });

        // Initialize Bootstrap tabs
        function initializeTabs() {
            console.log('Initializing tabs...');

            // Check if Bootstrap is available
            if (typeof bootstrap === 'undefined') {
                console.error('Bootstrap is not loaded!');
                return;
            }

            // Initialize all tab triggers
            const tabTriggerList = document.querySelectorAll('#aboutPageTabs button[data-bs-toggle="tab"]');
            console.log('Found tab triggers:', tabTriggerList.length);

            if (tabTriggerList.length === 0) {
                console.error('No tab triggers found!');
                return;
            }

            // Initialize Bootstrap Tab instances
            const tabList = [...tabTriggerList].map(tabTriggerEl => {
                console.log('Initializing tab:', tabTriggerEl.getAttribute('data-bs-target'));
                return new bootstrap.Tab(tabTriggerEl);
            });

            // Add click event listeners to ensure tabs work
            tabTriggerList.forEach(function(tabTrigger) {
                tabTrigger.addEventListener('click', function(event) {
                    event.preventDefault();
                    console.log('Tab clicked:', this.getAttribute('data-bs-target'));
                    const tab = new bootstrap.Tab(this);
                    tab.show();
                });
            });

            // Handle tab shown event
            tabTriggerList.forEach(function(tabTrigger) {
                tabTrigger.addEventListener('shown.bs.tab', function(event) {
                    console.log('Tab shown:', event.target.getAttribute('data-bs-target'));
                    // Update URL hash without scrolling
                    const targetId = event.target.getAttribute('data-bs-target').substring(1);
                    history.replaceState(null, null, '#' + targetId);
                });
            });

            // Show tab based on URL hash
            const hash = window.location.hash;
            if (hash) {
                console.log('Showing tab from hash:', hash);
                const tabTrigger = document.querySelector(`#aboutPageTabs button[data-bs-target="${hash}"]`);
                if (tabTrigger) {
                    const tab = new bootstrap.Tab(tabTrigger);
                    tab.show();
                }
            }

            console.log('Tabs initialized successfully');
        }

        // File Upload Functionality
        function initializeFileUpload() {
            // Handle file upload buttons
            document.addEventListener('click', function(e) {
                if (e.target.closest('.upload-btn')) {
                    const btn = e.target.closest('.upload-btn');
                    const targetField = btn.dataset.target;
                    const input = document.createElement('input');
                    input.type = 'file';
                    input.accept = 'image/*';

                    input.onchange = function(event) {
                        const file = event.target.files[0];
                        if (file) {
                            // Create FormData for file upload
                            const formData = new FormData();
                            formData.append('file', file);

                            // Show loading state
                            const originalContent = btn.innerHTML;
                            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                            btn.disabled = true;

                            // Upload file
                            fetch('/Admin/Settings/UploadFile', {
                                method: 'POST',
                                body: formData
                            })
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    // Update the input field with the uploaded file URL
                                    const targetInput = document.querySelector(`input[name="${targetField}"]`);
                                    if (targetInput) {
                                        targetInput.value = data.url;
                                        // Trigger change event for validation
                                        targetInput.dispatchEvent(new Event('change'));
                                    }
                                } else {
                                    alert('Upload failed: ' + data.message);
                                }
                            })
                            .catch(error => {
                                console.error('Upload error:', error);
                                alert('Upload failed. Please try again.');
                            })
                            .finally(() => {
                                // Reset button state
                                btn.innerHTML = originalContent;
                                btn.disabled = false;
                            });
                        }
                    };

                    input.click();
                }
            });
        }

        // Initialize everything when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            initializeFileUpload();
        });
    </script>
}
