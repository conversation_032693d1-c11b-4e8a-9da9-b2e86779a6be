using Technoloway.Core.Entities;

namespace Technoloway.Core.Interfaces
{
    public interface ILegalPageRepository : IRepository<LegalPage>
    {
        Task<LegalPage?> GetBySlugAsync(string slug);
        Task<LegalPage?> GetBySlugWithSectionsAsync(string slug);
        Task<IEnumerable<LegalPage>> GetActiveAsync();
        Task<LegalPage?> GetWithSectionsAsync(int id);
        Task UpdateWithSectionsAsync(LegalPage legalPage, List<LegalPageSection> sections);
    }
}
