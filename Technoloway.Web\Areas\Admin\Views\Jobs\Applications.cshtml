@model IEnumerable<Technoloway.Core.Entities.JobApplication>

@{
    ViewData["Title"] = "Job Applications";
    Layout = "_AdminLayout";
    var jobListing = ViewBag.JobListing as Technoloway.Core.Entities.JobListing;
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Applications for @(jobListing != null ? jobListing.Title : "Job")</h1>
    <div>
        @if (jobListing != null)
        {
            <a asp-action="Details" asp-route-id="@jobListing.Id" class="btn btn-sm btn-info shadow-sm">
                <i class="fas fa-eye fa-sm text-white-50"></i> View Job
            </a>
        }
        <a asp-action="Index" class="btn btn-sm btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Jobs
        </a>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">All Applications</h6>
    </div>
    <div class="card-body">
        @if (!Model.Any())
        {
            <div class="alert alert-info">
                <p class="mb-0">No applications have been submitted for this job yet.</p>
            </div>
        }
        else
        {
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Applicant</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Date Applied</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model)
                        {
                            <tr>
                                <td>@item.ApplicantName</td>
                                <td>@item.ApplicantEmail</td>
                                <td>@item.ApplicantPhone</td>
                                <td>@item.CreatedAt.ToString("yyyy-MM-dd")</td>
                                <td>
                                    @switch (item.Status)
                                    {
                                        case "Pending":
                                            <span class="badge bg-warning">Pending</span>
                                            break;
                                        case "Reviewed":
                                            <span class="badge bg-info">Reviewed</span>
                                            break;
                                        case "Interviewed":
                                            <span class="badge bg-primary">Interviewed</span>
                                            break;
                                        case "Rejected":
                                            <span class="badge bg-danger">Rejected</span>
                                            break;
                                        case "Hired":
                                            <span class="badge bg-success">Hired</span>
                                            break;
                                        default:
                                            <span class="badge bg-secondary">@item.Status</span>
                                            break;
                                    }
                                </td>
                                <td>
                                    <a asp-action="ApplicationDetails" asp-route-id="@item.Id" class="btn btn-primary btn-sm">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#dataTable').DataTable();
        });
    </script>
}
