@model Technoloway.Web.Areas.Admin.Models.CategoryViewModel

<div class="modal-header">
    <h5 class="modal-title">
        <i class="fas fa-edit me-2"></i>Edit Category
    </h5>
    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
</div>

<form id="categoryForm" asp-action="EditCategory" method="post">
    @Html.AntiForgeryToken()
    <input asp-for="Id" type="hidden" />
    
    <div class="modal-body">
        <div class="mb-3">
            <label asp-for="ParentID" class="form-label">Parent Category</label>
            <select asp-for="ParentID" class="form-select" asp-items="ViewBag.ParentCategories">
                <option value="">-- Root Category --</option>
            </select>
            <span asp-validation-for="ParentID" class="text-danger"></span>
        </div>

        <div class="mb-3">
            <label asp-for="CategName" class="form-label">Category Name</label>
            <input asp-for="CategName" class="form-control" placeholder="Enter category name" />
            <span asp-validation-for="CategName" class="text-danger"></span>
        </div>

        <div class="mb-3">
            <label asp-for="CategDesc" class="form-label">Description</label>
            <textarea asp-for="CategDesc" class="form-control" rows="3" placeholder="Enter category description (optional)"></textarea>
            <span asp-validation-for="CategDesc" class="text-danger"></span>
        </div>
    </div>

    <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="submit" class="btn btn-primary">
            <i class="fas fa-save me-1"></i>Update Category
        </button>
    </div>
</form>

<script>
    $(document).ready(function() {
        // Focus on the first input
        $('#CategName').focus();
        
        // Initialize validation
        $('#categoryForm').validate({
            rules: {
                CategName: {
                    required: true,
                    maxlength: 50
                }
            },
            messages: {
                CategName: {
                    required: "Category name is required",
                    maxlength: "Category name cannot exceed 50 characters"
                }
            }
        });
    });
</script>
