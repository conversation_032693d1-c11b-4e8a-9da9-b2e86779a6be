using Technoloway.Core.Common;
using System.ComponentModel.DataAnnotations;

namespace Technoloway.Core.Entities;

public class TeamMember : BaseEntity
{
    [Required]
    [StringLength(255)]
    public string Name { get; set; } = string.Empty;

    [Required]
    [StringLength(255)]
    public string Position { get; set; } = string.Empty;

    public DateTime? BirthDate { get; set; }

    [StringLength(10)]
    public string? Gender { get; set; }

    [StringLength(10)]
    public string? MaritalStatus { get; set; }

    [StringLength(15)]
    public string? SocialSecurityNo { get; set; }

    public DateTime? HireDate { get; set; }

    [StringLength(40)]
    public string? Address { get; set; }

    [StringLength(15)]
    public string? City { get; set; }

    [StringLength(15)]
    public string? State { get; set; }

    [StringLength(10)]
    public string? ZipCode { get; set; }

    [StringLength(15)]
    public string? Country { get; set; }

    [Required]
    [StringLength(12)]
    public string Phone { get; set; } = string.Empty;

    public decimal? Salary { get; set; }

    [StringLength(10)]
    public string? PayrollMethod { get; set; }

    [StringLength(500)]
    public string? EmpResumeUrl { get; set; }

    public string? Notes { get; set; }

    public string? Bio { get; set; }

    [StringLength(500)]
    public string? PhotoUrl { get; set; }

    [StringLength(255)]
    public string? Email { get; set; }

    [StringLength(500)]
    public string? LinkedInUrl { get; set; }

    [StringLength(500)]
    public string? TwitterUrl { get; set; }

    [StringLength(500)]
    public string? GithubUrl { get; set; }

    public int DisplayOrder { get; set; }
    public bool IsActive { get; set; } = true;

    // Navigation properties
    public ICollection<PayrollRecord> PayrollRecords { get; set; } = new List<PayrollRecord>();
    public ICollection<ProjectTask> Tasks { get; set; } = new List<ProjectTask>();
}
