using Microsoft.Extensions.Options;
using Technoloway.Core.Configuration;

namespace Technoloway.Infrastructure.Configuration;

public class ValidateSecuritySettings : IValidateOptions<SecuritySettings>
{
    public ValidateOptionsResult Validate(string? name, SecuritySettings options)
    {
        var failures = new List<string>();

        if (options.MaxFailedAccessAttempts < 3 || options.MaxFailedAccessAttempts > 10)
        {
            failures.Add("MaxFailedAccessAttempts must be between 3 and 10");
        }

        if (options.LockoutTimeSpan < TimeSpan.FromMinutes(5) || options.LockoutTimeSpan > TimeSpan.FromHours(24))
        {
            failures.Add("LockoutTimeSpan must be between 5 minutes and 24 hours");
        }

        if (options.PasswordRequiredLength < 8 || options.PasswordRequiredLength > 128)
        {
            failures.Add("PasswordRequiredLength must be between 8 and 128 characters");
        }

        if (options.SessionTimeoutMinutes < 5 || options.SessionTimeoutMinutes > 480)
        {
            failures.Add("SessionTimeoutMinutes must be between 5 and 480 minutes");
        }

        if (options.HstsMaxAge < TimeSpan.FromDays(1) || options.HstsMaxAge > TimeSpan.FromDays(730))
        {
            failures.Add("HstsMaxAge must be between 1 day and 2 years");
        }

        return failures.Count > 0 
            ? ValidateOptionsResult.Fail(failures)
            : ValidateOptionsResult.Success;
    }
}

public class ValidateFileUploadSettings : IValidateOptions<FileUploadSettings>
{
    public ValidateOptionsResult Validate(string? name, FileUploadSettings options)
    {
        var failures = new List<string>();

        if (options.MaxFileSizeBytes < 1024 || options.MaxFileSizeBytes > 104857600) // 1KB to 100MB
        {
            failures.Add("MaxFileSizeBytes must be between 1KB and 100MB");
        }

        if (options.AllowedImageExtensions == null || options.AllowedImageExtensions.Length == 0)
        {
            failures.Add("AllowedImageExtensions cannot be empty");
        }

        if (options.AllowedDocumentExtensions == null || options.AllowedDocumentExtensions.Length == 0)
        {
            failures.Add("AllowedDocumentExtensions cannot be empty");
        }

        if (string.IsNullOrWhiteSpace(options.UploadPath))
        {
            failures.Add("UploadPath cannot be empty");
        }

        // Validate extensions format
        if (options.AllowedImageExtensions != null)
        {
            foreach (var ext in options.AllowedImageExtensions)
            {
                if (!ext.StartsWith(".") || ext.Length < 2)
                {
                    failures.Add($"Invalid image extension format: {ext}. Extensions must start with '.' and have at least one character after.");
                }
            }
        }

        if (options.AllowedDocumentExtensions != null)
        {
            foreach (var ext in options.AllowedDocumentExtensions)
            {
                if (!ext.StartsWith(".") || ext.Length < 2)
                {
                    failures.Add($"Invalid document extension format: {ext}. Extensions must start with '.' and have at least one character after.");
                }
            }
        }

        return failures.Count > 0 
            ? ValidateOptionsResult.Fail(failures)
            : ValidateOptionsResult.Success;
    }
}
