@model Technoloway.Core.Entities.Technology

@{
    ViewData["Title"] = "Technology Details";
    Layout = "_AdminLayout";
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Technology Details</h1>
    <div>
        <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-sm btn-primary shadow-sm">
            <i class="fas fa-edit fa-sm text-white-50"></i> Edit
        </a>
        <a asp-action="Index" class="btn btn-sm btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to List
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">@Model.Name</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center">
                        @if (!string.IsNullOrEmpty(Model.IconUrl))
                        {
                            <img src="@Model.IconUrl" alt="@Model.Name" class="img-fluid mb-3" style="max-height: 100px; max-width: 100px; object-fit: contain;" />
                        }
                        else
                        {
                            <div class="text-muted mb-3">
                                <i class="fas fa-microchip fa-4x"></i>
                            </div>
                        }
                    </div>
                    <div class="col-md-9">
                        <h4 class="mb-3">@Model.Name</h4>
                        <p class="text-muted mb-4">@Model.Description</p>
                        
                        <div class="row">
                            <div class="col-sm-6">
                                <strong>Display Order:</strong> @Model.DisplayOrder
                            </div>
                            <div class="col-sm-6">
                                <strong>Created:</strong> @Model.CreatedAt.ToString("MMM dd, yyyy")
                            </div>
                        </div>
                        
                        @if (Model.UpdatedAt.HasValue)
                        {
                            <div class="row mt-2">
                                <div class="col-sm-6">
                                    <strong>Last Updated:</strong> @Model.UpdatedAt.Value.ToString("MMM dd, yyyy")
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Actions</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-primary">
                        <i class="fas fa-edit"></i> Edit Technology
                    </a>
                    <a asp-action="Delete" asp-route-id="@Model.Id" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Delete Technology
                    </a>
                    <a asp-action="Index" class="btn btn-secondary">
                        <i class="fas fa-list"></i> Back to List
                    </a>
                </div>
            </div>
        </div>
        
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Technical Information</h6>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>ID:</strong></td>
                        <td>@Model.Id</td>
                    </tr>
                    <tr>
                        <td><strong>Name:</strong></td>
                        <td>@Model.Name</td>
                    </tr>
                    <tr>
                        <td><strong>Display Order:</strong></td>
                        <td>@Model.DisplayOrder</td>
                    </tr>
                    <tr>
                        <td><strong>Icon URL:</strong></td>
                        <td>
                            @if (!string.IsNullOrEmpty(Model.IconUrl))
                            {
                                <a href="@Model.IconUrl" target="_blank" class="text-truncate d-block" style="max-width: 150px;">
                                    @Model.IconUrl
                                </a>
                            }
                            else
                            {
                                <span class="text-muted">Not set</span>
                            }
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Created:</strong></td>
                        <td>@Model.CreatedAt.ToString("yyyy-MM-dd HH:mm")</td>
                    </tr>
                    @if (Model.UpdatedAt.HasValue)
                    {
                        <tr>
                            <td><strong>Updated:</strong></td>
                            <td>@Model.UpdatedAt.Value.ToString("yyyy-MM-dd HH:mm")</td>
                        </tr>
                    }
                </table>
            </div>
        </div>
    </div>
</div>
