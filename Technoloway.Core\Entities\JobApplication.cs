using Technoloway.Core.Common;

namespace Technoloway.Core.Entities;

public class JobApplication : BaseEntity
{
    public string ApplicantName { get; set; } = string.Empty;
    public string ApplicantEmail { get; set; } = string.Empty;
    public string ApplicantPhone { get; set; } = string.Empty;
    public string ResumeUrl { get; set; } = string.Empty;
    public string CoverLetter { get; set; } = string.Empty;
    public string Status { get; set; } = "Pending"; // Pending, Reviewed, Interviewed, Rejected, Hired
    public string Notes { get; set; } = string.Empty;
    
    // Navigation properties
    public int JobListingId { get; set; }
    public JobListing JobListing { get; set; } = null!;
}
