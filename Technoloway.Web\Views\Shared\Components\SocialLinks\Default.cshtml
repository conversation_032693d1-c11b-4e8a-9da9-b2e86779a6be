@model Technoloway.Web.ViewComponents.SocialLinksViewModel

@{
    string GetSocialIcon(string key)
    {
        return key.ToLower() switch
        {
            var k when k.Contains("facebook") => "fab fa-facebook-f",
            var k when k.Contains("twitter") => "fab fa-twitter",
            var k when k.Contains("linkedin") => "fab fa-linkedin-in",
            var k when k.<PERSON>tains("instagram") => "fab fa-instagram",
            var k when k.Contains("youtube") => "fab fa-youtube",
            var k when k.Contains("github") => "fab fa-github",
            var k when k.Contains("whatsapp") => "fab fa-whatsapp",
            var k when k.Contains("telegram") => "fab fa-telegram",
            var k when k.Contains("discord") => "fab fa-discord",
            var k when k.Contains("tiktok") => "fab fa-tiktok",
            var k when k.Contains("pinterest") => "fab fa-pinterest",
            var k when k.Contains("snapchat") => "fab fa-snapchat",
            _ => "fas fa-link"
        };
    }

    string GetSocialDisplayName(string key)
    {
        return key.Replace("Url", "").Replace("Link", "").Replace("Social", "").Trim();
    }
}

<div class="col-md-4 mb-4 mb-md-0">
    <h5>About @Model.SiteName</h5>
    <p>@Model.SiteTagline</p>

    @if (Model.SocialSettings.Any())
    {
        <div class="social-icons mt-3">
            @foreach (var social in Model.SocialSettings.Where(s => !string.IsNullOrEmpty(s.Value) && s.Value != "#").OrderBy(s => s.Key))
            {
                <a href="@social.Value" target="_blank" class="text-white" title="@GetSocialDisplayName(social.Key)">
                    @if (!string.IsNullOrEmpty(social.Icon))
                    {
                        @if (social.Icon.StartsWith("fa") || social.Icon.Contains("fa-"))
                        {
                            <!-- FontAwesome icon -->
                            <i class="@social.Icon" style="background: none; border: none; font-size: 1.2rem;"></i>
                        }
                        else
                        {
                            <!-- Uploaded image icon -->
                            <img src="@social.Icon" alt="@social.Key icon" style="width: 24px; height: 24px; display: inline-block;" />
                        }
                    }
                    else
                    {
                        <!-- Fallback FontAwesome icon -->
                        <i class="@GetSocialIcon(social.Key)" style="background: none; border: none; font-size: 1.2rem;"></i>
                    }
                </a>
            }
        </div>
    }
</div>
