@model Technoloway.Web.Models.JobApplicationViewModel

@{
    ViewData["Title"] = $"Apply for {Model.JobTitle}";
    ViewData["MetaDescription"] = $"Apply for the {Model.JobTitle} position at Technoloway. Submit your application and join our team.";
    ViewData["MetaKeywords"] = $"apply, job application, {Model.JobTitle.ToLower()}, careers, employment";
}

<!-- <PERSON> Header -->
<div class="bg-dark text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-md-8">
                <h1 class="fw-bold">Apply for @Model.JobTitle</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="/" class="text-white">Home</a></li>
                        <li class="breadcrumb-item"><a asp-action="Index" class="text-white">Careers</a></li>
                        <li class="breadcrumb-item"><a asp-action="Details" asp-route-id="@Model.JobListingId" class="text-white">@Model.JobTitle</a></li>
                        <li class="breadcrumb-item active text-white" aria-current="page">Apply</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- Application Form Section -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card border-0 shadow">
                    <div class="card-body p-4">
                        <h2 class="fw-bold mb-4 text-center">Job Application Form</h2>
                        
                        <form asp-action="Apply" method="post" enctype="multipart/form-data">
                            <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                            
                            <input type="hidden" asp-for="JobListingId" />
                            <input type="hidden" asp-for="JobTitle" />
                            
                            <div class="mb-4">
                                <h4 class="fw-bold">Personal Information</h4>
                                <hr />
                                
                                <div class="mb-3">
                                    <label asp-for="Name" class="form-label"></label>
                                    <input asp-for="Name" class="form-control" placeholder="Enter your full name" />
                                    <span asp-validation-for="Name" class="text-danger"></span>
                                </div>
                                
                                <div class="mb-3">
                                    <label asp-for="Email" class="form-label"></label>
                                    <input asp-for="Email" class="form-control" placeholder="Enter your email address" />
                                    <span asp-validation-for="Email" class="text-danger"></span>
                                </div>
                                
                                <div class="mb-3">
                                    <label asp-for="Phone" class="form-label"></label>
                                    <input asp-for="Phone" class="form-control" placeholder="Enter your phone number" />
                                    <span asp-validation-for="Phone" class="text-danger"></span>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <h4 class="fw-bold">Resume/CV</h4>
                                <hr />
                                
                                <div class="mb-3">
                                    <label asp-for="Resume" class="form-label"></label>
                                    <input asp-for="Resume" class="form-control" type="file" accept=".pdf,.doc,.docx" />
                                    <div class="form-text">Accepted formats: PDF, DOC, DOCX. Maximum file size: 5MB.</div>
                                    <span asp-validation-for="Resume" class="text-danger"></span>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <h4 class="fw-bold">Cover Letter</h4>
                                <hr />
                                
                                <div class="mb-3">
                                    <label asp-for="CoverLetter" class="form-label"></label>
                                    <textarea asp-for="CoverLetter" class="form-control" rows="6" placeholder="Tell us why you're interested in this position and why you would be a good fit"></textarea>
                                    <span asp-validation-for="CoverLetter" class="text-danger"></span>
                                </div>
                            </div>
                            
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="privacyConsent" required />
                                <label class="form-check-label" for="privacyConsent">
                                    I consent to Technoloway storing and processing my personal data for the purpose of this job application.
                                </label>
                            </div>
                            
                            <div class="text-center mt-4">
                                <a asp-action="Details" asp-route-id="@Model.JobListingId" class="btn btn-outline-secondary me-2">
                                    <i class="fas fa-arrow-left me-2"></i> Back to Job Details
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-paper-plane me-2"></i> Submit Application
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
