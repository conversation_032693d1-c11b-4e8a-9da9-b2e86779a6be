@model Technoloway.Core.Entities.Client

@{
    ViewData["Title"] = "Client Details";
    Layout = "_AdminLayout";
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Client Details</h1>
    <div>
        <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-sm btn-primary shadow-sm">
            <i class="fas fa-edit fa-sm text-white-50"></i> Edit
        </a>
        <a asp-action="Projects" asp-route-id="@Model.Id" class="btn btn-sm btn-success shadow-sm">
            <i class="fas fa-project-diagram fa-sm text-white-50"></i> Projects
        </a>
        <a asp-action="Invoices" asp-route-id="@Model.Id" class="btn btn-sm btn-warning shadow-sm">
            <i class="fas fa-file-invoice-dollar fa-sm text-white-50"></i> Invoices
        </a>
        <a asp-action="Index" class="btn btn-sm btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to List
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Client Information</h6>
            </div>
            <div class="card-body">
                @if (!string.IsNullOrEmpty(Model.LogoUrl))
                {
                    <div class="text-center mb-4">
                        <img src="@Model.LogoUrl" alt="@Model.CompanyName Logo" class="img-fluid rounded" style="max-height: 100px;" />
                    </div>
                }
                
                <h4 class="mb-3">@Model.CompanyName</h4>
                
                <dl class="row">
                    <dt class="col-sm-4">Contact</dt>
                    <dd class="col-sm-8">@Model.ContactName</dd>
                    
                    <dt class="col-sm-4">Email</dt>
                    <dd class="col-sm-8">
                        <a href="mailto:@Model.ContactEmail">@Model.ContactEmail</a>
                    </dd>
                    
                    <dt class="col-sm-4">Phone</dt>
                    <dd class="col-sm-8">
                        <a href="tel:@Model.ContactPhone">@Model.ContactPhone</a>
                    </dd>
                    
                    <dt class="col-sm-4">Address</dt>
                    <dd class="col-sm-8">
                        @if (!string.IsNullOrEmpty(Model.Address))
                        {
                            <div>@Model.Address</div>
                        }
                        @if (!string.IsNullOrEmpty(Model.City) || !string.IsNullOrEmpty(Model.State) || !string.IsNullOrEmpty(Model.ZipCode))
                        {
                            <div>
                                @if (!string.IsNullOrEmpty(Model.City))
                                {
                                    <span>@Model.City</span>
                                }
                                @if (!string.IsNullOrEmpty(Model.State))
                                {
                                    <span>@(string.IsNullOrEmpty(Model.City) ? "" : ", ")@Model.State</span>
                                }
                                @if (!string.IsNullOrEmpty(Model.ZipCode))
                                {
                                    <span> @Model.ZipCode</span>
                                }
                            </div>
                        }
                        @if (!string.IsNullOrEmpty(Model.Country))
                        {
                            <div>@Model.Country</div>
                        }
                    </dd>
                    
                    <dt class="col-sm-4">Created</dt>
                    <dd class="col-sm-8">@Model.CreatedAt.ToString("yyyy-MM-dd")</dd>
                    
                    @if (Model.UpdatedAt.HasValue)
                    {
                        <dt class="col-sm-4">Updated</dt>
                        <dd class="col-sm-8">@Model.UpdatedAt.Value.ToString("yyyy-MM-dd")</dd>
                    }
                    
                    @if (!string.IsNullOrEmpty(Model.UserId))
                    {
                        <dt class="col-sm-4">User ID</dt>
                        <dd class="col-sm-8">@Model.UserId</dd>
                    }
                </dl>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="row">
            <div class="col-md-6">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Projects</h6>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-3">
                            <h1 class="h3 mb-0 text-gray-800">@ViewBag.ProjectCount</h1>
                            <p class="text-muted">Total Projects</p>
                        </div>
                        
                        <a asp-action="Projects" asp-route-id="@Model.Id" class="btn btn-success btn-block">
                            <i class="fas fa-project-diagram"></i> View All Projects
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Invoices</h6>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-3">
                            <h1 class="h3 mb-0 text-gray-800">@ViewBag.InvoiceCount</h1>
                            <p class="text-muted">Total Invoices</p>
                        </div>
                        
                        <div class="text-center mb-3">
                            <h4 class="h5 mb-0 text-warning">@ViewBag.PendingInvoiceCount</h4>
                            <p class="text-muted">Pending Invoices</p>
                        </div>
                        
                        <a asp-action="Invoices" asp-route-id="@Model.Id" class="btn btn-warning btn-block">
                            <i class="fas fa-file-invoice-dollar"></i> View All Invoices
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Financial Summary</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="text-center mb-3">
                            <h1 class="h3 mb-0 text-gray-800">@ViewBag.TotalInvoiceAmount.ToString("C")</h1>
                            <p class="text-muted">Total Invoice Amount</p>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="text-center mb-3">
                            <h1 class="h3 mb-0 text-success">@ViewBag.PaidInvoiceAmount.ToString("C")</h1>
                            <p class="text-muted">Paid Amount</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
