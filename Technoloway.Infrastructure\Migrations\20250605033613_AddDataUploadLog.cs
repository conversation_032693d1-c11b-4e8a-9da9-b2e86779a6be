﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Technoloway.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddDataUploadLog : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "DataUploadLogs",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    FileName = table.Column<string>(type: "TEXT", maxLength: 255, nullable: false),
                    FileType = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    FileSizeBytes = table.Column<long>(type: "INTEGER", nullable: false),
                    TargetEntity = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    Operation = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    Status = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    RecordsProcessed = table.Column<int>(type: "INTEGER", nullable: false),
                    RecordsSuccessful = table.Column<int>(type: "INTEGER", nullable: false),
                    RecordsFailed = table.Column<int>(type: "INTEGER", nullable: false),
                    UploadedByUserId = table.Column<string>(type: "TEXT", maxLength: 450, nullable: false),
                    UploadedByUserName = table.Column<string>(type: "TEXT", maxLength: 255, nullable: false),
                    StartedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    CompletedAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    ErrorDetails = table.Column<string>(type: "TEXT", nullable: true),
                    ValidationErrors = table.Column<string>(type: "TEXT", nullable: true),
                    FieldMappings = table.Column<string>(type: "TEXT", nullable: true),
                    BackupFilePath = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    CanRollback = table.Column<bool>(type: "INTEGER", nullable: false),
                    RolledBackAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    RolledBackByUserId = table.Column<string>(type: "TEXT", maxLength: 450, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    IsDeleted = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DataUploadLogs", x => x.Id);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "DataUploadLogs");
        }
    }
}
