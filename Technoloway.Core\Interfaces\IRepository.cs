using System.Linq.Expressions;
using Technoloway.Core.Common;

namespace Technoloway.Core.Interfaces;

public interface IRepository<T> where T : BaseEntity
{
    Task<T?> GetByIdAsync(int id);
    Task<IReadOnlyList<T>> ListAllAsync();
    Task<IReadOnlyList<T>> ListAsync(Expression<Func<T, bool>> predicate);
    Task<T> AddAsync(T entity);
    Task UpdateAsync(T entity);
    Task UpdateDetachedAsync(T entity);
    Task DeleteAsync(T entity); // Soft delete (sets IsDeleted = true)
    Task HardDeleteAsync(T entity); // Hard delete (permanently removes from database)
    Task<int> CountAsync(Expression<Func<T, bool>>? predicate = null);
    IQueryable<T> GetAll();
    IQueryable<T> GetAllAsNoTracking();
}
