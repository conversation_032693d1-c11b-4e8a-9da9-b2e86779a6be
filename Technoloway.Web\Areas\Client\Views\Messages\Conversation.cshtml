@model IEnumerable<Technoloway.Core.Entities.Message>
@{
    ViewData["Title"] = "Project Conversation";
    var project = ViewBag.Project as Technoloway.Core.Entities.Project;
    var clientName = ViewBag.ClientName as string;
}

<div class="container-fluid p-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="d-flex align-items-center mb-2">
                        <a asp-action="Index" class="btn btn-outline-secondary btn-sm me-3">
                            <i class="fas fa-arrow-left me-1"></i> Back to Messages
                        </a>
                        <h1 class="mb-0">Project Conversation</h1>
                    </div>
                    <p class="text-muted mb-0">
                        <i class="fas fa-project-diagram me-1"></i>
                        @project?.Name
                    </p>
                </div>
                <div class="d-flex align-items-center gap-2">
                    <span class="badge bg-success">@Model.Count() Messages</span>
                    <button class="btn btn-outline-primary btn-sm" onclick="scrollToBottom()">
                        <i class="fas fa-arrow-down me-1"></i> Latest
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card chat-container">
                <!-- Messages Area -->
                <div class="card-body p-0">
                    <div class="messages-container" id="messagesContainer">
                        @if (Model.Any())
                        {
                            @foreach (var message in Model)
                            {
                                <div class="message-wrapper @(message.SenderRole == "Client" ? "message-sent" : "message-received")">
                                    <div class="message-bubble @(message.SenderRole == "Client" ? "client" : "admin")">
                                        <div class="message-header">
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm bg-@(message.SenderRole == "Client" ? "primary" : "success") rounded-circle d-flex align-items-center justify-content-center me-2">
                                                    <i class="fas fa-@(message.SenderRole == "Client" ? "user" : "user-tie") text-white"></i>
                                                </div>
                                                <div>
                                                    <h6 class="mb-0">@message.SenderName</h6>
                                                    <small class="text-muted">@message.SenderRole</small>
                                                </div>
                                            </div>
                                            <small class="text-muted">@message.CreatedAt.ToString("MMM dd, HH:mm")</small>
                                        </div>
                                        <div class="message-content">
                                            @message.Content
                                        </div>
                                        @if (message.IsRead)
                                        {
                                            <div class="message-status">
                                                <small class="text-muted">
                                                    <i class="fas fa-check-double"></i> Read
                                                </small>
                                            </div>
                                        }
                                    </div>
                                </div>
                            }
                        }
                        else
                        {
                            <div class="empty-messages text-center py-5">
                                <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No messages yet</h5>
                                <p class="text-muted">Start the conversation by sending your first message!</p>
                            </div>
                        }
                    </div>
                </div>

                <!-- Message Input -->
                <div class="card-footer bg-light border-0">
                    <form asp-action="SendMessage" method="post" id="messageForm" class="d-flex gap-2">
                        <input type="hidden" name="projectId" value="@project?.Id" />
                        <div class="flex-grow-1">
                            <div class="input-group">
                                <textarea name="content" id="messageInput" class="form-control" rows="1"
                                         placeholder="Type your message..." required
                                         style="resize: none; min-height: 42px; max-height: 120px;"></textarea>
                                <button type="button" class="btn btn-outline-secondary" onclick="toggleEmojiPicker()">
                                    <i class="fas fa-smile"></i>
                                </button>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary" id="sendButton">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </form>

                    <!-- Typing Indicator -->
                    <div id="typingIndicator" class="typing-indicator mt-2" style="display: none;">
                        <small class="text-muted">
                            <i class="fas fa-circle typing-dot"></i>
                            <i class="fas fa-circle typing-dot"></i>
                            <i class="fas fa-circle typing-dot"></i>
                            Team is typing...
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.chat-container {
    height: calc(100vh - 200px);
    display: flex;
    flex-direction: column;
}

.messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.message-wrapper {
    margin-bottom: 1.5rem;
    display: flex;
}

.message-wrapper.message-sent {
    justify-content: flex-end;
}

.message-wrapper.message-received {
    justify-content: flex-start;
}

.message-bubble {
    max-width: 70%;
    padding: 1rem 1.25rem;
    border-radius: 1.5rem;
    position: relative;
    word-wrap: break-word;
    animation: messageSlideIn 0.3s ease-out;
}

.message-bubble.client {
    background: linear-gradient(135deg, var(--bs-primary) 0%, #0056b3 100%);
    color: white;
    border-bottom-right-radius: 0.5rem;
}

.message-bubble.admin {
    background: white;
    color: #333;
    border: 1px solid #e9ecef;
    border-bottom-left-radius: 0.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.message-bubble.admin .message-header {
    border-bottom-color: #e9ecef;
}

.message-content {
    line-height: 1.5;
    white-space: pre-wrap;
}

.message-status {
    margin-top: 0.5rem;
    text-align: right;
}

.avatar-sm {
    width: 32px;
    height: 32px;
}

.empty-messages {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 300px;
}

.typing-indicator {
    animation: fadeIn 0.3s ease-in;
}

.typing-dot {
    animation: typingDots 1.4s infinite;
    font-size: 0.5rem;
    margin: 0 1px;
}

.typing-dot:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
    animation-delay: 0.4s;
}

@@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@@keyframes typingDots {
    0%, 60%, 100% {
        opacity: 0.3;
    }
    30% {
        opacity: 1;
    }
}

/* Auto-resize textarea */
#messageInput {
    transition: height 0.2s ease;
}

/* Scroll styling */
.messages-container::-webkit-scrollbar {
    width: 6px;
}

.messages-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    scrollToBottom();

    // Auto-resize textarea
    const messageInput = document.getElementById('messageInput');
    messageInput.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = Math.min(this.scrollHeight, 120) + 'px';
    });

    // Handle form submission
    document.getElementById('messageForm').addEventListener('submit', function(e) {
        e.preventDefault();
        sendMessage();
    });

    // Handle Enter key (Shift+Enter for new line)
    messageInput.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });
});

function sendMessage() {
    const form = document.getElementById('messageForm');
    const messageInput = document.getElementById('messageInput');
    const sendButton = document.getElementById('sendButton');
    const content = messageInput.value.trim();

    if (!content) {
        return;
    }

    // Disable form while sending
    sendButton.disabled = true;
    sendButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

    const formData = new FormData(form);

    fetch(form.action, {
        method: 'POST',
        body: formData,
        headers: {
            'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Add message to UI
            addMessageToUI(data.messageData);

            // Clear input
            messageInput.value = '';
            messageInput.style.height = 'auto';

            // Scroll to bottom
            scrollToBottom();
        } else {
            alert('Error sending message: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error sending message. Please try again.');
    })
    .finally(() => {
        // Re-enable form
        sendButton.disabled = false;
        sendButton.innerHTML = '<i class="fas fa-paper-plane"></i>';
        messageInput.focus();
    });
}

function addMessageToUI(messageData) {
    const messagesContainer = document.getElementById('messagesContainer');

    const messageWrapper = document.createElement('div');
    messageWrapper.className = 'message-wrapper message-sent';

    messageWrapper.innerHTML = `
        <div class="message-bubble client">
            <div class="message-header">
                <div class="d-flex align-items-center">
                    <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                        <i class="fas fa-user text-white"></i>
                    </div>
                    <div>
                        <h6 class="mb-0">${messageData.senderName}</h6>
                        <small class="text-white-50">Client</small>
                    </div>
                </div>
                <small class="text-white-50">${messageData.createdAt}</small>
            </div>
            <div class="message-content">
                ${messageData.content}
            </div>
        </div>
    `;

    messagesContainer.appendChild(messageWrapper);
}

function scrollToBottom() {
    const messagesContainer = document.getElementById('messagesContainer');
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

function toggleEmojiPicker() {
    // Placeholder for emoji picker functionality
    console.log('Emoji picker would open here');
}
</script>
