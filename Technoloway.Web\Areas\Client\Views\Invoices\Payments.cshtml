@model IEnumerable<Technoloway.Core.Entities.Payment>
@{
    ViewData["Title"] = "Payment History";
    var totalPayments = ViewBag.TotalPayments as int? ?? 0;
    var totalAmount = ViewBag.TotalAmount as decimal? ?? 0;
    var pendingPayments = ViewBag.PendingPayments as int? ?? 0;
    var completedPayments = ViewBag.CompletedPayments as int? ?? 0;
    var failedPayments = ViewBag.FailedPayments as int? ?? 0;
}

<div class="container-fluid p-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="d-flex align-items-center mb-2">
                        <a asp-action="Index" class="btn btn-outline-secondary btn-sm me-3">
                            <i class="fas fa-arrow-left me-1"></i> Back to Invoices
                        </a>
                        <h1 class="mb-0">Payment History</h1>
                    </div>
                    <p class="text-muted">Track all your payment transactions</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary" onclick="exportPayments()">
                        <i class="fas fa-download me-1"></i> Export
                    </button>
                    <button class="btn btn-outline-secondary" onclick="window.print()">
                        <i class="fas fa-print me-1"></i> Print
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="text-primary mb-2">
                        <i class="fas fa-credit-card fa-2x"></i>
                    </div>
                    <h3 class="mb-1">@totalPayments</h3>
                    <p class="text-muted mb-0">Total Payments</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="text-success mb-2">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                    <h3 class="mb-1">@completedPayments</h3>
                    <p class="text-muted mb-0">Completed</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="text-warning mb-2">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                    <h3 class="mb-1">@pendingPayments</h3>
                    <p class="text-muted mb-0">Pending</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="text-danger mb-2">
                        <i class="fas fa-times-circle fa-2x"></i>
                    </div>
                    <h3 class="mb-1">@failedPayments</h3>
                    <p class="text-muted mb-0">Failed</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Total Amount Card -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm bg-gradient-primary text-white">
                <div class="card-body text-center py-4">
                    <h2 class="mb-1">$@totalAmount.ToString("N2")</h2>
                    <p class="mb-0 opacity-75">Total Amount Paid</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Payments List -->
    <div class="row">
        <div class="col-12">
            @if (Model.Any())
            {
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">Payment Transactions</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Payment Date</th>
                                        <th>Invoice</th>
                                        <th>Amount</th>
                                        <th>Method</th>
                                        <th>Status</th>
                                        <th>Notes</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var payment in Model)
                                    {
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong>@payment.PaymentDate.ToString("MMM dd, yyyy")</strong>
                                                    <br>
                                                    <small class="text-muted">@payment.PaymentDate.ToString("HH:mm")</small>
                                                </div>
                                            </td>
                                            <td>
                                                @if (payment.Invoice != null)
                                                {
                                                    <a asp-action="Details" asp-route-id="@payment.InvoiceId" 
                                                       class="text-decoration-none">
                                                        @payment.Invoice.InvoiceNumber
                                                    </a>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">N/A</span>
                                                }
                                            </td>
                                            <td>
                                                <strong class="text-success">$@payment.Amount.ToString("N2")</strong>
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    @switch (payment.PaymentMethod)
                                                    {
                                                        case "Credit Card":
                                                            <i class="fas fa-credit-card text-primary me-2"></i>
                                                            break;
                                                        case "Bank Transfer":
                                                            <i class="fas fa-university text-info me-2"></i>
                                                            break;
                                                        case "PayPal":
                                                            <i class="fab fa-paypal text-primary me-2"></i>
                                                            break;
                                                        case "Check":
                                                            <i class="fas fa-money-check text-secondary me-2"></i>
                                                            break;
                                                        default:
                                                            <i class="fas fa-dollar-sign text-muted me-2"></i>
                                                            break;
                                                    }
                                                    @payment.PaymentMethod
                                                </div>
                                            </td>
                                            <td>
                                                @switch (payment.Status)
                                                {
                                                    case "Completed":
                                                        <span class="badge bg-success">
                                                            <i class="fas fa-check me-1"></i>Completed
                                                        </span>
                                                        break;
                                                    case "Pending":
                                                        <span class="badge bg-warning">
                                                            <i class="fas fa-clock me-1"></i>Pending
                                                        </span>
                                                        break;
                                                    case "Failed":
                                                        <span class="badge bg-danger">
                                                            <i class="fas fa-times me-1"></i>Failed
                                                        </span>
                                                        break;
                                                    case "Refunded":
                                                        <span class="badge bg-info">
                                                            <i class="fas fa-undo me-1"></i>Refunded
                                                        </span>
                                                        break;
                                                    default:
                                                        <span class="badge bg-secondary">@payment.Status</span>
                                                        break;
                                                }
                                            </td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(payment.Notes))
                                                {
                                                    <span class="text-muted">@payment.Notes</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group">
                                                    @if (payment.Invoice != null)
                                                    {
                                                        <a asp-action="Details" asp-route-id="@payment.InvoiceId" 
                                                           class="btn btn-sm btn-outline-primary" title="View Invoice">
                                                            <i class="fas fa-file-invoice"></i>
                                                        </a>
                                                    }
                                                    @if (payment.Status == "Completed")
                                                    {
                                                        <button type="button" class="btn btn-sm btn-outline-secondary" 
                                                                onclick="downloadReceipt(@payment.Id)" title="Download Receipt">
                                                            <i class="fas fa-receipt"></i>
                                                        </button>
                                                    }
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            }
            else
            {
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center py-5">
                        <div class="mb-4">
                            <i class="fas fa-credit-card fa-4x text-muted"></i>
                        </div>
                        <h4 class="text-muted mb-3">No Payments Found</h4>
                        <p class="text-muted mb-4">You haven't made any payments yet. Payment history will appear here once you start making payments.</p>
                        <a asp-action="Index" class="btn btn-primary">
                            <i class="fas fa-file-invoice-dollar me-1"></i> View Invoices
                        </a>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-info) 100%);
}

.card:hover {
    transform: translateY(-2px);
    transition: transform 0.2s ease-in-out;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.opacity-75 {
    opacity: 0.75;
}

@@media print {
    .btn, .card-header {
        display: none !important;
    }
    
    .container-fluid {
        padding: 0 !important;
    }
}
</style>

<script>
function downloadReceipt(paymentId) {
    // In a real application, this would generate and download a receipt
    alert('Receipt download functionality would be implemented here for payment ID: ' + paymentId);
}

function exportPayments() {
    // In a real application, this would export payment data to CSV/Excel
    var table = document.querySelector('table');
    if (!table) {
        alert('No payment data to export');
        return;
    }
    
    // Simple CSV export simulation
    var csv = [];
    var rows = table.querySelectorAll('tr');
    
    for (var i = 0; i < rows.length; i++) {
        var row = [], cols = rows[i].querySelectorAll('td, th');
        
        for (var j = 0; j < cols.length - 1; j++) { // Exclude actions column
            var cellText = cols[j].innerText.replace(/"/g, '""');
            row.push('"' + cellText + '"');
        }
        
        csv.push(row.join(','));
    }
    
    var csvContent = csv.join('\n');
    var blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    var link = document.createElement('a');
    
    if (link.download !== undefined) {
        var url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', 'payment-history.csv');
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

// Auto-refresh payment status every 30 seconds for pending payments
setInterval(function() {
    var pendingBadges = document.querySelectorAll('.badge.bg-warning');
    if (pendingBadges.length > 0) {
        // In a real application, you would make an AJAX call to check payment status
        console.log('Checking payment status updates...');
    }
}, 30000);
</script>
