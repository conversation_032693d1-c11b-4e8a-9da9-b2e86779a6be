@page
@model Technoloway.Web.Areas.Identity.Pages.Account.Manage.IndexModel
@{
    ViewData["Title"] = "Profile";
    ViewData["ActivePage"] = "Index";
    Layout = "_ClientLoginLayout";
}

<div class="modern-client-login-header">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="login-header-content text-center">
                    <div class="login-breadcrumb">
                        <a href="/" class="breadcrumb-link">
                            <i class="fas fa-home"></i>
                            <span>Home</span>
                        </a>
                        <span class="breadcrumb-separator">•</span>
                        <span class="breadcrumb-current">Account Settings</span>
                    </div>
                    <h1 class="login-title">
                        <span class="title-highlight">Account</span> Settings
                    </h1>
                    <p class="login-subtitle">
                        Manage your account information and security settings
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<section class="modern-section client-login-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="modern-card login-card">
                    <div class="card-header">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-user-cog me-2"></i>Profile Information
                        </h3>
                    </div>

                    <div class="card-body">
                        @if (Model.StatusMessage != null)
                        {
                            <div class="modern-alert @(Model.StatusMessage.Contains("Error") ? "error" : "success") mb-4">
                                <div class="alert-icon">
                                    <i class="fas @(Model.StatusMessage.Contains("Error") ? "fa-exclamation-triangle" : "fa-check-circle")"></i>
                                </div>
                                <div class="alert-content">
                                    <p class="mb-0">@Model.StatusMessage</p>
                                </div>
                            </div>
                        }

                        <form id="profile-form" method="post" class="modern-form">
                            <div asp-validation-summary="ModelOnly" class="validation-summary"></div>
                            
                            <div class="form-group">
                                <label asp-for="Username" class="form-label">
                                    <i class="fas fa-user"></i>
                                    <span>Username</span>
                                </label>
                                <input asp-for="Username" class="form-input" disabled />
                            </div>

                            <div class="form-group">
                                <label asp-for="Input.Email" class="form-label">
                                    <i class="fas fa-envelope"></i>
                                    <span>Email</span>
                                </label>
                                <input asp-for="Input.Email" class="form-input" />
                                <span asp-validation-for="Input.Email" class="form-error"></span>
                                @if (Model.IsEmailConfirmed)
                                {
                                    <div class="form-help text-success">
                                        <i class="fas fa-check-circle me-1"></i> Email confirmed
                                    </div>
                                }
                                else
                                {
                                    <div class="form-help text-warning">
                                        <i class="fas fa-exclamation-triangle me-1"></i> 
                                        Email not confirmed. <a asp-page="./ConfirmEmail">Send confirmation email</a>
                                    </div>
                                }
                            </div>

                            <div class="form-group">
                                <label asp-for="Input.PhoneNumber" class="form-label">
                                    <i class="fas fa-phone"></i>
                                    <span>Phone Number</span>
                                </label>
                                <input asp-for="Input.PhoneNumber" class="form-input" />
                                <span asp-validation-for="Input.PhoneNumber" class="form-error"></span>
                            </div>

                            <div class="d-grid gap-2">
                                <button id="update-profile-button" type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Save Changes
                                </button>
                            </div>
                        </form>

                        <hr class="my-4">

                        <div class="row">
                            <div class="col-md-6">
                                <h5><i class="fas fa-key me-2"></i>Security</h5>
                                <p class="text-muted">Manage your password and security settings</p>
                                <a asp-page="./ChangePassword" class="btn btn-outline-primary">
                                    <i class="fas fa-lock me-2"></i>Change Password
                                </a>
                            </div>
                            <div class="col-md-6">
                                <h5><i class="fas fa-download me-2"></i>Personal Data</h5>
                                <p class="text-muted">Download or delete your personal data</p>
                                <a asp-page="./PersonalData" class="btn btn-outline-info">
                                    <i class="fas fa-user-shield me-2"></i>Manage Data
                                </a>
                            </div>
                        </div>

                        @if (User.IsInRole("Client"))
                        {
                            <hr class="my-4">
                            <div class="text-center">
                                <h5><i class="fas fa-building me-2"></i>Company Profile</h5>
                                <p class="text-muted">Manage your company information and preferences</p>
                                <a asp-area="Client" asp-controller="Profile" asp-action="Index" class="btn btn-success">
                                    <i class="fas fa-building me-2"></i>Go to Company Profile
                                </a>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
