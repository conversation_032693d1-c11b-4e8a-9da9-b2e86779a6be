@model Technoloway.Core.Entities.ProjectDocument

@{
    ViewData["Title"] = "Document Details";
    string fileExtension = System.IO.Path.GetExtension(Model.FileName).TrimStart('.').ToLower();
    string iconClass = "fas fa-file text-muted";

    if (fileExtension.Equals("pdf"))
        iconClass = "fas fa-file-pdf text-danger";
    else if (fileExtension.Equals("doc") || fileExtension.Equals("docx"))
        iconClass = "fas fa-file-word text-primary";
    else if (fileExtension.Equals("xls") || fileExtension.Equals("xlsx"))
        iconClass = "fas fa-file-excel text-success";
    else if (fileExtension.Equals("ppt") || fileExtension.Equals("pptx"))
        iconClass = "fas fa-file-powerpoint text-warning";
    else if (fileExtension.Equals("jpg") || fileExtension.Equals("jpeg") || fileExtension.Equals("png") || fileExtension.Equals("gif"))
        iconClass = "fas fa-file-image text-info";
    else if (fileExtension.Equals("zip") || fileExtension.Equals("rar"))
        iconClass = "fas fa-file-archive text-secondary";

    bool canPreview = fileExtension.Equals("pdf") || fileExtension.Equals("jpg") ||
                     fileExtension.Equals("jpeg") || fileExtension.Equals("png") ||
                     fileExtension.Equals("gif") || fileExtension.Equals("txt") ||
                     fileExtension.Equals("md");
}

<div class="container-fluid p-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="text-gradient mb-2">
                        <i class="@iconClass me-2"></i>Document Details
                    </h1>
                    <p class="text-muted">View document information and download</p>
                </div>
                <div class="d-flex gap-2">
                    <a asp-action="Index" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i> Back to Documents
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Document Information -->
        <div class="col-lg-8 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Document Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">File Name</label>
                            <div class="d-flex align-items-center">
                                <i class="@iconClass me-2 fa-lg"></i>
                                <span class="fw-bold">@Model.FileName</span>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">File Type</label>
                            <div>
                                <span class="badge bg-primary">@Model.FileType.ToUpper()</span>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">File Size</label>
                            <div>
                                <span class="fw-bold">@(Model.FileSize / 1024) KB</span>
                                <small class="text-muted">(@Model.FileSize bytes)</small>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Project</label>
                            <div>
                                <span class="fw-bold">@(Model.Project?.Name ?? "Unknown Project")</span>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Uploaded By</label>
                            <div>
                                <span class="fw-bold">@Model.UploadedByName</span>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Upload Date</label>
                            <div>
                                <span class="fw-bold">@Model.CreatedAt.ToString("MMMM dd, yyyy")</span>
                                <small class="text-muted d-block">@Model.CreatedAt.ToString("h:mm tt")</small>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>

        <!-- Actions Sidebar -->
        <div class="col-lg-4 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h6 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a asp-action="Download" asp-route-id="@Model.Id" class="btn btn-success">
                            <i class="fas fa-download me-2"></i>Download File
                        </a>
                        @if (canPreview)
                        {
                            <a asp-action="Preview" asp-route-id="@Model.Id" class="btn btn-info">
                                <i class="fas fa-search me-2"></i>Preview File
                            </a>
                        }
                        <a href="@Model.FileUrl" target="_blank" class="btn btn-outline-primary">
                            <i class="fas fa-external-link-alt me-2"></i>Open in New Tab
                        </a>
                        <hr>
                        <a asp-area="Client" asp-controller="Projects" asp-action="Details" asp-route-id="@Model.ProjectId" class="btn btn-outline-secondary">
                            <i class="fas fa-project-diagram me-2"></i>View Project
                        </a>
                        <a asp-area="Client" asp-controller="Projects" asp-action="Documents" asp-route-id="@Model.ProjectId" class="btn btn-outline-secondary">
                            <i class="fas fa-folder me-2"></i>Project Documents
                        </a>
                    </div>
                </div>
            </div>

            <!-- File Preview Card (for images) -->
            @if (fileExtension.Equals("jpg") || fileExtension.Equals("jpeg") || fileExtension.Equals("png") || fileExtension.Equals("gif"))
            {
                <div class="card border-0 shadow-sm mt-4">
                    <div class="card-header bg-white">
                        <h6 class="mb-0">
                            <i class="fas fa-image me-2"></i>Preview
                        </h6>
                    </div>
                    <div class="card-body text-center">
                        <img src="@Model.FileUrl" alt="@Model.FileName" class="img-fluid rounded" style="max-height: 200px;">
                    </div>
                </div>
            }
        </div>
    </div>

    <!-- File Statistics -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>File Statistics
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3 mb-3">
                            <div class="p-3">
                                <i class="fas fa-calendar-alt fa-2x text-primary mb-2"></i>
                                <h6 class="text-muted">Days Since Upload</h6>
                                <h4 class="mb-0">@((DateTime.Now - Model.CreatedAt).Days)</h4>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="p-3">
                                <i class="fas fa-hdd fa-2x text-success mb-2"></i>
                                <h6 class="text-muted">Size (MB)</h6>
                                <h4 class="mb-0">@Math.Round((double)Model.FileSize / 1024 / 1024, 2)</h4>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="p-3">
                                <i class="fas fa-file-alt fa-2x text-info mb-2"></i>
                                <h6 class="text-muted">File Extension</h6>
                                <h4 class="mb-0">.@fileExtension.ToUpper()</h4>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="p-3">
                                <i class="fas fa-eye fa-2x text-warning mb-2"></i>
                                <h6 class="text-muted">Preview Available</h6>
                                <h4 class="mb-0">
                                    @if (canPreview)
                                    {
                                        <i class="fas fa-check text-success"></i>
                                    }
                                    else
                                    {
                                        <i class="fas fa-times text-danger"></i>
                                    }
                                </h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.text-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.card {
    transition: all 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-1px);
}

.btn {
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-1px);
}
</style>
