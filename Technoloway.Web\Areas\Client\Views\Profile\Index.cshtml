@model Technoloway.Web.Areas.Client.Models.ClientProfileViewModel
@{
    ViewData["Title"] = "Profile Settings";
}

<div class="container-fluid p-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <div class="icon-circle bg-primary">
                            <i class="fas fa-user-cog text-white"></i>
                        </div>
                    </div>
                    <div>
                        <h1 class="text-gradient mb-1">Profile Settings</h1>
                        <p class="text-muted mb-0">Manage your company information and preferences</p>
                    </div>
                </div>
                <div>
                    <button type="button" class="btn btn-outline-primary" id="editProfileBtn">
                        <i class="fas fa-edit me-2"></i>Edit Profile
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Information -->
    <div class="row">
        <div class="col-lg-8">
            <!-- Company Information Card -->
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-gradient-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-building me-2"></i>Company Information
                    </h5>
                </div>
                <div class="card-body p-4">
                    <!-- View Mode -->
                    <div id="viewMode">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label text-muted small">Company Name</label>
                                <p class="fw-bold">@Model.CompanyName</p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-muted small">Contact Name</label>
                                <p class="fw-bold">@Model.ContactName</p>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label text-muted small">Contact Email</label>
                                <p class="fw-bold">@Model.ContactEmail</p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-muted small">Contact Phone</label>
                                <p class="fw-bold">@(string.IsNullOrEmpty(Model.ContactPhone) ? "Not provided" : Model.ContactPhone)</p>
                            </div>
                        </div>
                        
                        @if (!string.IsNullOrEmpty(Model.Address) || !string.IsNullOrEmpty(Model.City))
                        {
                            <div class="mb-3">
                                <label class="form-label text-muted small">Address</label>
                                <p class="fw-bold">
                                    @if (!string.IsNullOrEmpty(Model.Address))
                                    {
                                        @Model.Address<br>
                                    }
                                    @if (!string.IsNullOrEmpty(Model.City) || !string.IsNullOrEmpty(Model.State) || !string.IsNullOrEmpty(Model.ZipCode))
                                    {
                                        @Model.City @Model.State @Model.ZipCode<br>
                                    }
                                    @Model.Country
                                </p>
                            </div>
                        }
                        
                        @if (!string.IsNullOrEmpty(Model.LogoUrl))
                        {
                            <div class="mb-3">
                                <label class="form-label text-muted small">Company Logo</label>
                                <div>
                                    <img src="@Model.LogoUrl" alt="Company Logo" class="img-thumbnail" style="max-height: 100px;">
                                </div>
                            </div>
                        }
                    </div>

                    <!-- Edit Mode -->
                    <div id="editMode" style="display: none;">
                        <form asp-action="Update" method="post" class="needs-validation" novalidate>
                            <input asp-for="Id" type="hidden">
                            <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                            
                            <!-- Company Details -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input asp-for="CompanyName" class="form-control" placeholder="Company Name" required>
                                        <label asp-for="CompanyName"></label>
                                        <span asp-validation-for="CompanyName" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input asp-for="ContactName" class="form-control" placeholder="Contact Name" required>
                                        <label asp-for="ContactName"></label>
                                        <span asp-validation-for="ContactName" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>

                            <!-- Contact Information -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input asp-for="ContactEmail" type="email" class="form-control" placeholder="Contact Email" required>
                                        <label asp-for="ContactEmail"></label>
                                        <span asp-validation-for="ContactEmail" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input asp-for="ContactPhone" type="tel" class="form-control" placeholder="Contact Phone">
                                        <label asp-for="ContactPhone"></label>
                                        <span asp-validation-for="ContactPhone" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>

                            <!-- Address Information -->
                            <div class="form-floating mb-3">
                                <input asp-for="Address" class="form-control" placeholder="Street Address">
                                <label asp-for="Address"></label>
                                <span asp-validation-for="Address" class="text-danger"></span>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <div class="form-floating">
                                        <input asp-for="City" class="form-control" placeholder="City">
                                        <label asp-for="City"></label>
                                        <span asp-validation-for="City" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-floating">
                                        <input asp-for="State" class="form-control" placeholder="State/Province">
                                        <label asp-for="State"></label>
                                        <span asp-validation-for="State" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-floating">
                                        <input asp-for="ZipCode" class="form-control" placeholder="Zip/Postal Code">
                                        <label asp-for="ZipCode"></label>
                                        <span asp-validation-for="ZipCode" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input asp-for="Country" class="form-control" placeholder="Country">
                                        <label asp-for="Country"></label>
                                        <span asp-validation-for="Country" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input asp-for="LogoUrl" type="url" class="form-control" placeholder="Logo URL">
                                        <label asp-for="LogoUrl"></label>
                                        <span asp-validation-for="LogoUrl" class="text-danger"></span>
                                    </div>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="d-flex justify-content-end gap-2">
                                <button type="button" class="btn btn-outline-secondary" id="cancelEditBtn">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Save Changes
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions Sidebar -->
        <div class="col-lg-4">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-light">
                    <h6 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a asp-area="Client" asp-controller="Home" asp-action="Index" class="btn btn-outline-primary">
                            <i class="fas fa-tachometer-alt me-2"></i>Go to Dashboard
                        </a>
                        <a asp-area="Client" asp-controller="Projects" asp-action="Index" class="btn btn-outline-success">
                            <i class="fas fa-project-diagram me-2"></i>View Projects
                        </a>
                        <a asp-area="Identity" asp-page="/Account/Manage/Index" class="btn btn-outline-info">
                            <i class="fas fa-key me-2"></i>Account Settings
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    
    <script>
        document.getElementById('editProfileBtn').addEventListener('click', function() {
            document.getElementById('viewMode').style.display = 'none';
            document.getElementById('editMode').style.display = 'block';
            this.style.display = 'none';
        });
        
        document.getElementById('cancelEditBtn').addEventListener('click', function() {
            document.getElementById('editMode').style.display = 'none';
            document.getElementById('viewMode').style.display = 'block';
            document.getElementById('editProfileBtn').style.display = 'block';
        });
    </script>
}
