@model Technoloway.Web.Areas.Admin.Models.LoginViewModel

@{
    ViewData["Title"] = "Admin Login";
    Layout = "_AdminLoginLayout";
}

<!-- Modern Login Header -->
<div class="modern-login-header">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="login-header-content text-center">
                    <div class="login-breadcrumb">
                        <a href="/" class="breadcrumb-link">
                            <i class="fas fa-home"></i>
                            <span>Home</span>
                        </a>
                        <span class="breadcrumb-separator">•</span>
                        <span class="breadcrumb-current">Admin Login</span>
                    </div>
                    <h1 class="login-title">
                        <span class="title-highlight">Admin</span> Access
                    </h1>
                    <p class="login-subtitle">
                        Secure access to your administrative dashboard and management tools
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modern Login Section -->
<section class="modern-section login-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-5 col-md-7">
                <div class="modern-card login-card">
                    <div class="card-header text-center">
                        <div class="login-logo">
                            <img src="~/images/technoloway-logo.svg" alt="Technoloway Logo" class="brand-logo" />
                        </div>
                        <h3 class="card-title">
                            <span class="title-highlight">Welcome</span> Back
                        </h3>
                        <p class="card-subtitle">
                            Sign in to access your admin dashboard
                        </p>
                    </div>

                    <div class="card-body">
                        @if (TempData["ErrorMessage"] != null)
                        {
                            <div class="modern-alert error">
                                <div class="alert-icon">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="alert-content">
                                    <h4>Login Failed</h4>
                                    <p>@TempData["ErrorMessage"]</p>
                                </div>
                            </div>
                        }

                        <form asp-action="Login" asp-route-returnurl="@ViewData["ReturnUrl"]" method="post" class="modern-form">
                            <div asp-validation-summary="ModelOnly" class="validation-summary"></div>

                            <div class="form-group">
                                <label asp-for="Email" class="form-label">
                                    <i class="fas fa-envelope"></i>
                                    <span>Email Address</span>
                                </label>
                                <input asp-for="Email" class="form-input" placeholder="Enter your admin email" autocomplete="username" />
                                <span asp-validation-for="Email" class="form-error"></span>
                            </div>

                            <div class="form-group">
                                <label asp-for="Password" class="form-label">
                                    <i class="fas fa-lock"></i>
                                    <span>Password</span>
                                </label>
                                <div class="password-input-wrapper">
                                    <input asp-for="Password" class="form-input" placeholder="Enter your password" autocomplete="current-password" />
                                    <button type="button" class="password-toggle" onclick="togglePassword()">
                                        <i class="fas fa-eye" id="password-eye"></i>
                                    </button>
                                </div>
                                <span asp-validation-for="Password" class="form-error"></span>
                            </div>

                            <div class="form-check-wrapper">
                                <label class="modern-checkbox">
                                    <input asp-for="RememberMe" type="checkbox" />
                                    <span class="checkmark"></span>
                                    <span class="checkbox-text">Keep me signed in</span>
                                </label>
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="modern-btn primary large full-width">
                                    <span class="btn-text">Sign In</span>
                                    <span class="btn-icon">
                                        <i class="fas fa-sign-in-alt"></i>
                                    </span>
                                </button>
                            </div>
                        </form>

                        <div class="login-footer">
                            <div class="footer-links">
                                <a href="/" class="footer-link">
                                    <i class="fas fa-arrow-left"></i>
                                    <span>Back to Website</span>
                                </a>
                                <a href="#" class="footer-link" onclick="showForgotPassword()">
                                    <i class="fas fa-question-circle"></i>
                                    <span>Need Help?</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Security Notice -->
                <div class="security-notice">
                    <div class="security-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="security-content">
                        <h5>Secure Admin Access</h5>
                        <p>This is a protected area. All login attempts are monitored and logged for security purposes.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script>
        // Enhanced login interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Form input animations
            const formInputs = document.querySelectorAll('.form-input');

            formInputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                });

                input.addEventListener('blur', function() {
                    if (!this.value) {
                        this.parentElement.classList.remove('focused');
                    }
                });

                // Check if input has value on load
                if (input.value) {
                    input.parentElement.classList.add('focused');
                }
            });

            // Form submission enhancement
            const loginForm = document.querySelector('.modern-form');
            const submitBtn = document.querySelector('.modern-btn.primary');

            if (loginForm && submitBtn) {
                loginForm.addEventListener('submit', function(e) {
                    submitBtn.classList.add('loading');
                    submitBtn.innerHTML = `
                        <span class="btn-text">Signing In...</span>
                        <span class="btn-icon">
                            <i class="fas fa-spinner fa-spin"></i>
                        </span>
                    `;
                });
            }

            // Floating elements animation
            const floatingElements = document.querySelectorAll('.floating-element');

            floatingElements.forEach((element, index) => {
                element.style.animationDelay = `${index * 0.5}s`;
                element.classList.add('floating-animation');
            });
        });

        // Password toggle functionality
        function togglePassword() {
            const passwordInput = document.querySelector('input[name="Password"]');
            const passwordEye = document.getElementById('password-eye');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                passwordEye.classList.remove('fa-eye');
                passwordEye.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                passwordEye.classList.remove('fa-eye-slash');
                passwordEye.classList.add('fa-eye');
            }
        }

        // Forgot password modal (placeholder)
        function showForgotPassword() {
            alert('Please contact your system administrator for password reset assistance.');
        }
    </script>
}
