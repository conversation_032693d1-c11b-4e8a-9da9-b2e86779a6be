﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Technoloway.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddAboutPages : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "AboutPages",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    Title = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    MetaDescription = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    MetaKeywords = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    HeroTitle = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    HeroSubtitle = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false),
                    HeroImageUrl = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    StoryTitle = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    StorySubtitle = table.Column<string>(type: "TEXT", maxLength: 300, nullable: false),
                    StoryContent = table.Column<string>(type: "TEXT", nullable: false),
                    StoryImageUrl = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    Stat1Number = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    Stat1Label = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    Stat2Number = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    Stat2Label = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    Stat3Number = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    Stat3Label = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    Stat4Number = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    Stat4Label = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    MissionTitle = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    MissionContent = table.Column<string>(type: "TEXT", nullable: true),
                    VisionTitle = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    VisionContent = table.Column<string>(type: "TEXT", nullable: true),
                    ValuesTitle = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    ValuesContent = table.Column<string>(type: "TEXT", nullable: true),
                    CtaTitle = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    CtaSubtitle = table.Column<string>(type: "TEXT", maxLength: 300, nullable: true),
                    CtaPrimaryButtonText = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    CtaPrimaryButtonUrl = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    CtaSecondaryButtonText = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    CtaSecondaryButtonUrl = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false),
                    LastModified = table.Column<DateTime>(type: "TEXT", nullable: false),
                    ModifiedBy = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    IsDeleted = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AboutPages", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "AboutPageSections",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    AboutPageId = table.Column<int>(type: "INTEGER", nullable: false),
                    Title = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    Content = table.Column<string>(type: "TEXT", nullable: false),
                    IconClass = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    ImageUrl = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    DisplayOrder = table.Column<int>(type: "INTEGER", nullable: false),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false),
                    SectionType = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    IsDeleted = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AboutPageSections", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AboutPageSections_AboutPages_AboutPageId",
                        column: x => x.AboutPageId,
                        principalTable: "AboutPages",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AboutPageSections_AboutPageId",
                table: "AboutPageSections",
                column: "AboutPageId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AboutPageSections");

            migrationBuilder.DropTable(
                name: "AboutPages");
        }
    }
}
