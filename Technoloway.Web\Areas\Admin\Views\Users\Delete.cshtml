@model Technoloway.Web.Areas.Admin.Models.UserViewModel

@{
    ViewData["Title"] = "Delete User";
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Delete User</h1>
    <a asp-action="Index" class="btn btn-secondary shadow-sm">
        <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Users
    </a>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card shadow border-danger">
            <div class="card-header bg-danger text-white py-3">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-exclamation-triangle me-2"></i>Confirm User Deletion
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Warning:</strong> You are about to permanently delete this user account. This action cannot be undone.
                </div>

                <h5>Are you sure you want to delete this user?</h5>
                
                <div class="table-responsive mt-4">
                    <table class="table table-bordered">
                        <tr>
                            <th width="30%">Email:</th>
                            <td><strong>@Model.Email</strong></td>
                        </tr>
                        <tr>
                            <th>Username:</th>
                            <td>@Model.UserName</td>
                        </tr>
                        <tr>
                            <th>Email Status:</th>
                            <td>
                                @if (Model.EmailConfirmed)
                                {
                                    <span class="badge bg-success">Confirmed</span>
                                }
                                else
                                {
                                    <span class="badge bg-warning text-dark">Unconfirmed</span>
                                }
                            </td>
                        </tr>
                        <tr>
                            <th>Account Status:</th>
                            <td>
                                @if (Model.IsLockedOut)
                                {
                                    <span class="badge bg-danger">Locked Out</span>
                                    <small class="text-muted d-block">Until: @Model.LockoutEnd?.ToString("MMM dd, yyyy 'at' h:mm tt")</small>
                                }
                                else
                                {
                                    <span class="badge bg-success">Active</span>
                                }
                            </td>
                        </tr>
                        <tr>
                            <th>Assigned Roles:</th>
                            <td>
                                @if (Model.Roles.Any())
                                {
                                    @foreach (var role in Model.Roles)
                                    {
                                        <span class="badge bg-primary me-1">@role</span>
                                    }
                                }
                                else
                                {
                                    <span class="text-muted">No roles assigned</span>
                                }
                            </td>
                        </tr>
                        <tr>
                            <th>Failed Login Attempts:</th>
                            <td>
                                @if (Model.AccessFailedCount > 0)
                                {
                                    <span class="badge bg-warning text-dark">@Model.AccessFailedCount</span>
                                }
                                else
                                {
                                    <span class="badge bg-success">0</span>
                                }
                            </td>
                        </tr>
                    </table>
                </div>

                <form asp-action="Delete" method="post" class="mt-4">
                    <input type="hidden" asp-for="Id" />
                    <div class="d-flex justify-content-end">
                        <a asp-action="Index" class="btn btn-secondary me-2">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                        <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-primary me-2">
                            <i class="fas fa-edit me-2"></i>Edit Instead
                        </a>
                        <button type="submit" class="btn btn-danger" onclick="return confirm('Are you absolutely sure you want to delete this user? This action cannot be undone!')">
                            <i class="fas fa-trash me-2"></i>Delete User
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row justify-content-center mt-4">
    <div class="col-md-8">
        <div class="card shadow border-warning">
            <div class="card-header bg-warning text-dark py-3">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-exclamation-triangle me-2"></i>Important Information
                </h6>
            </div>
            <div class="card-body">
                <ul class="mb-0">
                    <li><strong>Permanent Action:</strong> Deleting a user permanently removes their account from the system.</li>
                    <li><strong>Data Loss:</strong> All user data, preferences, and history will be lost.</li>
                    <li><strong>Related Data:</strong> Any content created by this user may become orphaned.</li>
                    <li><strong>Alternative:</strong> Consider locking the account instead of deleting it if you want to preserve data.</li>
                    <li><strong>Self-Protection:</strong> You cannot delete your own account through this interface.</li>
                </ul>
            </div>
        </div>
    </div>
</div>
