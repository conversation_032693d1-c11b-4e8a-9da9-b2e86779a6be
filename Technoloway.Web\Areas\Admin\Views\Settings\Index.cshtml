@model IEnumerable<IGrouping<string, Technoloway.Core.Entities.SiteSetting>>
@using System.IO

@{
    ViewData["Title"] = "Site Settings";
}

<div class="container-fluid p-4">
    <!-- Success/Error Messages -->
    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show mb-4" role="alert">
            <i class="fas fa-check-circle me-2"></i>@TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show mb-4" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>@TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800 fw-bold">Site Settings Management</h1>
            <p class="text-muted mb-0">Configure your website settings and preferences</p>
        </div>
        <div class="d-flex gap-2">
            <button class="btn-modern-admin secondary" onclick="resetAllChanges()">
                <i class="fas fa-undo"></i>
                Reset Changes
            </button>
            <a asp-action="Create" class="btn-modern-admin primary">
                <i class="fas fa-plus"></i>
                Add Setting
            </a>
        </div>
    </div>

    <!-- Settings Stats Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon me-3">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Total Settings</p>
                        <h3 class="admin-stat-number">@Model.SelectMany(g => g).Count()</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card success h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon success me-3">
                        <i class="fas fa-eye"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Public Settings</p>
                        <h3 class="admin-stat-number">@Model.SelectMany(g => g).Count(s => s.IsPublic)</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card warning h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon warning me-3">
                        <i class="fas fa-lock"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Private Settings</p>
                        <h3 class="admin-stat-number">@Model.SelectMany(g => g).Count(s => !s.IsPublic)</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card info h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon info me-3">
                        <i class="fas fa-layer-group"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Categories</p>
                        <h3 class="admin-stat-number">@Model.Count()</h3>
                    </div>
                </div>
            </div>
        </div>
    </div>

<form asp-action="UpdateMultiple" method="post">
    @foreach (var group in Model)
    {
        <div class="admin-card mb-4">
            <div class="card-header bg-white border-bottom d-flex align-items-center justify-content-between p-4">
                <div>
                    <h5 class="mb-0 fw-bold text-gray-800">
                        <i class="fas fa-cog me-2 text-primary"></i>@group.Key Settings
                    </h5>
                    <p class="text-muted mb-0 small">@group.Count() settings in this category</p>
                </div>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="toggleCategory('@group.Key')">
                        <i class="fas fa-eye me-1"></i>
                        Toggle
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="resetCategory('@group.Key')">
                        <i class="fas fa-undo me-1"></i>
                        Reset
                    </button>
                </div>
            </div>
            <div class="card-body p-0" id="<EMAIL>(" ", "")">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th class="border-0 fw-semibold">Setting</th>
                                <th class="border-0 fw-semibold">Value</th>
                                <th class="border-0 fw-semibold">Icon</th>
                                <th class="border-0 fw-semibold">Description</th>
                                <th class="border-0 fw-semibold">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var setting in group)
                            {
                                <tr class="setting-row" data-setting-id="@setting.Id">
                                    <td class="border-0">
                                        <div class="d-flex align-items-center">
                                            <div class="admin-stat-icon me-3" style="width: 35px; height: 35px; font-size: 0.9rem;">
                                                <i class="fas @(setting.IsPublic ? "fa-eye" : "fa-lock")"></i>
                                            </div>
                                            <div>
                                                <div class="fw-semibold text-gray-800">@setting.Key</div>
                                                <div class="text-muted small">
                                                    @if (!setting.IsPublic)
                                                    {
                                                        <span class="badge bg-warning text-dark">
                                                            <i class="fas fa-lock me-1"></i>Private
                                                        </span>
                                                    }
                                                    else
                                                    {
                                                        <span class="badge bg-success">
                                                            <i class="fas fa-eye me-1"></i>Public
                                                        </span>
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="border-0">
                                        @if (setting.Key.ToLower().Contains("password") || setting.Key.ToLower().Contains("secret") || setting.Key.ToLower().Contains("key"))
                                        {
                                            <div class="input-group">
                                                <input type="password"
                                                       name="settingValues[@setting.Id]"
                                                       value="@setting.Value"
                                                       class="form-control setting-input"
                                                       style="border-radius: var(--admin-radius); border: 2px solid var(--admin-border);"
                                                       placeholder="Enter @setting.Key"
                                                       data-original-value="@setting.Value" />
                                                <button type="button" class="btn btn-outline-secondary" onclick="togglePassword(this)">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                        }
                                        else if (setting.Value.Length > 50)
                                        {
                                            <textarea name="settingValues[@setting.Id]"
                                                      class="form-control setting-input"
                                                      rows="3"
                                                      style="border-radius: var(--admin-radius); border: 2px solid var(--admin-border);"
                                                      placeholder="Enter @setting.Key"
                                                      data-original-value="@setting.Value">@setting.Value</textarea>
                                        }
                                        else
                                        {
                                            <input type="text"
                                                   name="settingValues[@setting.Id]"
                                                   value="@setting.Value"
                                                   class="form-control setting-input"
                                                   style="border-radius: var(--admin-radius); border: 2px solid var(--admin-border);"
                                                   placeholder="Enter @setting.Key"
                                                   data-original-value="@setting.Value" />
                                        }
                                    </td>
                                    <td class="border-0">
                                        <div class="d-flex align-items-center">
                                            @if (!string.IsNullOrEmpty(setting.Icon) && setting.Icon.StartsWith("/images/"))
                                            {
                                                <img src="@setting.Icon" alt="Setting Icon" class="me-2 rounded"
                                                     style="height: 32px; width: 32px; object-fit: contain; border: 2px solid var(--admin-border);"
                                                     title="@setting.Icon" />
                                            }
                                            else
                                            {
                                                <div class="me-2 d-flex align-items-center justify-content-center rounded"
                                                     style="height: 32px; width: 32px; background: var(--admin-bg); border: 2px solid var(--admin-border);">
                                                    <i class="fas fa-image text-muted"></i>
                                                </div>
                                            }
                                            <input type="text"
                                                   name="settingIcons[@setting.Id]"
                                                   value="@setting.Icon"
                                                   class="form-control form-control-sm icon-input"
                                                   style="border-radius: var(--admin-radius); border: 2px solid var(--admin-border);"
                                                   placeholder="/images/settings/icon.jpg"
                                                   title="Icon field - enter image path"
                                                   data-original-value="@setting.Icon" />
                                        </div>
                                    </td>
                                    <td class="border-0">
                                        <div class="text-muted small">@setting.Description</div>
                                    </td>
                                    <td class="border-0">
                                        <div class="d-flex gap-1">
                                            <a asp-action="Edit" asp-route-id="@setting.Id"
                                               class="btn btn-outline-primary btn-sm" title="Edit Setting">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-secondary btn-sm"
                                                    onclick="resetSetting(@setting.Id)" title="Reset to Original">
                                                <i class="fas fa-undo"></i>
                                            </button>
                                            <a asp-action="Delete" asp-route-id="@setting.Id"
                                               class="btn btn-outline-danger btn-sm" title="Delete Setting"
                                               onclick="return confirm('Are you sure you want to delete this setting?')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    }

    <!-- Action Buttons -->
    <div class="d-flex justify-content-between align-items-center mt-4">
        <div>
            <span class="text-muted small" id="changes-indicator">
                <i class="fas fa-info-circle me-1"></i>
                Make changes above and click save to apply them.
            </span>
        </div>
        <div class="d-flex gap-2">
            <button type="button" class="btn btn-outline-secondary" onclick="resetAllChanges()">
                <i class="fas fa-undo me-2"></i>Reset All Changes
            </button>
            <a asp-action="Create" class="btn-modern-admin secondary">
                <i class="fas fa-plus me-2"></i>Add New Setting
            </a>
            <button type="submit" class="btn-modern-admin primary" id="save-button">
                <i class="fas fa-save me-2"></i>Save All Changes
            </button>
        </div>
    </div>
</form>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Track changes and highlight modified fields
            $('.setting-input, .icon-input').on('input change', function() {
                const originalValue = $(this).data('original-value') || '';
                const currentValue = $(this).val();

                if (originalValue !== currentValue) {
                    $(this).addClass('border-warning');
                    $(this).closest('tr').addClass('table-warning');
                } else {
                    $(this).removeClass('border-warning');
                    $(this).closest('tr').removeClass('table-warning');
                }

                updateChangesIndicator();
            });

            // Enhanced icon preview functionality
            $('.icon-input').on('input', function() {
                const iconValue = $(this).val().trim();
                const container = $(this).closest('.d-flex');
                const existingIcon = container.find('img').first();
                const placeholder = container.find('.d-flex').first();

                if (iconValue && iconValue.startsWith('/images/')) {
                    // Image path - show image
                    if (existingIcon.length > 0) {
                        existingIcon.attr('src', iconValue).attr('title', iconValue);
                        placeholder.hide();
                        existingIcon.show();
                    } else {
                        placeholder.hide();
                        container.prepend(`<img src="${iconValue}" alt="Setting Icon" class="me-2 rounded"
                                         style="height: 32px; width: 32px; object-fit: contain; border: 2px solid var(--admin-border);"
                                         title="${iconValue}" />`);
                    }
                } else {
                    // Empty value or invalid path - show placeholder
                    existingIcon.hide();
                    placeholder.show();
                }
            });
        });

        // Add hover effects to stat cards
        $('.admin-stat-card').hover(
            function() {
                $(this).addClass('shadow-lg');
            },
            function() {
                $(this).removeClass('shadow-lg');
            }
        );

        // Function to toggle password visibility
        function togglePassword(button) {
            const input = $(button).siblings('input');
            const icon = $(button).find('i');

            if (input.attr('type') === 'password') {
                input.attr('type', 'text');
                icon.removeClass('fa-eye').addClass('fa-eye-slash');
            } else {
                input.attr('type', 'password');
                icon.removeClass('fa-eye-slash').addClass('fa-eye');
            }
        }

        // Function to reset individual setting
        function resetSetting(settingId) {
            const row = $(`.setting-row[data-setting-id="${settingId}"]`);
            const inputs = row.find('.setting-input, .icon-input');

            inputs.each(function() {
                const originalValue = $(this).data('original-value') || '';
                $(this).val(originalValue);
                $(this).removeClass('border-warning');
                $(this).trigger('input'); // Trigger input event to update icon preview
            });

            row.removeClass('table-warning');
            updateChangesIndicator();
        }

        // Function to reset category
        function resetCategory(categoryKey) {
            const categoryId = categoryKey.replace(/\s/g, '');
            const category = $(`#category-${categoryId}`);
            const inputs = category.find('.setting-input, .icon-input');

            inputs.each(function() {
                const originalValue = $(this).data('original-value') || '';
                $(this).val(originalValue);
                $(this).removeClass('border-warning');
                $(this).trigger('input'); // Trigger input event to update icon preview
            });

            category.find('.setting-row').removeClass('table-warning');
            updateChangesIndicator();
        }

        // Function to reset all changes
        function resetAllChanges() {
            if (confirm('Are you sure you want to reset all changes?')) {
                $('.setting-input, .icon-input').each(function() {
                    const originalValue = $(this).data('original-value') || '';
                    $(this).val(originalValue);
                    $(this).removeClass('border-warning');
                    $(this).trigger('input'); // Trigger input event to update icon preview
                });

                $('.setting-row').removeClass('table-warning');
                updateChangesIndicator();
            }
        }

        // Function to toggle category visibility
        function toggleCategory(categoryKey) {
            const categoryId = categoryKey.replace(/\s/g, '');
            const category = $(`#category-${categoryId}`);
            category.toggle();
        }

        // Function to update changes indicator
        function updateChangesIndicator() {
            const changedFields = $('.border-warning').length;
            const indicator = $('#changes-indicator');

            if (changedFields > 0) {
                indicator.html(`<i class="fas fa-exclamation-triangle me-1 text-warning"></i>
                               ${changedFields} setting(s) modified. Click save to apply changes.`);
                indicator.removeClass('text-muted').addClass('text-warning');
                $('#save-button').addClass('btn-warning').removeClass('btn-primary');
            } else {
                indicator.html(`<i class="fas fa-info-circle me-1"></i>
                               Make changes above and click save to apply them.`);
                indicator.removeClass('text-warning').addClass('text-muted');
                $('#save-button').removeClass('btn-warning').addClass('btn-primary');
            }
        }

        // Add settings statistics calculations
        function updateSettingsStats() {
            const totalSettings = @Model.SelectMany(g => g).Count();
            const publicSettings = @Model.SelectMany(g => g).Count(s => s.IsPublic);
            const privateSettings = @Model.SelectMany(g => g).Count(s => !s.IsPublic);
            const categories = @Model.Count();

            if (totalSettings > 0) {
                const publicPercentage = Math.round((publicSettings / totalSettings) * 100);
                const privatePercentage = Math.round((privateSettings / totalSettings) * 100);
                console.log(`Public settings: ${publicPercentage}%`);
                console.log(`Private settings: ${privatePercentage}%`);
                console.log(`Categories: ${categories}`);
            }
        }

        updateSettingsStats();

        // Add setting icon hover effects
        $('.admin-stat-icon').hover(
            function() {
                $(this).css('transform', 'rotate(5deg) scale(1.1)');
                $(this).css('transition', 'transform 0.3s ease');
            },
            function() {
                $(this).css('transform', 'rotate(0deg) scale(1)');
            }
        );

        // Add badge hover effects
        $('.badge').hover(
            function() {
                $(this).css('transform', 'scale(1.05)');
                $(this).css('transition', 'transform 0.2s ease');
            },
            function() {
                $(this).css('transform', 'scale(1)');
            }
        );

        // Enhanced confirmation for delete
        $('a[asp-action="Delete"]').click(function(e) {
            const settingKey = $(this).closest('tr').find('.fw-semibold').text();
            if (!confirm(`Are you sure you want to delete the setting "${settingKey}"?`)) {
                e.preventDefault();
                return false;
            }
        });
    </script>
}
