using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Technoloway.Core.Entities;
using Technoloway.Core.Interfaces;
using Technoloway.Web.Areas.Admin.Models;
using Technoloway.Web.Services;

namespace Technoloway.Web.Areas.Admin.Controllers;

[Area("Admin")]
[Authorize(Policy = "RequireAdminRole")]
[Route("Admin/[controller]/[action]")]
public class TestimonialsController : Controller
{
    private readonly IRepository<Testimonial> _testimonialRepository;
    private readonly IFileUploadService _fileUploadService;

    public TestimonialsController(IRepository<Testimonial> testimonialRepository, IFileUploadService fileUploadService)
    {
        _testimonialRepository = testimonialRepository;
        _fileUploadService = fileUploadService;
    }

    public async Task<IActionResult> Index()
    {
        var testimonials = await _testimonialRepository.GetAll()
            .Where(t => !t.IsDeleted)
            .OrderBy(t => t.DisplayOrder)
            .ThenByDescending(t => t.CreatedAt)
            .ToListAsync();

        return View(testimonials);
    }

    public IActionResult Create()
    {
        var viewModel = new TestimonialViewModel
        {
            IsActive = true,
            Rating = 5,
            DisplayOrder = 0
        };
        return View(viewModel);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(TestimonialViewModel viewModel)
    {
        try
        {
            Console.WriteLine($"Create POST called: ClientName={viewModel.ClientName}, Rating={viewModel.Rating}");

            // Handle file upload if provided
            if (viewModel.PhotoFile != null && viewModel.PhotoFile.Length > 0)
            {
                if (!_fileUploadService.IsValidImageFile(viewModel.PhotoFile))
                {
                    ModelState.AddModelError("PhotoFile", "Please upload a valid image file (JPG, PNG, GIF, WebP). Maximum size: 5MB.");
                }
                else
                {
                    viewModel.ClientPhotoUrl = await _fileUploadService.UploadImageAsync(viewModel.PhotoFile, "testimonials");
                }
            }

            if (ModelState.IsValid)
            {
                var testimonial = viewModel.ToEntity();

                // Set timestamps
                testimonial.CreatedAt = DateTime.UtcNow;
                testimonial.UpdatedAt = DateTime.UtcNow;

                // Set display order if not provided
                if (testimonial.DisplayOrder == 0)
                {
                    var maxOrder = await _testimonialRepository.GetAll()
                        .Where(t => !t.IsDeleted)
                        .MaxAsync(t => (int?)t.DisplayOrder) ?? 0;
                    testimonial.DisplayOrder = maxOrder + 1;
                }

                Console.WriteLine($"Testimonial details before saving: ClientName={testimonial.ClientName}, Company={testimonial.ClientCompany}, Rating={testimonial.Rating}");

                var result = await _testimonialRepository.AddAsync(testimonial);
                Console.WriteLine($"Testimonial saved successfully with ID: {result.Id}");

                TempData["SuccessMessage"] = "Testimonial created successfully!";
                return RedirectToAction(nameof(Index));
            }
            else
            {
                Console.WriteLine("ModelState is invalid:");
                foreach (var error in ModelState.Values.SelectMany(v => v.Errors))
                {
                    Console.WriteLine($"Validation error: {error.ErrorMessage}");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in Create method: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
            ModelState.AddModelError("", $"Error creating testimonial: {ex.Message}");
        }

        return View(viewModel);
    }

    public async Task<IActionResult> Edit(int id)
    {
        var testimonial = await _testimonialRepository.GetByIdAsync(id);
        if (testimonial == null || testimonial.IsDeleted)
        {
            return NotFound();
        }

        var viewModel = TestimonialViewModel.FromEntity(testimonial);
        return View(viewModel);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Edit(int id, TestimonialViewModel viewModel)
    {
        if (id != viewModel.Id)
        {
            return NotFound();
        }

        try
        {
            Console.WriteLine($"Edit POST called: ID={id}, ClientName={viewModel.ClientName}");

            var existingTestimonial = await _testimonialRepository.GetByIdAsync(id);
            if (existingTestimonial == null || existingTestimonial.IsDeleted)
            {
                return NotFound();
            }

            // Handle file upload if provided
            if (viewModel.PhotoFile != null && viewModel.PhotoFile.Length > 0)
            {
                if (!_fileUploadService.IsValidImageFile(viewModel.PhotoFile))
                {
                    ModelState.AddModelError("PhotoFile", "Please upload a valid image file (JPG, PNG, GIF, WebP). Maximum size: 5MB.");
                }
                else
                {
                    // Get the old file name for deletion
                    string? oldFileName = null;
                    if (!string.IsNullOrEmpty(existingTestimonial.ClientPhotoUrl) && existingTestimonial.ClientPhotoUrl.StartsWith("/images/"))
                    {
                        oldFileName = Path.GetFileName(existingTestimonial.ClientPhotoUrl);
                    }

                    viewModel.ClientPhotoUrl = await _fileUploadService.UploadImageAsync(viewModel.PhotoFile, "testimonials", oldFileName);
                }
            }
            else
            {
                // Keep existing photo if no new file uploaded
                viewModel.ClientPhotoUrl = existingTestimonial.ClientPhotoUrl;
            }

            if (ModelState.IsValid)
            {
                // Update properties
                viewModel.UpdateEntity(existingTestimonial);
                existingTestimonial.UpdatedAt = DateTime.UtcNow;

                await _testimonialRepository.UpdateAsync(existingTestimonial);
                Console.WriteLine($"Testimonial updated successfully: ID={id}");

                TempData["SuccessMessage"] = "Testimonial updated successfully!";
                return RedirectToAction(nameof(Index));
            }
            else
            {
                Console.WriteLine("ModelState is invalid:");
                foreach (var error in ModelState.Values.SelectMany(v => v.Errors))
                {
                    Console.WriteLine($"Validation error: {error.ErrorMessage}");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in Edit method: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
            ModelState.AddModelError("", $"Error updating testimonial: {ex.Message}");
        }

        return View(viewModel);
    }

    public async Task<IActionResult> Details(int id)
    {
        var testimonial = await _testimonialRepository.GetByIdAsync(id);
        if (testimonial == null || testimonial.IsDeleted)
        {
            return NotFound();
        }

        return View(testimonial);
    }

    public async Task<IActionResult> Delete(int id)
    {
        var testimonial = await _testimonialRepository.GetByIdAsync(id);
        if (testimonial == null || testimonial.IsDeleted)
        {
            return NotFound();
        }

        return View(testimonial);
    }

    [HttpPost, ActionName("Delete")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DeleteConfirmed(int id)
    {
        try
        {
            var testimonial = await _testimonialRepository.GetByIdAsync(id);
            if (testimonial == null || testimonial.IsDeleted)
            {
                return NotFound();
            }

            await _testimonialRepository.DeleteAsync(testimonial);
            Console.WriteLine($"Testimonial soft deleted: ID={id}");

            TempData["SuccessMessage"] = "Testimonial deleted successfully!";
            return RedirectToAction(nameof(Index));
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error deleting testimonial: {ex.Message}");
            TempData["ErrorMessage"] = "Error deleting testimonial.";
            return RedirectToAction(nameof(Index));
        }
    }

    [HttpPost]
    public async Task<IActionResult> ToggleActive(int id)
    {
        try
        {
            var testimonial = await _testimonialRepository.GetByIdAsync(id);
            if (testimonial == null || testimonial.IsDeleted)
            {
                return Json(new { success = false, message = "Testimonial not found" });
            }

            testimonial.IsActive = !testimonial.IsActive;
            testimonial.UpdatedAt = DateTime.UtcNow;
            await _testimonialRepository.UpdateAsync(testimonial);

            return Json(new { success = true, isActive = testimonial.IsActive });
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error toggling testimonial status: {ex.Message}");
            return Json(new { success = false, message = "Error updating testimonial status" });
        }
    }
}
