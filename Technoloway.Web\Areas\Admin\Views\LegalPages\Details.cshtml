@model Technoloway.Core.Entities.LegalPage

@{
    ViewData["Title"] = "Legal Page Details";
    Layout = "~/Areas/Admin/Views/Shared/_AdminLayout.cshtml";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- <PERSON> Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0 text-gray-800">@Model.Title</h1>
                    <p class="text-muted">Legal page details and content preview</p>
                </div>
                <div>
                    <a href="@Url.Action("Edit", new { id = Model.Id })" class="btn btn-primary me-2">
                        <i class="fas fa-edit me-2"></i>Edit Page
                    </a>
                    <a href="@Url.Action("Index")" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to List
                    </a>
                </div>
            </div>

            <!-- Page Information Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>Page Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Title:</label>
                                <p class="mb-0">@Model.Title</p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">URL Slug:</label>
                                <p class="mb-0">
                                    <code>/@Model.Slug</code>
                                    <a href="/@Model.Slug" target="_blank" class="btn btn-sm btn-outline-primary ms-2">
                                        <i class="fas fa-external-link-alt me-1"></i>View Live
                                    </a>
                                </p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">Status:</label>
                                <p class="mb-0">
                                    <span class="badge @(Model.IsActive ? "bg-success" : "bg-secondary")">
                                        @(Model.IsActive ? "Active" : "Inactive")
                                    </span>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label fw-bold">Meta Description:</label>
                                <p class="mb-0">@(Model.MetaDescription ?? "No meta description set")</p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">Last Modified:</label>
                                <p class="mb-0">
                                    @Model.LastModified.ToString("MMMM dd, yyyy 'at' hh:mm tt")
                                    <br><small class="text-muted">by @(Model.ModifiedBy ?? "System")</small>
                                </p>
                            </div>
                            <div class="mb-3">
                                <label class="form-label fw-bold">Sections:</label>
                                <p class="mb-0">
                                    <span class="badge bg-info">@Model.Sections.Count sections</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-file-alt me-2"></i>Main Content
                    </h6>
                </div>
                <div class="card-body">
                    <div class="content-preview">
                        @Html.Raw(Model.Content)
                    </div>
                </div>
            </div>

            <!-- Sections Card -->
            @if (Model.Sections.Any())
            {
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-list me-2"></i>Sections (@Model.Sections.Count)
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            @foreach (var section in Model.Sections.OrderBy(s => s.DisplayOrder))
                            {
                                <div class="col-md-6 mb-4">
                                    <div class="section-card border rounded p-3 h-100">
                                        <div class="d-flex align-items-center mb-3">
                                            @if (!string.IsNullOrEmpty(section.IconClass))
                                            {
                                                <div class="section-icon me-3">
                                                    <i class="@(section.IconClass) fa-2x text-primary"></i>
                                                </div>
                                            }
                                            <div>
                                                <h5 class="mb-1">@(section.Title)</h5>
                                                <small class="text-muted">
                                                    Order: @(section.DisplayOrder)
                                                    <span class="badge @(section.IsActive ? "bg-success" : "bg-secondary") ms-2">
                                                        @(section.IsActive ? "Active" : "Inactive")
                                                    </span>
                                                </small>
                                            </div>
                                        </div>
                                        <div class="section-content">
                                            @Html.Raw(section.Content)
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            }
            else
            {
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-list me-2"></i>Sections
                        </h6>
                    </div>
                    <div class="card-body text-center py-5">
                        <i class="fas fa-list fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Sections</h5>
                        <p class="text-muted">This legal page doesn't have any sections yet.</p>
                        <a href="@Url.Action("Edit", new { id = Model.Id })" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Add Sections
                        </a>
                    </div>
                </div>
            }

            <!-- Preview Card -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-eye me-2"></i>Live Preview
                    </h6>
                </div>
                <div class="card-body">
                    <div class="preview-container border rounded p-4" style="background-color: #f8f9fc;">
                        <div class="preview-header mb-4">
                            <h1 class="h2 mb-2">@Model.Title</h1>
                            @if (!string.IsNullOrEmpty(Model.MetaDescription))
                            {
                                <p class="lead text-muted">@Model.MetaDescription</p>
                            }
                        </div>

                        <div class="preview-content mb-4">
                            @Html.Raw(Model.Content)
                        </div>

                        @if (Model.Sections.Any())
                        {
                            <div class="preview-sections">
                                @foreach (var section in Model.Sections.Where(s => s.IsActive).OrderBy(s => s.DisplayOrder))
                                {
                                    <div class="section-preview mb-4 p-3 border rounded">
                                        <div class="d-flex align-items-center mb-3">
                                            @if (!string.IsNullOrEmpty(section.IconClass))
                                            {
                                                <i class="@(section.IconClass) me-3 text-primary"></i>
                                            }
                                            <h3 class="h4 mb-0">@(section.Title)</h3>
                                        </div>
                                        <div>
                                            @Html.Raw(section.Content)
                                        </div>
                                    </div>
                                }
                            </div>
                        }
                    </div>
                    <div class="text-center mt-3">
                        <a href="/@Model.Slug" target="_blank" class="btn btn-outline-primary">
                            <i class="fas fa-external-link-alt me-2"></i>View Full Page
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <style>
        .content-preview {
            background-color: #f8f9fc;
            border-radius: 8px;
            padding: 1.5rem;
        }

        .section-card {
            background-color: #f8f9fc;
            transition: all 0.3s ease;
        }

        .section-card:hover {
            background-color: #eaecf4;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .section-icon {
            width: 60px;
            text-align: center;
        }

        .preview-container {
            max-height: 600px;
            overflow-y: auto;
        }

        .section-preview {
            background-color: white;
        }

        code {
            background-color: #e9ecef;
            color: #495057;
            padding: 2px 6px;
            border-radius: 3px;
        }
    </style>
}
