using Technoloway.Core.Entities;

namespace Technoloway.Infrastructure.Data
{
    public static class LegalPageSeeder
    {
        public static async Task SeedAsync(ApplicationDbContext context)
        {
            // Check if legal pages already exist
            if (context.LegalPages.Any())
                return;

            var legalPages = new List<LegalPage>
            {
                new LegalPage
                {
                    Title = "Terms of Service",
                    Slug = "terms",
                    MetaDescription = "Terms of Service for Technoloway - Professional web development and technology solutions",
                    Content = @"<p>Welcome to Technoloway. These terms and conditions outline the rules and regulations for the use of our services.</p>
                    <p>By accessing and using our services, you accept these terms and conditions in full. If you disagree with these terms and conditions or any part of these terms and conditions, you must not use our services.</p>",
                    IsActive = true,
                    LastModified = DateTime.UtcNow,
                    ModifiedBy = "System",
                    CreatedAt = DateTime.UtcNow,
                    Sections = new List<LegalPageSection>
                    {
                        new LegalPageSection
                        {
                            Title = "Acceptance of Terms",
                            Content = @"<p>By accessing and using Technoloway's services, you acknowledge that you have read, understood, and agree to be bound by these Terms of Service. These terms constitute a legally binding agreement between you and Technoloway.</p>
                            <p>If you do not agree to these terms, please do not use our services. We reserve the right to modify these terms at any time, and your continued use of our services constitutes acceptance of any changes.</p>",
                            IconClass = "fas fa-handshake",
                            DisplayOrder = 1,
                            IsActive = true,
                            CreatedAt = DateTime.UtcNow
                        },
                        new LegalPageSection
                        {
                            Title = "Services Description",
                            Content = @"<p>Technoloway provides professional web development, software development, and technology consulting services. Our services include but are not limited to:</p>
                            <ul>
                                <li>Custom web application development</li>
                                <li>Mobile application development</li>
                                <li>E-commerce solutions</li>
                                <li>Technology consulting</li>
                                <li>Digital transformation services</li>
                                <li>Maintenance and support services</li>
                            </ul>
                            <p>All services are provided according to the specifications agreed upon in individual project contracts.</p>",
                            IconClass = "fas fa-cogs",
                            DisplayOrder = 2,
                            IsActive = true,
                            CreatedAt = DateTime.UtcNow
                        },
                        new LegalPageSection
                        {
                            Title = "User Responsibilities",
                            Content = @"<p>As a user of our services, you agree to:</p>
                            <ul>
                                <li>Provide accurate and complete information when required</li>
                                <li>Maintain the confidentiality of your account credentials</li>
                                <li>Use our services in compliance with all applicable laws and regulations</li>
                                <li>Not engage in any activity that could harm our systems or other users</li>
                                <li>Respect intellectual property rights</li>
                                <li>Provide timely feedback and approvals as required for project completion</li>
                            </ul>",
                            IconClass = "fas fa-user-check",
                            DisplayOrder = 3,
                            IsActive = true,
                            CreatedAt = DateTime.UtcNow
                        }
                    }
                },
                new LegalPage
                {
                    Title = "Privacy Policy",
                    Slug = "privacy",
                    MetaDescription = "Privacy Policy for Technoloway - How we collect, use, and protect your personal information",
                    Content = @"<p>At Technoloway, we are committed to protecting your privacy and ensuring the security of your personal information. This Privacy Policy explains how we collect, use, and safeguard your data.</p>
                    <p>We believe in transparency and want you to understand exactly how your information is handled when you use our services.</p>",
                    IsActive = true,
                    LastModified = DateTime.UtcNow,
                    ModifiedBy = "System",
                    CreatedAt = DateTime.UtcNow,
                    Sections = new List<LegalPageSection>
                    {
                        new LegalPageSection
                        {
                            Title = "Information We Collect",
                            Content = @"<p>We collect information that you provide directly to us, such as:</p>
                            <ul>
                                <li>Contact information (name, email address, phone number)</li>
                                <li>Company information and project requirements</li>
                                <li>Communication preferences</li>
                                <li>Feedback and testimonials</li>
                            </ul>
                            <p>We also automatically collect certain information about your device and how you interact with our services, including IP address, browser type, and usage patterns.</p>",
                            IconClass = "fas fa-database",
                            DisplayOrder = 1,
                            IsActive = true,
                            CreatedAt = DateTime.UtcNow
                        },
                        new LegalPageSection
                        {
                            Title = "How We Use Your Information",
                            Content = @"<p>We use the information we collect to:</p>
                            <ul>
                                <li>Provide and improve our services</li>
                                <li>Communicate with you about projects and services</li>
                                <li>Send important updates and notifications</li>
                                <li>Analyze usage patterns to enhance user experience</li>
                                <li>Comply with legal obligations</li>
                                <li>Protect against fraud and security threats</li>
                            </ul>
                            <p>We will never sell your personal information to third parties.</p>",
                            IconClass = "fas fa-shield-alt",
                            DisplayOrder = 2,
                            IsActive = true,
                            CreatedAt = DateTime.UtcNow
                        },
                        new LegalPageSection
                        {
                            Title = "Data Security",
                            Content = @"<p>We implement appropriate technical and organizational measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.</p>
                            <p>Our security measures include:</p>
                            <ul>
                                <li>Encryption of data in transit and at rest</li>
                                <li>Regular security assessments and updates</li>
                                <li>Access controls and authentication mechanisms</li>
                                <li>Employee training on data protection</li>
                                <li>Incident response procedures</li>
                            </ul>",
                            IconClass = "fas fa-lock",
                            DisplayOrder = 3,
                            IsActive = true,
                            CreatedAt = DateTime.UtcNow
                        },
                        new LegalPageSection
                        {
                            Title = "Your Rights",
                            Content = @"<p>Under applicable data protection laws, you have the right to:</p>
                            <ul>
                                <li>Access your personal information</li>
                                <li>Correct inaccurate or incomplete data</li>
                                <li>Request deletion of your personal information</li>
                                <li>Object to processing of your personal information</li>
                                <li>Request data portability</li>
                                <li>Withdraw consent where processing is based on consent</li>
                            </ul>
                            <p>To exercise these rights, please contact <NAME_EMAIL>.</p>",
                            IconClass = "fas fa-user-shield",
                            DisplayOrder = 4,
                            IsActive = true,
                            CreatedAt = DateTime.UtcNow
                        }
                    }
                }
            };

            context.LegalPages.AddRange(legalPages);
            await context.SaveChangesAsync();
        }
    }
}
