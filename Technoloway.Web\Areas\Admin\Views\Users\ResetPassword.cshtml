@model Technoloway.Web.Areas.Admin.Models.ResetPasswordViewModel

@{
    ViewData["Title"] = "Reset Password";
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Reset User Password</h1>
    <a asp-action="Index" class="btn btn-secondary shadow-sm">
        <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Users
    </a>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-key me-2"></i>Reset Password for: @Model.Email
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Note:</strong> This will reset the user's password. They will need to use the new password to log in.
                </div>

                <form asp-action="ResetPassword" method="post">
                    <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                    <input type="hidden" asp-for="UserId" />
                    <input type="hidden" asp-for="Email" />

                    <div class="form-group mb-3">
                        <label class="form-label">User Email</label>
                        <input type="text" class="form-control" value="@Model.Email" readonly />
                        <small class="form-text text-muted">The password will be reset for this user account.</small>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="NewPassword" class="form-label">New Password *</label>
                        <input asp-for="NewPassword" class="form-control" />
                        <span asp-validation-for="NewPassword" class="text-danger"></span>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="ConfirmPassword" class="form-label">Confirm New Password *</label>
                        <input asp-for="ConfirmPassword" class="form-control" />
                        <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-key me-2"></i>Reset Password
                        </button>
                        <a asp-action="Index" class="btn btn-secondary ms-2">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                        <a asp-action="Edit" asp-route-id="@Model.UserId" class="btn btn-primary ms-2">
                            <i class="fas fa-edit me-2"></i>Edit User
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-info">
                    <i class="fas fa-lightbulb me-2"></i>Password Requirements
                </h6>
            </div>
            <div class="card-body">
                <ul class="small">
                    <li>At least 6 characters long</li>
                    <li>Maximum 100 characters</li>
                    <li>Must contain at least one uppercase letter</li>
                    <li>Must contain at least one lowercase letter</li>
                    <li>Must contain at least one digit</li>
                    <li>Must contain at least one special character</li>
                </ul>
            </div>
        </div>

        <div class="card shadow mt-3">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-warning">
                    <i class="fas fa-shield-alt me-2"></i>Security Notes
                </h6>
            </div>
            <div class="card-body">
                <ul class="small mb-0">
                    <li><strong>Immediate Effect:</strong> The password change takes effect immediately.</li>
                    <li><strong>User Notification:</strong> Consider informing the user about the password reset.</li>
                    <li><strong>Secure Delivery:</strong> Share the new password through a secure channel.</li>
                    <li><strong>Force Change:</strong> Consider requiring the user to change the password on next login.</li>
                </ul>
            </div>
        </div>

        <div class="card shadow mt-3">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-success">
                    <i class="fas fa-tips me-2"></i>Best Practices
                </h6>
            </div>
            <div class="card-body">
                <ul class="small mb-0">
                    <li>Use a strong, unique password</li>
                    <li>Include a mix of characters</li>
                    <li>Avoid common words or patterns</li>
                    <li>Consider using a password generator</li>
                    <li>Document the password reset in logs</li>
                </ul>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        // Password strength indicator (optional enhancement)
        document.getElementById('NewPassword').addEventListener('input', function() {
            // Add password strength validation if needed
        });
    </script>
}
