﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Technoloway.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class CombineSlideFields : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Description",
                table: "HeroSlides");

            migrationBuilder.DropColumn(
                name: "Subtitle",
                table: "HeroSlides");

            migrationBuilder.DropColumn(
                name: "Title",
                table: "HeroSlides");

            migrationBuilder.AddColumn<string>(
                name: "Content",
                table: "HeroSlides",
                type: "TEXT",
                nullable: false,
                defaultValue: "");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Content",
                table: "HeroSlides");

            migrationBuilder.AddColumn<string>(
                name: "Description",
                table: "HeroSlides",
                type: "TEXT",
                maxLength: 1000,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Subtitle",
                table: "HeroSlides",
                type: "TEXT",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Title",
                table: "HeroSlides",
                type: "TEXT",
                maxLength: 200,
                nullable: false,
                defaultValue: "");
        }
    }
}
