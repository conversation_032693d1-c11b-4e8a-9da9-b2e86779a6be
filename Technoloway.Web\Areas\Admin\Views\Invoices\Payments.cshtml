@model IEnumerable<Technoloway.Core.Entities.Payment>

@{
    ViewData["Title"] = "Invoice Payments";
    Layout = "_AdminLayout";
    var invoice = ViewBag.Invoice as Technoloway.Core.Entities.Invoice;
    var totalPaid = ViewBag.TotalPaid as decimal? ?? 0;
    var balance = ViewBag.Balance as decimal? ?? 0;
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Payments for Invoice #@(invoice != null ? invoice.InvoiceNumber : "")</h1>
    <div>
        <a asp-action="AddPayment" asp-route-id="@(invoice != null ? invoice.Id : 0)" class="btn btn-sm btn-warning shadow-sm">
            <i class="fas fa-plus fa-sm text-white-50"></i> Add Payment
        </a>
        <a asp-action="Details" asp-route-id="@(invoice != null ? invoice.Id : 0)" class="btn btn-sm btn-info shadow-sm">
            <i class="fas fa-eye fa-sm text-white-50"></i> View Invoice
        </a>
        <a asp-action="Index" class="btn btn-sm btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Invoices
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Payment History</h6>
            </div>
            <div class="card-body">
                @if (!Model.Any())
                {
                    <div class="alert alert-info">
                        <p class="mb-0">No payments have been recorded for this invoice yet.</p>
                    </div>

                    <div class="text-center mt-3">
                        <a asp-action="AddPayment" asp-route-id="@(invoice != null ? invoice.Id : 0)" class="btn btn-warning">
                            <i class="fas fa-plus"></i> Record First Payment
                        </a>
                    </div>
                }
                else
                {
                    <div class="table-responsive">
                        <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Transaction ID</th>
                                    <th>Date</th>
                                    <th>Amount</th>
                                    <th>Method</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var payment in Model)
                                {
                                    <tr>
                                        <td>@payment.TransactionId</td>
                                        <td>@payment.PaymentDate.ToString("yyyy-MM-dd")</td>
                                        <td>@payment.Amount.ToString("C")</td>
                                        <td>@payment.PaymentMethod</td>
                                        <td>
                                            @switch (payment.Status)
                                            {
                                                case "Completed":
                                                    <span class="badge bg-success">Completed</span>
                                                    break;
                                                case "Pending":
                                                    <span class="badge bg-warning">Pending</span>
                                                    break;
                                                case "Failed":
                                                    <span class="badge bg-danger">Failed</span>
                                                    break;
                                                case "Refunded":
                                                    <span class="badge bg-info">Refunded</span>
                                                    break;
                                                default:
                                                    <span class="badge bg-secondary">@payment.Status</span>
                                                    break;
                                            }
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a asp-area="Admin" asp-controller="Invoices" asp-action="EditPayment" asp-route-id="@payment.Id" class="btn btn-primary btn-sm" title="Edit Payment">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button type="button" class="btn btn-danger btn-sm" data-bs-toggle="modal" data-bs-target="#<EMAIL>" title="Delete Payment">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>

                                            <!-- Delete Modal -->
                                            <div class="modal fade" id="<EMAIL>" tabindex="-1" role="dialog" aria-labelledby="<EMAIL>" aria-hidden="true">
                                                <div class="modal-dialog" role="document">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title" id="<EMAIL>">Confirm Delete</h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                        </div>
                                                        <div class="modal-body">
                                                            <p>Are you sure you want to delete this payment?</p>
                                                            <p><strong>Transaction ID:</strong> @payment.TransactionId</p>
                                                            <p><strong>Amount:</strong> @payment.Amount.ToString("C")</p>
                                                            <p><strong>Date:</strong> @payment.PaymentDate.ToString("yyyy-MM-dd")</p>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                            <form asp-action="DeletePayment" asp-route-id="@payment.Id" method="post" style="display: inline;">
                                                                @Html.AntiForgeryToken()
                                                                <button type="submit" class="btn btn-danger">Delete</button>
                                                            </form>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Payment Summary</h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    <h4>Invoice #@(invoice != null ? invoice.InvoiceNumber : "")</h4>
                    <p>
                        @switch (invoice?.Status)
                        {
                            case "Pending":
                                <span class="badge bg-warning">Pending</span>
                                break;
                            case "Paid":
                                <span class="badge bg-success">Paid</span>
                                break;
                            case "Overdue":
                                <span class="badge bg-danger">Overdue</span>
                                break;
                            case "Cancelled":
                                <span class="badge bg-secondary">Cancelled</span>
                                break;
                            default:
                                <span class="badge bg-info">@invoice?.Status</span>
                                break;
                        }
                    </p>
                </div>

                <div class="row text-center">
                    <div class="col-md-6 mb-3">
                        <h6 class="text-muted">Total Amount</h6>
                        <h4>@(invoice != null ? invoice.TotalAmount.ToString("C") : "$0.00")</h4>
                    </div>
                    <div class="col-md-6 mb-3">
                        <h6 class="text-muted">Due Date</h6>
                        <h4>@(invoice != null ? invoice.DueDate.ToString("yyyy-MM-dd") : "-")</h4>
                    </div>
                </div>

                <hr />

                <div class="row text-center">
                    <div class="col-md-6 mb-3">
                        <h6 class="text-muted">Total Paid</h6>
                        <h4 class="text-success">@totalPaid.ToString("C")</h4>
                    </div>
                    <div class="col-md-6 mb-3">
                        <h6 class="text-muted">Balance</h6>
                        <h4 class="@(balance <= 0 ? "text-success" : "text-danger")">@balance.ToString("C")</h4>
                    </div>
                </div>

                <div class="text-center mt-4">
                    @if (balance > 0)
                    {
                        <a asp-action="AddPayment" asp-route-id="@(invoice != null ? invoice.Id : 0)" class="btn btn-warning btn-block">
                            <i class="fas fa-plus"></i> Add Payment
                        </a>
                    }
                    else
                    {
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i> This invoice is fully paid.
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#dataTable').DataTable({
                "order": [[1, "desc"]] // Sort by date (column 1) in descending order
            });
        });
    </script>
}
