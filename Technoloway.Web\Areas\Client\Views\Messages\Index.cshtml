@model IEnumerable<Technoloway.Core.Entities.Message>
@{
    ViewData["Title"] = "Messages";
    var projects = ViewBag.Projects as List<Technoloway.Core.Entities.Project>;
    var selectedProjectId = ViewBag.SelectedProjectId as int?;
    var unreadCount = ViewBag.UnreadCount as int? ?? 0;
    var totalMessages = ViewBag.TotalMessages as int? ?? 0;
}

<div class="container-fluid p-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="text-gradient mb-2">
                        <i class="fas fa-comments me-2"></i>Messages
                    </h1>
                    <p class="text-muted">Communicate with your project team</p>
                </div>
                <div class="d-flex align-items-center gap-3">
                    @if (unreadCount > 0)
                    {
                        <span class="badge bg-danger fs-6">@unreadCount Unread</span>
                    }
                    <span class="badge bg-info fs-6">@totalMessages Total</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Project Filter -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center gap-3">
                        <label class="form-label mb-0 fw-bold">Filter by Project:</label>
                        <div class="flex-grow-1">
                            <select class="form-select" id="projectFilter" onchange="filterByProject()">
                                <option value="">All Projects</option>
                                @if (projects != null)
                                {
                                    @foreach (var project in projects)
                                    {
                                        if (selectedProjectId == project.Id)
                                        {
                                            <option value="@project.Id" selected>@project.Name</option>
                                        }
                                        else
                                        {
                                            <option value="@project.Id">@project.Name</option>
                                        }
                                    }
                                }
                            </select>
                        </div>
                        <button class="btn btn-outline-primary" onclick="refreshMessages()">
                            <i class="fas fa-sync-alt me-1"></i> Refresh
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Messages List -->
    <div class="row">
        <div class="col-12">
            @if (Model.Any())
            {
                <div class="row">
                    @foreach (var message in Model)
                    {
                        var project = projects?.FirstOrDefault(p => p.Id == message.ProjectId);
                        <div class="col-12 mb-3">
                            <div class="card border-0 shadow-sm @(!message.IsRead && message.SenderRole != "Client" ? "border-start border-primary border-3" : "")">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-md bg-@(message.SenderRole == "Client" ? "primary" : "success") rounded-circle d-flex align-items-center justify-content-center me-3">
                                                <i class="fas fa-@(message.SenderRole == "Client" ? "user" : "user-tie") text-white"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-1">@message.SenderName</h6>
                                                <div class="d-flex align-items-center gap-2">
                                                    <span class="badge bg-@(message.SenderRole == "Client" ? "primary" : "success")">@message.SenderRole</span>
                                                    @if (project != null)
                                                    {
                                                        <span class="badge bg-secondary">@project.Name</span>
                                                    }
                                                    @if (!message.IsRead && message.SenderRole != "Client")
                                                    {
                                                        <span class="badge bg-danger">New</span>
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                        <div class="text-end">
                                            <small class="text-muted">@message.CreatedAt.ToString("MMM dd, yyyy HH:mm")</small>
                                            @if (message.IsRead && message.ReadAt.HasValue)
                                            {
                                                <div>
                                                    <small class="text-success">
                                                        <i class="fas fa-check-double"></i> Read @message.ReadAt.Value.ToString("MMM dd, HH:mm")
                                                    </small>
                                                </div>
                                            }
                                        </div>
                                    </div>

                                    <div class="message-content mb-3">
                                        <p class="mb-0">@message.Content</p>
                                    </div>

                                    @if (project != null)
                                    {
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="d-flex align-items-center text-muted">
                                                <i class="fas fa-project-diagram me-1"></i>
                                                <small>Project: @project.Name</small>
                                            </div>
                                            <div class="d-flex gap-2">
                                                <a asp-controller="Messages" asp-action="Conversation" asp-route-projectId="@project.Id"
                                                   class="btn btn-outline-primary btn-sm">
                                                    <i class="fas fa-comments me-1"></i> View Conversation
                                                </a>
                                                <a asp-controller="Projects" asp-action="Details" asp-route-id="@project.Id"
                                                   class="btn btn-outline-secondary btn-sm">
                                                    <i class="fas fa-eye me-1"></i> View Project
                                                </a>
                                            </div>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                    }
                </div>

                <!-- Pagination would go here if needed -->
            }
            else
            {
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center py-5">
                            <div class="mb-4">
                                <i class="fas fa-comments fa-4x text-muted"></i>
                            </div>
                            <h4 class="text-muted mb-3">No Messages Found</h4>
                            @if (selectedProjectId.HasValue)
                            {
                                <p class="text-muted mb-4">No messages found for the selected project.</p>
                                <button class="btn btn-outline-primary" onclick="clearFilter()">
                                    <i class="fas fa-filter me-1"></i> Clear Filter
                                </button>
                            }
                            else
                            {
                                <p class="text-muted mb-4">You don't have any messages yet. Messages will appear here when your project team communicates with you.</p>
                                <a asp-controller="Projects" asp-action="Index" class="btn btn-primary">
                                    <i class="fas fa-project-diagram me-1"></i> View Projects
                                </a>
                            }
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

<style>
.avatar-md {
    width: 48px;
    height: 48px;
}

.message-content {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 0.5rem;
    border-left: 3px solid var(--bs-primary);
}

.card:hover {
    transform: translateY(-2px);
    transition: transform 0.2s ease-in-out;
}

.text-gradient {
    background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-info) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}
</style>

<script>
function filterByProject() {
    const select = document.getElementById('projectFilter');
    const projectId = select.value;

    if (projectId) {
        window.location.href = '@Url.Action("Index", "Messages")?projectId=' + projectId;
    } else {
        window.location.href = '@Url.Action("Index", "Messages")';
    }
}

function clearFilter() {
    window.location.href = '@Url.Action("Index", "Messages")';
}

function refreshMessages() {
    window.location.reload();
}

// Auto-refresh every 30 seconds to check for new messages
setInterval(function() {
    // Only refresh if user is not actively interacting with the page
    if (document.hidden === false) {
        fetch('@Url.Action("GetUnreadCount", "Messages")')
            .then(response => response.json())
            .then(data => {
                // Update unread count in navigation if it exists
                const unreadBadge = document.querySelector('.unread-count');
                if (unreadBadge && data.count > 0) {
                    unreadBadge.textContent = data.count;
                    unreadBadge.style.display = 'inline';
                } else if (unreadBadge) {
                    unreadBadge.style.display = 'none';
                }
            })
            .catch(error => console.log('Error checking unread count:', error));
    }
}, 30000);
</script>
