@model Technoloway.Web.Areas.Admin.Models.ClientViewModel

@{
    ViewData["Title"] = "Edit Client";
    Layout = "_AdminLayout";
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Edit Client</h1>
    <a asp-action="Index" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">
        <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to List
    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Client Details</h6>
    </div>
    <div class="card-body">
        <form asp-action="Edit" method="post" enctype="multipart/form-data">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="Id" />
            <input type="hidden" asp-for="CreatedAt" />

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <label asp-for="CompanyName" class="control-label">Company Name</label>
                        <input asp-for="CompanyName" class="form-control" />
                        <span asp-validation-for="CompanyName" class="text-danger"></span>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="LogoFile" class="control-label">Company Logo</label>
                        <input asp-for="LogoFile" class="form-control" type="file" accept="image/*" />
                        <span asp-validation-for="LogoFile" class="text-danger"></span>
                        <small class="form-text text-muted">
                            Optional: Upload new company logo. Supported formats: JPG, PNG, GIF, WebP. Maximum size: 5MB. Recommended size: 200x200px.
                        </small>

                        @if (!string.IsNullOrEmpty(Model.LogoUrl) && Model.LogoUrl.StartsWith("/images/"))
                        {
                            <div class="mt-2">
                                <small class="text-muted">Current logo:</small>
                                <div class="d-flex align-items-center mt-1">
                                    <img src="@Model.LogoUrl" alt="Current Logo" style="max-height: 60px; max-width: 120px; object-fit: contain; border: 1px solid #ddd; padding: 5px; border-radius: 4px;" />
                                </div>
                            </div>
                        }

                        <div class="mt-2">
                            <img id="logo-preview" src="@(string.IsNullOrEmpty(Model.LogoUrl) ? "/images/logo-placeholder.png" : Model.LogoUrl)" alt="Logo Preview"
                                 style="max-height: 80px; max-width: 200px; object-fit: contain; border: 1px solid #ddd; padding: 5px; border-radius: 4px;" />
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="ContactName" class="control-label">Contact Name</label>
                        <input asp-for="ContactName" class="form-control" />
                        <span asp-validation-for="ContactName" class="text-danger"></span>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="ContactEmail" class="control-label">Contact Email</label>
                        <input asp-for="ContactEmail" type="email" class="form-control" />
                        <span asp-validation-for="ContactEmail" class="text-danger"></span>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="ContactPhone" class="control-label">Contact Phone</label>
                        <input asp-for="ContactPhone" class="form-control" />
                        <span asp-validation-for="ContactPhone" class="text-danger"></span>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <label asp-for="Address" class="control-label">Address</label>
                        <input asp-for="Address" class="form-control" />
                        <span asp-validation-for="Address" class="text-danger"></span>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="City" class="control-label">City</label>
                        <input asp-for="City" class="form-control" />
                        <span asp-validation-for="City" class="text-danger"></span>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="State" class="control-label">State/Province</label>
                        <input asp-for="State" class="form-control" />
                        <span asp-validation-for="State" class="text-danger"></span>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="ZipCode" class="control-label">Zip/Postal Code</label>
                        <input asp-for="ZipCode" class="form-control" />
                        <span asp-validation-for="ZipCode" class="text-danger"></span>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="Country" class="control-label">Country</label>
                        <input asp-for="Country" class="form-control" />
                        <span asp-validation-for="Country" class="text-danger"></span>
                    </div>
                </div>
            </div>

            <div class="form-group mb-3">
                <label asp-for="UserId" class="control-label">User ID (Optional)</label>
                <input asp-for="UserId" class="form-control" />
                <span asp-validation-for="UserId" class="text-danger"></span>
                <small class="form-text text-muted">Identity user ID if this client should have access to the client portal</small>
            </div>

            <div class="form-group">
                <button type="submit" class="btn btn-primary">Save</button>
                <a asp-action="Index" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script>
        $(document).ready(function() {
            $('#LogoFile').on('change', function() {
                var file = this.files[0];
                if (file) {
                    var reader = new FileReader();
                    reader.onload = function(e) {
                        $('#logo-preview').attr('src', e.target.result);
                    };
                    reader.readAsDataURL(file);
                }
                // Note: Don't reset to placeholder if no file selected, keep current image
            });
        });
    </script>
}
