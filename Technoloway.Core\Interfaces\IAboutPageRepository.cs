using Technoloway.Core.Entities;

namespace Technoloway.Core.Interfaces
{
    public interface IAboutPageRepository : IRepository<AboutPage>
    {
        Task<AboutPage?> GetActiveAboutPageAsync();
        Task<AboutPage?> GetWithSectionsAsync(int id);
        Task<AboutPage?> GetActiveWithSectionsAsync();
        Task<List<AboutPage>> ListAllAsync();
        Task<bool> HasActivePageAsync();
    }
}
