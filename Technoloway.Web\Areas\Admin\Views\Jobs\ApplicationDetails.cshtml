@model Technoloway.Core.Entities.JobApplication

@{
    ViewData["Title"] = "Application Details";
    Layout = "_AdminLayout";
    var jobListing = ViewBag.JobListing as Technoloway.Core.Entities.JobListing;
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Application Details</h1>
    <div>
        <a asp-action="Applications" asp-route-id="@Model.JobListingId" class="btn btn-sm btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Applications
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Applicant Information</h6>
            </div>
            <div class="card-body">
                <h4 class="mb-3">@Model.ApplicantName</h4>

                <div class="row mb-4">
                    <div class="col-md-6">
                        <p><strong>Email:</strong> <a href="mailto:@Model.ApplicantEmail">@Model.ApplicantEmail</a></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Phone:</strong> <a href="tel:@Model.ApplicantPhone">@Model.ApplicantPhone</a></p>
                    </div>
                </div>

                <div class="mb-4">
                    <h5>Cover Letter</h5>
                    <div class="p-3 bg-light rounded">
                        @Model.CoverLetter
                    </div>
                </div>

                <div class="mb-4">
                    <h5>Resume</h5>
                    <a href="@Model.ResumeUrl" target="_blank" class="btn btn-primary">
                        <i class="fas fa-file-pdf"></i> View Resume
                    </a>
                </div>
            </div>
        </div>

        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Notes</h6>
            </div>
            <div class="card-body">
                <form asp-action="UpdateApplicationStatus" method="post">
                    <input type="hidden" name="id" value="@Model.Id" />

                    <div class="form-group mb-3">
                        <label for="notes">Internal Notes</label>
                        <textarea id="notes" name="notes" class="form-control" rows="5">@Model.Notes</textarea>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">Save Notes</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Application Status</h6>
            </div>
            <div class="card-body">
                <form asp-action="UpdateApplicationStatus" method="post">
                    <input type="hidden" name="id" value="@Model.Id" />

                    <div class="form-group mb-3">
                        <label for="status">Current Status</label>
                        <select id="status" name="status" class="form-control">
                            @if (Model.Status == "Pending")
                            {
                                <option value="Pending" selected>Pending</option>
                            }
                            else
                            {
                                <option value="Pending">Pending</option>
                            }

                            @if (Model.Status == "Reviewed")
                            {
                                <option value="Reviewed" selected>Reviewed</option>
                            }
                            else
                            {
                                <option value="Reviewed">Reviewed</option>
                            }

                            @if (Model.Status == "Interviewed")
                            {
                                <option value="Interviewed" selected>Interviewed</option>
                            }
                            else
                            {
                                <option value="Interviewed">Interviewed</option>
                            }

                            @if (Model.Status == "Rejected")
                            {
                                <option value="Rejected" selected>Rejected</option>
                            }
                            else
                            {
                                <option value="Rejected">Rejected</option>
                            }

                            @if (Model.Status == "Hired")
                            {
                                <option value="Hired" selected>Hired</option>
                            }
                            else
                            {
                                <option value="Hired">Hired</option>
                            }
                        </select>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">Update Status</button>
                    </div>
                </form>

                <hr />

                <dl class="row mt-3">
                    <dt class="col-sm-5">Applied On</dt>
                    <dd class="col-sm-7">@Model.CreatedAt.ToString("yyyy-MM-dd")</dd>

                    @if (Model.UpdatedAt.HasValue)
                    {
                        <dt class="col-sm-5">Last Updated</dt>
                        <dd class="col-sm-7">@Model.UpdatedAt.Value.ToString("yyyy-MM-dd")</dd>
                    }
                </dl>
            </div>
        </div>

        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Job Information</h6>
            </div>
            <div class="card-body">
                @if (jobListing != null)
                {
                    <h5>@jobListing.Title</h5>
                    <p class="text-muted">
                        @if (jobListing.IsActive)
                        {
                            <span class="badge bg-success">Active</span>
                        }
                        else
                        {
                            <span class="badge bg-secondary">Inactive</span>
                        }
                        @if (jobListing.ExpiresAt.HasValue && jobListing.ExpiresAt < DateTime.UtcNow)
                        {
                            <span class="badge bg-danger">Expired</span>
                        }
                    </p>

                    <p>
                        <strong>Location:</strong>
                        @if (jobListing.IsRemote)
                        {
                            <span>Remote</span>
                        }
                        else
                        {
                            <span>@jobListing.Location</span>
                        }
                    </p>
                    <p><strong>Type:</strong> @jobListing.EmploymentType</p>

                    <div class="mt-3">
                        <a asp-action="Details" asp-route-id="@jobListing.Id" class="btn btn-info btn-sm">
                            <i class="fas fa-eye"></i> View Job Details
                        </a>
                    </div>
                }
                else
                {
                    <p class="text-danger">Job information not available</p>
                }
            </div>
        </div>
    </div>
</div>
