@model Technoloway.Core.Entities.Feedback
@{
    ViewData["Title"] = "Feedback Details";
    Layout = "~/Areas/Client/Views/Shared/_ClientLayout.cshtml";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-comment-dots text-primary me-2"></i>
                    Feedback Details
                </h2>
                <a href="@Url.Action("Index")" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i> Back to Feedback
                </a>
            </div>

            <div class="row">
                <div class="col-lg-8">
                    <!-- Main Feedback Content -->
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h5 class="mb-1">@Model.Subject</h5>
                                    <small class="text-muted">
                                        Submitted on @Model.CreatedAt.ToString("MMMM dd, yyyy 'at' h:mm tt")
                                    </small>
                                </div>
                                <div class="text-end">
                                    @{
                                        var statusClass = Model.Status switch
                                        {
                                            "New" => "bg-info",
                                            "In Review" => "bg-warning",
                                            "In Progress" => "bg-primary",
                                            "Resolved" => "bg-success",
                                            "Closed" => "bg-secondary",
                                            _ => "bg-secondary"
                                        };
                                    }
                                    <span class="badge @statusClass fs-6">@Model.Status</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="mb-4">
                                <h6 class="text-muted mb-2">Message:</h6>
                                <div class="bg-light p-3 rounded">
                                    @Html.Raw(Model.Message.Replace("\n", "<br>"))
                                </div>
                            </div>

                            @if (!string.IsNullOrEmpty(Model.AdminResponse))
                            {
                                <div class="border-top pt-4">
                                    <h6 class="text-primary mb-2">
                                        <i class="fas fa-reply me-1"></i>
                                        Admin Response
                                    </h6>
                                    <div class="bg-primary bg-opacity-10 p-3 rounded">
                                        @Html.Raw(Model.AdminResponse.Replace("\n", "<br>"))
                                    </div>
                                    <small class="text-muted">
                                        Responded by @Model.AdminName on @Model.ResponseDate?.ToString("MMMM dd, yyyy 'at' h:mm tt")
                                    </small>
                                </div>
                            }
                            else if (Model.Status != "Closed")
                            {
                                <div class="border-top pt-4">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        We have received your feedback and will respond soon. Thank you for your patience!
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <!-- Feedback Information -->
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">Feedback Information</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label text-muted">Type:</label>
                                <div>
                                    <span class="badge bg-info">@Model.FeedbackType</span>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label text-muted">Priority:</label>
                                <div>
                                    @{
                                        var priorityClass = Model.Priority switch
                                        {
                                            "Critical" => "bg-danger",
                                            "High" => "bg-warning",
                                            "Medium" => "bg-primary",
                                            "Low" => "bg-secondary",
                                            _ => "bg-secondary"
                                        };
                                    }
                                    <span class="badge @priorityClass">@Model.Priority</span>
                                </div>
                            </div>

                            @if (Model.Rating.HasValue)
                            {
                                <div class="mb-3">
                                    <label class="form-label text-muted">Your Rating:</label>
                                    <div class="rating-display">
                                        @for (int i = 1; i <= 5; i++)
                                        {
                                            <i class="fas fa-star @(i <= Model.Rating ? "text-warning" : "text-muted")"></i>
                                        }
                                        <span class="ms-2 text-muted">(@Model.Rating/5)</span>
                                    </div>
                                </div>
                            }

                            @if (Model.Project != null)
                            {
                                <div class="mb-3">
                                    <label class="form-label text-muted">Related Project:</label>
                                    <div>
                                        <a href="@Url.Action("Details", "Projects", new { id = Model.Project.Id })" class="text-decoration-none">
                                            <i class="fas fa-project-diagram me-1"></i>
                                            @Model.Project.Name
                                        </a>
                                    </div>
                                </div>
                            }

                            <div class="mb-3">
                                <label class="form-label text-muted">Submitted By:</label>
                                <div>
                                    <strong>@Model.ClientName</strong><br>
                                    <small class="text-muted">@Model.ClientEmail</small>
                                </div>
                            </div>

                            @if (Model.ResolvedAt.HasValue)
                            {
                                <div class="mb-3">
                                    <label class="form-label text-muted">Resolved On:</label>
                                    <div>
                                        @Model.ResolvedAt.Value.ToString("MMMM dd, yyyy 'at' h:mm tt")
                                    </div>
                                </div>
                            }
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="mb-0">Quick Actions</h6>
                        </div>
                        <div class="card-body">
                            <button type="button" class="btn btn-primary w-100 mb-2" data-bs-toggle="modal" data-bs-target="#feedbackModal">
                                <i class="fas fa-plus me-1"></i> Send New Feedback
                            </button>
                            <a href="@Url.Action("Index")" class="btn btn-outline-secondary w-100">
                                <i class="fas fa-list me-1"></i> View All Feedback
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .rating-display {
        font-size: 1.1rem;
    }
    
    .card {
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border: 1px solid rgba(0, 0, 0, 0.125);
    }
    
    .badge {
        font-size: 0.75rem;
    }
    
    .bg-opacity-10 {
        background-color: rgba(var(--bs-primary-rgb), 0.1) !important;
    }
</style>
