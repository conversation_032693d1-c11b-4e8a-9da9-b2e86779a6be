@model Technoloway.Core.Entities.Service

@{
    ViewData["Title"] = "Delete Service";
    Layout = "_AdminLayout";
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Delete Service</h1>
    <a asp-action="Index" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">
        <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to List
    </a>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Are you sure you want to delete this service?</h6>
    </div>
    <div class="card-body">
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle"></i> This action will mark the service as deleted. It will no longer be visible on the website.
        </div>

        <dl class="row">
            <dt class="col-sm-3">Name</dt>
            <dd class="col-sm-9">@Model.Name</dd>

            <dt class="col-sm-3">Icon</dt>
            <dd class="col-sm-9"><i class="@Model.IconClass"></i> @Model.IconClass</dd>

            <dt class="col-sm-3">Description</dt>
            <dd class="col-sm-9">@Model.Description</dd>

            <dt class="col-sm-3">Price</dt>
            <dd class="col-sm-9">@Model.Price.ToString("C")</dd>

            <dt class="col-sm-3">Display Order</dt>
            <dd class="col-sm-9">@Model.DisplayOrder</dd>

            <dt class="col-sm-3">Status</dt>
            <dd class="col-sm-9">
                @if (Model.IsActive)
                {
                    <span class="badge bg-success text-white">Active</span>
                }
                else
                {
                    <span class="badge bg-secondary text-white">Inactive</span>
                }
            </dd>

            <dt class="col-sm-3">Created At</dt>
            <dd class="col-sm-9">@Model.CreatedAt.ToString()</dd>

            <dt class="col-sm-3">Last Updated</dt>
            <dd class="col-sm-9">@Model.UpdatedAt.ToString()</dd>
        </dl>

        <form asp-action="Delete" class="mt-4">
            <input type="hidden" asp-for="Id" />
            <button type="submit" class="btn btn-danger">
                <i class="fas fa-trash"></i> Delete
            </button>
            <a asp-action="Index" class="btn btn-secondary">Cancel</a>
        </form>
    </div>
</div>
