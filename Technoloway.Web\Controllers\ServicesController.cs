using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Technoloway.Core.Entities;
using Technoloway.Core.Interfaces;
using Technoloway.Web.Models;

namespace Technoloway.Web.Controllers;

public class ServicesController : Controller
{
    private readonly IRepository<Service> _serviceRepository;
    private readonly IRepository<Project> _projectRepository;
    private readonly IRepository<Testimonial> _testimonialRepository;
    private readonly IHeroSectionRepository _heroSectionRepository;

    public ServicesController(
        IRepository<Service> serviceRepository,
        IRepository<Project> projectRepository,
        IRepository<Testimonial> testimonialRepository,
        IHeroSectionRepository heroSectionRepository)
    {
        _serviceRepository = serviceRepository;
        _projectRepository = projectRepository;
        _testimonialRepository = testimonialRepository;
        _heroSectionRepository = heroSectionRepository;
    }

    public async Task<IActionResult> Index()
    {
        var heroSection = await _heroSectionRepository.GetActiveByPageWithSlidesAsync("Services");
        var services = await _serviceRepository.ListAsync(s => s.IsActive);

        ViewBag.HeroSection = heroSection;
        ViewBag.PageName = "Services";
        return View(services);
    }

    public async Task<IActionResult> Details(int id)
    {
        var service = await _serviceRepository.GetByIdAsync(id);
        if (service == null || !service.IsActive)
        {
            return NotFound();
        }

        // Get related projects for this service
        var relatedProjects = await _projectRepository.GetAll()
            .Where(p => p.OrderID == id && !p.IsDeleted)
            .OrderByDescending(p => p.ProjCompletionDate)
            .Take(3)
            .ToListAsync();

        // Get testimonials
        var testimonials = await _testimonialRepository.ListAsync(t => t.IsActive);

        var viewModel = new ServiceDetailsViewModel
        {
            Service = service,
            RelatedProjects = relatedProjects,
            Testimonials = testimonials
        };

        return View(viewModel);
    }
}
