@model Technoloway.Core.Entities.AboutPage

@{
    ViewData["Title"] = "About Page Details";
    ViewData["PageTitle"] = "About Page Details";
    ViewData["PageDescription"] = "View about page content and sections";
}

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-1">
                <i class="fas fa-info-circle me-2 text-primary"></i>About Page Details
            </h1>
            <p class="text-muted mb-0">View about page content and configuration</p>
        </div>
        <div class="btn-group">
            <a asp-action="Index" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>Back to List
            </a>
            <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-primary">
                <i class="fas fa-edit me-1"></i>Edit
            </a>
        </div>
    </div>

    <!-- Success Message -->
    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>@TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <div class="row">
        <!-- Main Information -->
        <div class="col-lg-8">
            <!-- Basic Information -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Basic Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Title:</strong></div>
                        <div class="col-sm-9">@Model.Title</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Meta Description:</strong></div>
                        <div class="col-sm-9">@(Model.MetaDescription ?? "Not set")</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Meta Keywords:</strong></div>
                        <div class="col-sm-9">@(Model.MetaKeywords ?? "Not set")</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Status:</strong></div>
                        <div class="col-sm-9">
                            @if (Model.IsActive)
                            {
                                <span class="badge bg-success">
                                    <i class="fas fa-check-circle me-1"></i>Active
                                </span>
                            }
                            else
                            {
                                <span class="badge bg-danger">
                                    <i class="fas fa-times-circle me-1"></i>Inactive
                                </span>
                            }
                        </div>
                    </div>
                </div>
            </div>

            <!-- Hero Section -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-star me-2"></i>Hero Section
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Hero Title:</strong></div>
                        <div class="col-sm-9">@Model.HeroTitle</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Hero Subtitle:</strong></div>
                        <div class="col-sm-9">@Model.HeroSubtitle</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Hero Image:</strong></div>
                        <div class="col-sm-9">@(Model.HeroImageUrl ?? "Not set")</div>
                    </div>
                </div>
            </div>

            <!-- Story Section -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-book me-2"></i>Story Section
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Story Title:</strong></div>
                        <div class="col-sm-9">@Model.StoryTitle</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Story Subtitle:</strong></div>
                        <div class="col-sm-9">@Model.StorySubtitle</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Story Content:</strong></div>
                        <div class="col-sm-9">
                            <div class="border p-3 bg-light" style="max-height: 200px; overflow-y: auto;">
                                @Html.Raw(Model.StoryContent)
                            </div>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-3"><strong>Story Image:</strong></div>
                        <div class="col-sm-9">@(Model.StoryImageUrl ?? "Not set")</div>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Statistics
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="row mb-3">
                                <div class="col-sm-4"><strong>Stat 1:</strong></div>
                                <div class="col-sm-8">@(Model.Stat1Number ?? "Not set") - @(Model.Stat1Label ?? "Not set")</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-4"><strong>Stat 2:</strong></div>
                                <div class="col-sm-8">@(Model.Stat2Number ?? "Not set") - @(Model.Stat2Label ?? "Not set")</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="row mb-3">
                                <div class="col-sm-4"><strong>Stat 3:</strong></div>
                                <div class="col-sm-8">@(Model.Stat3Number ?? "Not set") - @(Model.Stat3Label ?? "Not set")</div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-sm-4"><strong>Stat 4:</strong></div>
                                <div class="col-sm-8">@(Model.Stat4Number ?? "Not set") - @(Model.Stat4Label ?? "Not set")</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Metadata -->
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>Metadata
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-sm-5"><strong>Last Modified:</strong></div>
                        <div class="col-sm-7">
                            <div>@Model.LastModified.ToString("MMM dd, yyyy")</div>
                            <small class="text-muted">@Model.LastModified.ToString("hh:mm tt")</small>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-5"><strong>Modified By:</strong></div>
                        <div class="col-sm-7">
                            <span class="badge bg-light text-dark">
                                @(Model.ModifiedBy ?? "System")
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Mission & Vision -->
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-bullseye me-2"></i>Mission & Vision
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-sm-5"><strong>Mission Title:</strong></div>
                        <div class="col-sm-7">@(Model.MissionTitle ?? "Not set")</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-5"><strong>Vision Title:</strong></div>
                        <div class="col-sm-7">@(Model.VisionTitle ?? "Not set")</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-5"><strong>Values Title:</strong></div>
                        <div class="col-sm-7">@(Model.ValuesTitle ?? "Not set")</div>
                    </div>
                </div>
            </div>

            <!-- CTA Section -->
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-mouse-pointer me-2"></i>Call to Action
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-sm-5"><strong>CTA Title:</strong></div>
                        <div class="col-sm-7">@(Model.CtaTitle ?? "Not set")</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-5"><strong>Primary Button:</strong></div>
                        <div class="col-sm-7">@(Model.CtaPrimaryButtonText ?? "Not set")</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-sm-5"><strong>Secondary Button:</strong></div>
                        <div class="col-sm-7">@(Model.CtaSecondaryButtonText ?? "Not set")</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Sections -->
    @if (Model.Sections?.Any() == true)
    {
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>Additional Sections (@Model.Sections.Count)
                </h5>
            </div>
            <div class="card-body">
                @foreach (var aboutSection in Model.Sections.OrderBy(s => s.DisplayOrder))
                {
                    <div class="card mb-3">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                @if (!string.IsNullOrEmpty(aboutSection.IconClass))
                                {
                                    <i class="@aboutSection.IconClass me-2 text-primary"></i>
                                }
                                <strong>@aboutSection.Title</strong>
                                <span class="badge bg-secondary ms-2">@aboutSection.SectionType</span>
                                @if (aboutSection.IsActive)
                                {
                                    <span class="badge bg-success ms-1">Active</span>
                                }
                                else
                                {
                                    <span class="badge bg-danger ms-1">Inactive</span>
                                }
                            </div>
                            <small class="text-muted">Order: @aboutSection.DisplayOrder</small>
                        </div>
                        <div class="card-body">
                            <div class="border p-3 bg-light" style="max-height: 200px; overflow-y: auto;">
                                @Html.Raw(aboutSection.Content)
                            </div>
                        </div>
                    </div>
                }
            </div>
        </div>
    }
</div>
