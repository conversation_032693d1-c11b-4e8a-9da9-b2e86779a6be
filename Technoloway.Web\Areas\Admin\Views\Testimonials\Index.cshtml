@model IEnumerable<Technoloway.Core.Entities.Testimonial>

@{
    ViewData["Title"] = "Testimonials";
    Layout = "_AdminLayout";
}

<div class="container-fluid p-4">
    <!-- Success/Error Messages -->
    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show mb-4" role="alert">
            <i class="fas fa-check-circle me-2"></i>@TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show mb-4" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>@TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800 fw-bold">Testimonials Management</h1>
            <p class="text-muted mb-0">Manage client testimonials and reviews</p>
        </div>
        <div class="d-flex gap-2">
            <button class="btn-modern-admin secondary">
                <i class="fas fa-filter"></i>
                Filter
            </button>
            <button class="btn-modern-admin secondary">
                <i class="fas fa-sort-amount-down"></i>
                Reorder
            </button>
            <a asp-action="Create" class="btn-modern-admin primary">
                <i class="fas fa-plus"></i>
                Add Testimonial
            </a>
        </div>
    </div>

    <!-- Testimonial Stats Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon me-3">
                        <i class="fas fa-quote-left"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Total Testimonials</p>
                        <h3 class="admin-stat-number">@Model.Count()</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card success h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon success me-3">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Active</p>
                        <h3 class="admin-stat-number">@Model.Count(t => t.IsActive)</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card warning h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon warning me-3">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Avg Rating</p>
                        <h3 class="admin-stat-number">@(Model.Any() ? Model.Average(t => t.Rating).ToString("F1") : "0.0")</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card info h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon info me-3">
                        <i class="fas fa-camera"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">With Photos</p>
                        <h3 class="admin-stat-number">@Model.Count(t => !string.IsNullOrEmpty(t.ClientPhotoUrl))</h3>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Testimonials Management Card -->
    <div class="admin-card">
        <div class="card-header bg-white border-bottom d-flex align-items-center justify-content-between p-4">
            <div>
                <h5 class="mb-0 fw-bold text-gray-800">All Testimonials</h5>
                <p class="text-muted mb-0 small">@Model.Count() total testimonials</p>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-download me-1"></i>
                    Export
                </button>
                <button class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-sync me-1"></i>
                    Refresh
                </button>
            </div>
        </div>
        <div class="card-body p-0">
            @if (Model.Any())
            {
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="dataTable">
                        <thead>
                            <tr>
                                <th class="border-0 fw-semibold">Client</th>
                                <th class="border-0 fw-semibold">Company</th>
                                <th class="border-0 fw-semibold">Rating</th>
                                <th class="border-0 fw-semibold">Content</th>
                                <th class="border-0 fw-semibold">Status</th>
                                <th class="border-0 fw-semibold">Order</th>
                                <th class="border-0 fw-semibold">Created</th>
                                <th class="border-0 fw-semibold">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var testimonial in Model.OrderBy(t => t.DisplayOrder))
                            {
                                <tr>
                                    <td class="border-0">
                                        <div class="d-flex align-items-center">
                                            <div class="me-3">
                                                @if (!string.IsNullOrEmpty(testimonial.ClientPhotoUrl))
                                                {
                                                    <img src="@testimonial.ClientPhotoUrl" alt="@testimonial.ClientName"
                                                         class="rounded-circle" style="width: 50px; height: 50px; object-fit: cover; border: 3px solid var(--admin-border);" />
                                                }
                                                else
                                                {
                                                    <div class="admin-stat-icon d-flex align-items-center justify-content-center"
                                                         style="width: 50px; height: 50px;">
                                                        <i class="fas fa-user text-white"></i>
                                                    </div>
                                                }
                                            </div>
                                            <div>
                                                <div class="fw-semibold text-gray-800">@testimonial.ClientName</div>
                                                <div class="text-muted small">@testimonial.ClientTitle</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="border-0">
                                        <span class="badge bg-light text-dark">
                                            <i class="fas fa-building me-1"></i>@testimonial.ClientCompany
                                        </span>
                                    </td>
                                    <td class="border-0">
                                        <div class="d-flex align-items-center">
                                            <div class="me-2">
                                                @for (int i = 0; i < testimonial.Rating; i++)
                                                {
                                                    <i class="fas fa-star text-warning"></i>
                                                }
                                                @for (int i = testimonial.Rating; i < 5; i++)
                                                {
                                                    <i class="far fa-star text-muted"></i>
                                                }
                                            </div>
                                            <span class="badge bg-warning text-dark">@testimonial.Rating/5</span>
                                        </div>
                                    </td>
                                    <td class="border-0">
                                        <div style="max-width: 300px;" class="position-relative">
                                            @if (testimonial.Content.Length > 100)
                                            {
                                                <span class="testimonial-content" data-full-content="@testimonial.Content">
                                                    @(testimonial.Content.Substring(0, 100) + "...")
                                                </span>
                                                <button class="btn btn-link btn-sm p-0 ms-1" onclick="toggleContent(this)" title="Show full content">
                                                    <i class="fas fa-expand-alt"></i>
                                                </button>
                                            }
                                            else
                                            {
                                                <span>@testimonial.Content</span>
                                            }
                                        </div>
                                    </td>
                                    <td class="border-0">
                                        <button class="btn btn-sm @(testimonial.IsActive ? "btn-outline-success" : "btn-outline-secondary")"
                                                onclick="toggleActive(@testimonial.Id, this)">
                                            <i class="fas @(testimonial.IsActive ? "fa-check" : "fa-times") me-1"></i>
                                            @(testimonial.IsActive ? "Active" : "Inactive")
                                        </button>
                                    </td>
                                    <td class="border-0">
                                        <span class="badge bg-light text-dark">#@testimonial.DisplayOrder</span>
                                    </td>
                                    <td class="border-0">
                                        <div class="text-muted small">
                                            <i class="fas fa-calendar me-1"></i>
                                            @testimonial.CreatedAt.ToString("MMM dd, yyyy")
                                        </div>
                                    </td>
                                    <td class="border-0">
                                        <div class="d-flex gap-1">
                                            <a asp-action="Details" asp-route-id="@testimonial.Id"
                                               class="btn btn-outline-info btn-sm" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a asp-action="Edit" asp-route-id="@testimonial.Id"
                                               class="btn btn-outline-primary btn-sm" title="Edit Testimonial">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button class="btn btn-outline-secondary btn-sm" title="Move Up"
                                                    onclick="moveOrder(@testimonial.Id, 'up')">
                                                <i class="fas fa-arrow-up"></i>
                                            </button>
                                            <button class="btn btn-outline-secondary btn-sm" title="Move Down"
                                                    onclick="moveOrder(@testimonial.Id, 'down')">
                                                <i class="fas fa-arrow-down"></i>
                                            </button>
                                            <a asp-action="Delete" asp-route-id="@testimonial.Id"
                                               class="btn btn-outline-danger btn-sm" title="Delete Testimonial"
                                               onclick="return confirm('Are you sure you want to delete this testimonial?')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
            else
            {
                <div class="text-center py-5">
                    <i class="fas fa-quote-left text-muted mb-3" style="font-size: 3rem;"></i>
                    <h5 class="text-muted">No testimonials found</h5>
                    <p class="text-muted">Start by adding your first client testimonial.</p>
                    <a asp-action="Create" class="btn-modern-admin primary">
                        <i class="fas fa-plus me-2"></i>
                        Add First Testimonial
                    </a>
                </div>
            }
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#dataTable').DataTable({
                "pageLength": 10,
                "responsive": true,
                "order": [[5, "asc"]], // Sort by Display Order by default
                "columnDefs": [
                    { "orderable": false, "targets": [0, 7] } // Disable sorting for Client and Actions columns
                ]
            });
        });

        // Add hover effects to stat cards
        $('.admin-stat-card').hover(
            function() {
                $(this).addClass('shadow-lg');
            },
            function() {
                $(this).removeClass('shadow-lg');
            }
        );

        // Enhanced toggle active function
        function toggleActive(id, button) {
            $.post('@Url.Action("ToggleActive")', { id: id })
                .done(function(data) {
                    if (data.success) {
                        if (data.isActive) {
                            $(button).removeClass('btn-outline-secondary').addClass('btn-outline-success');
                            $(button).find('i').removeClass('fa-times').addClass('fa-check');
                            $(button).html('<i class="fas fa-check me-1"></i>Active');
                        } else {
                            $(button).removeClass('btn-outline-success').addClass('btn-outline-secondary');
                            $(button).find('i').removeClass('fa-check').addClass('fa-times');
                            $(button).html('<i class="fas fa-times me-1"></i>Inactive');
                        }
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .fail(function() {
                    alert('Error updating testimonial status');
                });
        }

        // Function to move testimonial order
        function moveOrder(testimonialId, direction) {
            if (confirm(`Are you sure you want to move this testimonial ${direction}?`)) {
                // Here you would make an AJAX call to update the display order
                // For now, we'll just show an alert
                // $.post('/Admin/Testimonials/MoveOrder', { id: testimonialId, direction: direction })
                //     .done(function() {
                //         location.reload();
                //     });

                // Temporary: Just show an alert
                alert(`Testimonial moved ${direction} successfully! (This would normally update via AJAX)`);
            }
        }

        // Function to toggle content display
        function toggleContent(button) {
            const contentSpan = $(button).siblings('.testimonial-content');
            const fullContent = contentSpan.data('full-content');
            const currentContent = contentSpan.text();

            if (currentContent.includes('...')) {
                contentSpan.text(fullContent);
                $(button).html('<i class="fas fa-compress-alt"></i>');
                $(button).attr('title', 'Show less');
            } else {
                contentSpan.text(fullContent.substring(0, 100) + '...');
                $(button).html('<i class="fas fa-expand-alt"></i>');
                $(button).attr('title', 'Show full content');
            }
        }

        // Add testimonial statistics calculations
        function updateTestimonialStats() {
            const totalTestimonials = @Model.Count();
            const activeTestimonials = @Model.Count(t => t.IsActive);
            const withPhotos = @Model.Count(t => !string.IsNullOrEmpty(t.ClientPhotoUrl));
            const avgRating = @(Model.Any() ? Model.Average(t => t.Rating).ToString("F1") : "0.0");

            if (totalTestimonials > 0) {
                const activePercentage = Math.round((activeTestimonials / totalTestimonials) * 100);
                const photoPercentage = Math.round((withPhotos / totalTestimonials) * 100);
                console.log(`Active testimonials: ${activePercentage}%`);
                console.log(`Testimonials with photos: ${photoPercentage}%`);
                console.log(`Average rating: ${avgRating}/5`);
            }
        }

        updateTestimonialStats();

        // Add client photo hover effects
        $('.rounded-circle, .admin-stat-icon').hover(
            function() {
                $(this).css('transform', 'scale(1.1)');
                $(this).css('transition', 'transform 0.3s ease');
            },
            function() {
                $(this).css('transform', 'scale(1)');
            }
        );

        // Add star rating hover effects
        $('.fa-star').hover(
            function() {
                $(this).css('transform', 'scale(1.2)');
                $(this).css('transition', 'transform 0.2s ease');
            },
            function() {
                $(this).css('transform', 'scale(1)');
            }
        );

        // Add badge hover effects
        $('.badge').hover(
            function() {
                $(this).css('transform', 'scale(1.05)');
                $(this).css('transition', 'transform 0.2s ease');
            },
            function() {
                $(this).css('transform', 'scale(1)');
            }
        );

        // Enhanced confirmation for delete
        $('a[asp-action="Delete"]').click(function(e) {
            const clientName = $(this).closest('tr').find('.fw-semibold').text();
            if (!confirm(`Are you sure you want to delete the testimonial from "${clientName}"?`)) {
                e.preventDefault();
                return false;
            }
        });

        // Add quote icon hover effects in stat cards
        $('.fa-quote-left').parent().hover(
            function() {
                $(this).css('transform', 'rotate(5deg) scale(1.1)');
                $(this).css('transition', 'transform 0.3s ease');
            },
            function() {
                $(this).css('transform', 'rotate(0deg) scale(1)');
            }
        );
    </script>
}
