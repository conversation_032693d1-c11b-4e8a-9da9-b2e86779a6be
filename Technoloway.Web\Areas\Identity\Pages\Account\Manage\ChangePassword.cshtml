@page
@model Technoloway.Web.Areas.Identity.Pages.Account.Manage.ChangePasswordModel
@{
    ViewData["Title"] = "Change password";
    ViewData["ActivePage"] = "ChangePassword";
    Layout = "_ClientLoginLayout";
}

<div class="modern-client-login-header">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="login-header-content text-center">
                    <div class="login-breadcrumb">
                        <a href="/" class="breadcrumb-link">
                            <i class="fas fa-home"></i>
                            <span>Home</span>
                        </a>
                        <span class="breadcrumb-separator">•</span>
                        <a asp-page="./Index" class="breadcrumb-link">Account Settings</a>
                        <span class="breadcrumb-separator">•</span>
                        <span class="breadcrumb-current">Change Password</span>
                    </div>
                    <h1 class="login-title">
                        <span class="title-highlight">Change</span> Password
                    </h1>
                    <p class="login-subtitle">
                        Update your account password for better security
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<section class="modern-section client-login-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-6">
                <div class="modern-card login-card">
                    <div class="card-header">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-lock me-2"></i>Change Password
                        </h3>
                    </div>

                    <div class="card-body">
                        @if (Model.StatusMessage != null)
                        {
                            <div class="modern-alert @(Model.StatusMessage.Contains("Error") ? "error" : "success") mb-4">
                                <div class="alert-icon">
                                    <i class="fas @(Model.StatusMessage.Contains("Error") ? "fa-exclamation-triangle" : "fa-check-circle")"></i>
                                </div>
                                <div class="alert-content">
                                    <p class="mb-0">@Model.StatusMessage</p>
                                </div>
                            </div>
                        }

                        <form id="change-password-form" method="post" class="modern-form">
                            <div asp-validation-summary="ModelOnly" class="validation-summary"></div>
                            
                            <div class="form-group">
                                <label asp-for="Input.OldPassword" class="form-label">
                                    <i class="fas fa-key"></i>
                                    <span>Current Password</span>
                                </label>
                                <div class="password-input-wrapper">
                                    <input asp-for="Input.OldPassword" class="form-input" autocomplete="current-password" />
                                    <button type="button" class="password-toggle" onclick="togglePassword('Input_OldPassword')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <span asp-validation-for="Input.OldPassword" class="form-error"></span>
                            </div>

                            <div class="form-group">
                                <label asp-for="Input.NewPassword" class="form-label">
                                    <i class="fas fa-lock"></i>
                                    <span>New Password</span>
                                </label>
                                <div class="password-input-wrapper">
                                    <input asp-for="Input.NewPassword" class="form-input" autocomplete="new-password" />
                                    <button type="button" class="password-toggle" onclick="togglePassword('Input_NewPassword')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <span asp-validation-for="Input.NewPassword" class="form-error"></span>
                            </div>

                            <div class="form-group">
                                <label asp-for="Input.ConfirmPassword" class="form-label">
                                    <i class="fas fa-lock"></i>
                                    <span>Confirm New Password</span>
                                </label>
                                <div class="password-input-wrapper">
                                    <input asp-for="Input.ConfirmPassword" class="form-input" autocomplete="new-password" />
                                    <button type="button" class="password-toggle" onclick="togglePassword('Input_ConfirmPassword')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <span asp-validation-for="Input.ConfirmPassword" class="form-error"></span>
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Update Password
                                </button>
                                <a asp-page="./Index" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>Back to Profile
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script>
        function togglePassword(inputId) {
            const input = document.getElementById(inputId);
            const icon = input.nextElementSibling.querySelector('i');
            
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }
    </script>
}
