using System.ComponentModel.DataAnnotations;

namespace Technoloway.Web.Areas.Admin.Models
{
    public class LegalPageViewModel
    {
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        [Display(Name = "Title")]
        public string Title { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        [Display(Name = "Slug")]
        [RegularExpression(@"^[a-z0-9-]+$", ErrorMessage = "Slug can only contain lowercase letters, numbers, and hyphens.")]
        public string Slug { get; set; } = string.Empty;

        [StringLength(200)]
        [Display(Name = "Meta Description")]
        public string? MetaDescription { get; set; }

        [Required]
        [Display(Name = "Content")]
        public string Content { get; set; } = string.Empty;

        [Display(Name = "Is Active")]
        public bool IsActive { get; set; } = true;

        public DateTime LastModified { get; set; }

        [Display(Name = "Modified By")]
        public string? ModifiedBy { get; set; }

        [Display(Name = "Sections")]
        public List<LegalPageSectionViewModel> Sections { get; set; } = new List<LegalPageSectionViewModel>();
    }

    public class LegalPageSectionViewModel
    {
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        [Display(Name = "Section Title")]
        public string Title { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Section Content")]
        public string Content { get; set; } = string.Empty;

        [StringLength(50)]
        [Display(Name = "Icon Class")]
        public string? IconClass { get; set; }

        [Display(Name = "Display Order")]
        public int DisplayOrder { get; set; }

        [Display(Name = "Is Active")]
        public bool IsActive { get; set; } = true;
    }
}
