using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Technoloway.Core.Interfaces;
using Technoloway.Core.Entities;

namespace Technoloway.Web.Areas.Client.Controllers;

[Area("Client")]
[Authorize(Policy = "RequireClient")]
public class ProjectsController : Controller
{
    private readonly IRepository<Project> _projectRepository;
    private readonly IRepository<Message> _messageRepository;
    private readonly IRepository<ProjectDocument> _documentRepository;
    private readonly UserManager<IdentityUser> _userManager;
    private readonly IRepository<Core.Entities.Client> _clientRepository;

    public ProjectsController(
        IRepository<Project> projectRepository,
        IRepository<Message> messageRepository,
        IRepository<ProjectDocument> documentRepository,
        UserManager<IdentityUser> userManager,
        IRepository<Core.Entities.Client> clientRepository)
    {
        _projectRepository = projectRepository;
        _messageRepository = messageRepository;
        _documentRepository = documentRepository;
        _userManager = userManager;
        _clientRepository = clientRepository;
    }

    public async Task<IActionResult> Index()
    {
        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return Challenge();
        }

        var client = (await _clientRepository.ListAsync(c => c.UserId == user.Id)).FirstOrDefault();
        if (client == null)
        {
            return RedirectToAction("Create", "Profile");
        }

        var projects = await _projectRepository.GetAll()
            .Where(p => p.ClientId == client.Id && !p.IsDeleted)
            .Include(p => p.Order)
            .Include(p => p.Technologies)
            .OrderByDescending(p => p.CreatedAt)
            .ToListAsync();

        return View(projects);
    }

    public async Task<IActionResult> Details(int id)
    {
        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return Challenge();
        }

        var client = (await _clientRepository.ListAsync(c => c.UserId == user.Id)).FirstOrDefault();
        if (client == null)
        {
            return RedirectToAction("Create", "Profile");
        }

        var project = await _projectRepository.GetAll()
            .Where(p => p.Id == id && p.ClientId == client.Id && !p.IsDeleted)
            .Include(p => p.Order)
            .Include(p => p.Technologies)
            .Include(p => p.Documents)
            .FirstOrDefaultAsync();

        if (project == null)
        {
            return NotFound();
        }

        var messages = await _messageRepository.ListAsync(m => m.ProjectId == id);
        ViewBag.Messages = messages.AsEnumerable().OrderByDescending(m => m.CreatedAt).Take(5).ToList();
        ViewBag.MessageCount = messages.Count;
        ViewBag.UnreadMessageCount = messages.Count(m => !m.IsRead && m.SenderRole != "Client");

        return View(project);
    }

    public async Task<IActionResult> Messages(int id)
    {
        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return Challenge();
        }

        var client = (await _clientRepository.ListAsync(c => c.UserId == user.Id)).FirstOrDefault();
        if (client == null)
        {
            return RedirectToAction("Create", "Profile");
        }

        var project = await _projectRepository.GetAll()
            .Where(p => p.Id == id && p.ClientId == client.Id && !p.IsDeleted)
            .FirstOrDefaultAsync();

        if (project == null)
        {
            return NotFound();
        }

        var messages = await _messageRepository.ListAsync(m => m.ProjectId == id);
        ViewBag.Project = project;

        // Mark messages as read
        var unreadMessages = messages.Where(m => !m.IsRead && m.SenderRole != "Client").ToList();
        foreach (var message in unreadMessages)
        {
            message.IsRead = true;
            message.ReadAt = DateTime.UtcNow;
            await _messageRepository.UpdateAsync(message);
        }

        return View(messages.AsEnumerable().OrderBy(m => m.CreatedAt));
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> SendMessage(int projectId, string content)
    {
        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return Challenge();
        }

        var client = (await _clientRepository.ListAsync(c => c.UserId == user.Id)).FirstOrDefault();
        if (client == null)
        {
            return RedirectToAction("Create", "Profile");
        }

        var project = await _projectRepository.GetAll()
            .Where(p => p.Id == projectId && p.ClientId == client.Id && !p.IsDeleted)
            .FirstOrDefaultAsync();

        if (project == null)
        {
            return NotFound();
        }

        if (string.IsNullOrWhiteSpace(content))
        {
            TempData["Error"] = "Message content cannot be empty.";
            return RedirectToAction("Messages", new { id = projectId });
        }

        var message = new Message
        {
            ProjectId = projectId,
            Content = content.Trim(),
            SenderId = user.Id,
            SenderName = client.ContactName,
            SenderRole = "Client",
            IsRead = false,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow
        };

        await _messageRepository.AddAsync(message);
        TempData["Success"] = "Message sent successfully.";

        return RedirectToAction("Messages", new { id = projectId });
    }

    public async Task<IActionResult> Documents(int id)
    {
        var user = await _userManager.GetUserAsync(User);
        if (user == null)
        {
            return Challenge();
        }

        var client = (await _clientRepository.ListAsync(c => c.UserId == user.Id)).FirstOrDefault();
        if (client == null)
        {
            return RedirectToAction("Create", "Profile");
        }

        var project = await _projectRepository.GetAll()
            .Where(p => p.Id == id && p.ClientId == client.Id && !p.IsDeleted)
            .FirstOrDefaultAsync();

        if (project == null)
        {
            return NotFound();
        }

        var documents = await _documentRepository.ListAsync(d => d.ProjectId == id);
        ViewBag.Project = project;

        return View(documents.OrderByDescending(d => d.CreatedAt));
    }
}
