@model IEnumerable<Technoloway.Core.Entities.TeamMember>

@{
    ViewData["Title"] = "Team Members";
    Layout = "_AdminLayout";
}

<div class="container-fluid p-4">
    <!-- Success Message -->
    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show mb-4" role="alert">
            <i class="fas fa-check-circle me-2"></i>@TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800 fw-bold">Team Members Management</h1>
            <p class="text-muted mb-0">Manage your team roster and member profiles</p>
        </div>
        <div class="d-flex gap-2">
            <button class="btn-modern-admin secondary">
                <i class="fas fa-filter"></i>
                Filter
            </button>
            <button class="btn-modern-admin secondary">
                <i class="fas fa-users"></i>
                Departments
            </button>
            <a asp-action="Create" class="btn-modern-admin primary">
                <i class="fas fa-plus"></i>
                Add New Member
            </a>
        </div>
    </div>

    <!-- Team Stats Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon me-3">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Total Members</p>
                        <h3 class="admin-stat-number">@Model.Count()</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card success h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon success me-3">
                        <i class="fas fa-user-check"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Active Members</p>
                        <h3 class="admin-stat-number">@Model.Count(m => m.IsActive)</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card warning h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon warning me-3">
                        <i class="fas fa-user-times"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Inactive Members</p>
                        <h3 class="admin-stat-number">@Model.Count(m => !m.IsActive)</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card info h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon info me-3">
                        <i class="fas fa-camera"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">With Photos</p>
                        <h3 class="admin-stat-number">@Model.Count(m => !string.IsNullOrEmpty(m.PhotoUrl))</h3>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Team Members Management Card -->
    <div class="admin-card">
        <div class="card-header bg-white border-bottom d-flex align-items-center justify-content-between p-4">
            <div>
                <h5 class="mb-0 fw-bold text-gray-800">All Team Members</h5>
                <p class="text-muted mb-0 small">@Model.Count() total team members</p>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-download me-1"></i>
                    Export
                </button>
                <button class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-sync me-1"></i>
                    Refresh
                </button>
            </div>
        </div>
        <div class="card-body p-0">
            @if (Model.Any())
            {
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="dataTable">
                        <thead>
                            <tr>
                                <th class="border-0 fw-semibold">Member</th>
                                <th class="border-0 fw-semibold">Position</th>
                                <th class="border-0 fw-semibold">Contact</th>
                                <th class="border-0 fw-semibold">Order</th>
                                <th class="border-0 fw-semibold">Status</th>
                                <th class="border-0 fw-semibold">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model.OrderBy(m => m.DisplayOrder))
                            {
                                <tr>
                                    <td class="border-0">
                                        <div class="d-flex align-items-center">
                                            <div class="me-3">
                                                @if (!string.IsNullOrEmpty(item.PhotoUrl))
                                                {
                                                    <img src="@item.PhotoUrl" alt="@item.Name" class="rounded-circle"
                                                         style="height: 50px; width: 50px; object-fit: cover; border: 3px solid var(--admin-border);" />
                                                }
                                                else
                                                {
                                                    <div class="admin-stat-icon d-flex align-items-center justify-content-center"
                                                         style="width: 50px; height: 50px;">
                                                        <i class="fas fa-user text-white"></i>
                                                    </div>
                                                }
                                            </div>
                                            <div>
                                                <div class="fw-semibold text-gray-800">@item.Name</div>
                                                <div class="text-muted small">Team Member</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="border-0">
                                        <span class="badge bg-light text-dark">
                                            <i class="fas fa-briefcase me-1"></i>@item.Position
                                        </span>
                                    </td>
                                    <td class="border-0">
                                        @if (!string.IsNullOrEmpty(item.Email))
                                        {
                                            <a href="mailto:@item.Email" class="text-decoration-none">
                                                <i class="fas fa-envelope me-1"></i>@item.Email
                                            </a>
                                        }
                                        else
                                        {
                                            <span class="text-muted fst-italic">
                                                <i class="fas fa-envelope me-1"></i>Not provided
                                            </span>
                                        }
                                    </td>
                                    <td class="border-0">
                                        <span class="badge bg-light text-dark">#@item.DisplayOrder</span>
                                    </td>
                                    <td class="border-0">
                                        @if (item.IsActive)
                                        {
                                            <span class="badge bg-success">
                                                <i class="fas fa-check me-1"></i>Active
                                            </span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-secondary">
                                                <i class="fas fa-pause me-1"></i>Inactive
                                            </span>
                                        }
                                    </td>
                                    <td class="border-0">
                                        <div class="d-flex gap-1">
                                            <a asp-action="Details" asp-route-id="@item.Id"
                                               class="btn btn-outline-info btn-sm" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a asp-action="Edit" asp-route-id="@item.Id"
                                               class="btn btn-outline-primary btn-sm" title="Edit Member">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <form asp-action="ToggleStatus" asp-route-id="@item.Id" method="post" style="display: inline;">
                                                @Html.AntiForgeryToken()
                                                <button type="submit" class="btn btn-outline-@(item.IsActive ? "warning" : "success") btn-sm"
                                                        title="@(item.IsActive ? "Deactivate" : "Activate")"
                                                        onclick="return confirm('@(item.IsActive ? "Deactivate" : "Activate") this team member?')">
                                                    <i class="fas @(item.IsActive ? "fa-pause" : "fa-play")"></i>
                                                </button>
                                            </form>
                                            <button class="btn btn-outline-secondary btn-sm" title="Move Up"
                                                    onclick="moveOrder(@item.Id, 'up')">
                                                <i class="fas fa-arrow-up"></i>
                                            </button>
                                            <button class="btn btn-outline-secondary btn-sm" title="Move Down"
                                                    onclick="moveOrder(@item.Id, 'down')">
                                                <i class="fas fa-arrow-down"></i>
                                            </button>
                                            <a asp-action="Delete" asp-route-id="@item.Id"
                                               class="btn btn-outline-danger btn-sm" title="Delete Member"
                                               onclick="return confirm('Are you sure you want to delete this team member?')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
            else
            {
                <div class="text-center py-5">
                    <i class="fas fa-users text-muted mb-3" style="font-size: 3rem;"></i>
                    <h5 class="text-muted">No team members found</h5>
                    <p class="text-muted">Add your first team member to get started.</p>
                    <a asp-action="Create" class="btn-modern-admin primary">
                        <i class="fas fa-plus me-2"></i>
                        Add First Member
                    </a>
                </div>
            }
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#dataTable').DataTable({
                "pageLength": 10,
                "responsive": true,
                "order": [[3, "asc"]], // Sort by Display Order by default
                "columnDefs": [
                    { "orderable": false, "targets": [0, 5] } // Disable sorting for Member and Actions columns
                ]
            });
        });

        // Function to move team member order
        function moveOrder(memberId, direction) {
            if (confirm(`Are you sure you want to move this team member ${direction}?`)) {
                // Here you would make an AJAX call to update the display order
                // For now, we'll just reload the page
                // $.post('/Admin/TeamMembers/MoveOrder', { id: memberId, direction: direction })
                //     .done(function() {
                //         location.reload();
                //     });

                // Temporary: Just show an alert
                alert(`Team member moved ${direction} successfully! (This would normally update via AJAX)`);
            }
        }

        // Add hover effects to stat cards
        $('.admin-stat-card').hover(
            function() {
                $(this).addClass('shadow-lg');
            },
            function() {
                $(this).removeClass('shadow-lg');
            }
        );

        // Add team statistics calculations
        function updateTeamStats() {
            const totalMembers = @Model.Count();
            const activeMembers = @Model.Count(m => m.IsActive);
            const withPhotos = @Model.Count(m => !string.IsNullOrEmpty(m.PhotoUrl));

            if (totalMembers > 0) {
                const activePercentage = Math.round((activeMembers / totalMembers) * 100);
                const photoPercentage = Math.round((withPhotos / totalMembers) * 100);
                console.log(`Active team members: ${activePercentage}%`);
                console.log(`Members with photos: ${photoPercentage}%`);
            }
        }

        updateTeamStats();

        // Add profile photo hover effects
        $('.rounded-circle').hover(
            function() {
                $(this).css('transform', 'scale(1.1)');
                $(this).css('transition', 'transform 0.3s ease');
            },
            function() {
                $(this).css('transform', 'scale(1)');
            }
        );

        // Enhanced confirmation for status toggle
        $('form[asp-action="ToggleStatus"] button').click(function(e) {
            const isActive = $(this).hasClass('btn-outline-warning');
            const memberName = $(this).closest('tr').find('.fw-semibold').text();
            const action = isActive ? 'deactivate' : 'activate';

            if (!confirm(`Are you sure you want to ${action} ${memberName}?`)) {
                e.preventDefault();
                return false;
            }
        });
    </script>
}
