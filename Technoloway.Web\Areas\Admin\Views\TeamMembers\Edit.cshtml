@model Technoloway.Web.Areas.Admin.Models.TeamMemberViewModel

@{
    ViewData["Title"] = "Edit Team Member";
    Layout = "_AdminLayout";
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Edit Team Member</h1>
    <div>
        <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-sm btn-info shadow-sm">
            <i class="fas fa-eye fa-sm text-white-50"></i> View Details
        </a>
        <a asp-action="Index" class="btn btn-sm btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to List
        </a>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Team Member Details</h6>
    </div>
    <div class="card-body">
        <form asp-action="Edit" enctype="multipart/form-data">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="Id" />
            <input type="hidden" asp-for="CreatedAt" />

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <label asp-for="Name" class="control-label"></label>
                        <input asp-for="Name" class="form-control" placeholder="e.g., John Doe" />
                        <span asp-validation-for="Name" class="text-danger"></span>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <label asp-for="Position" class="control-label"></label>
                        <input asp-for="Position" class="form-control" placeholder="e.g., Senior Developer, CEO" />
                        <span asp-validation-for="Position" class="text-danger"></span>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <label asp-for="Email" class="control-label"></label>
                        <input asp-for="Email" class="form-control" type="email" placeholder="<EMAIL>" />
                        <span asp-validation-for="Email" class="text-danger"></span>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <label asp-for="PhotoFile" class="control-label">Photo</label>
                        <input asp-for="PhotoFile" class="form-control" type="file" accept="image/*" />
                        <span asp-validation-for="PhotoFile" class="text-danger"></span>
                        <small class="form-text text-muted">
                            Upload a new photo to replace the current one. Supported formats: JPG, PNG, GIF, WebP. Maximum size: 5MB.
                        </small>

                        @if (!string.IsNullOrEmpty(Model.PhotoUrl))
                        {
                            <div class="mt-2">
                                <small class="text-muted">Current photo:</small><br>
                                <img src="@Model.PhotoUrl" alt="@Model.Name" class="rounded-circle" style="height: 80px; width: 80px; object-fit: cover;" />
                            </div>
                        }

                        <div id="photo-preview" class="mt-2" style="display: none;">
                            <small class="text-muted">New photo preview:</small><br>
                            <img id="preview-img" class="rounded-circle" style="height: 80px; width: 80px; object-fit: cover;" />
                        </div>

                        <input type="hidden" asp-for="PhotoUrl" />
                    </div>
                </div>
            </div>

            <div class="form-group mb-3">
                <label asp-for="Bio" class="control-label">Biography</label>
                <textarea asp-for="Bio" class="form-control" rows="4" placeholder="Brief biography and background..."></textarea>
                <span asp-validation-for="Bio" class="text-danger"></span>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label asp-for="LinkedInUrl" class="control-label">LinkedIn URL</label>
                        <input asp-for="LinkedInUrl" class="form-control" placeholder="https://linkedin.com/in/username" />
                        <span asp-validation-for="LinkedInUrl" class="text-danger"></span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label asp-for="TwitterUrl" class="control-label">Twitter URL</label>
                        <input asp-for="TwitterUrl" class="form-control" placeholder="https://twitter.com/username" />
                        <span asp-validation-for="TwitterUrl" class="text-danger"></span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label asp-for="GithubUrl" class="control-label">GitHub URL</label>
                        <input asp-for="GithubUrl" class="form-control" placeholder="https://github.com/username" />
                        <span asp-validation-for="GithubUrl" class="text-danger"></span>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label asp-for="DisplayOrder" class="control-label"></label>
                        <input asp-for="DisplayOrder" class="form-control" type="number" min="0" />
                        <span asp-validation-for="DisplayOrder" class="text-danger"></span>
                        <small class="form-text text-muted">Lower numbers appear first</small>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label asp-for="IsActive" class="control-label"></label>
                        <div class="form-check mt-2">
                            <input asp-for="IsActive" class="form-check-input" />
                            <label class="form-check-label" for="IsActive">Is Active</label>
                        </div>
                        <span asp-validation-for="IsActive" class="text-danger"></span>
                    </div>
                </div>
            </div>

            <div class="form-group mt-4">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Update Team Member
                </button>
                <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-info">View Details</a>
                <a asp-action="Index" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script>
        // Preview photo when file is selected
        $('#PhotoFile').on('change', function() {
            var file = this.files[0];
            var preview = $('#photo-preview');
            var previewImg = $('#preview-img');

            if (file) {
                // Validate file type
                var validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
                if (!validTypes.includes(file.type)) {
                    alert('Please select a valid image file (JPG, PNG, GIF, WebP)');
                    $(this).val('');
                    preview.hide();
                    return;
                }

                // Validate file size (5MB)
                if (file.size > 5 * 1024 * 1024) {
                    alert('File size must be less than 5MB');
                    $(this).val('');
                    preview.hide();
                    return;
                }

                // Show preview
                var reader = new FileReader();
                reader.onload = function(e) {
                    previewImg.attr('src', e.target.result);
                    preview.show();
                };
                reader.readAsDataURL(file);
            } else {
                preview.hide();
            }
        });

        // Drag and drop functionality
        var dropArea = $('#PhotoFile').parent();

        dropArea.on('dragover', function(e) {
            e.preventDefault();
            $(this).addClass('border-primary');
        });

        dropArea.on('dragleave', function(e) {
            e.preventDefault();
            $(this).removeClass('border-primary');
        });

        dropArea.on('drop', function(e) {
            e.preventDefault();
            $(this).removeClass('border-primary');

            var files = e.originalEvent.dataTransfer.files;
            if (files.length > 0) {
                $('#PhotoFile')[0].files = files;
                $('#PhotoFile').trigger('change');
            }
        });
    </script>
}
