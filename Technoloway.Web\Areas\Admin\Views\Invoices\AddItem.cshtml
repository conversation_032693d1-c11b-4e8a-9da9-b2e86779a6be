@model Technoloway.Core.Entities.InvoiceItem

@{
    ViewData["Title"] = "Add Invoice Item";
    Layout = "_AdminLayout";
    var invoice = ViewBag.Invoice as Technoloway.Core.Entities.Invoice;
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Add Item to Invoice #@(invoice != null ? invoice.InvoiceNumber : "")</h1>
    <div>
        <a asp-action="Items" asp-route-id="@(invoice != null ? invoice.Id : 0)" class="btn btn-sm btn-success shadow-sm">
            <i class="fas fa-list fa-sm text-white-50"></i> View Items
        </a>
        <a asp-action="Details" asp-route-id="@(invoice != null ? invoice.Id : 0)" class="btn btn-sm btn-info shadow-sm">
            <i class="fas fa-eye fa-sm text-white-50"></i> View Invoice
        </a>
    </div>
</div>

<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Item Details</h6>
    </div>
    <div class="card-body">
        <form asp-action="AddItem" method="post">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <input type="hidden" asp-for="InvoiceId" />
            
            <div class="form-group mb-3">
                <label asp-for="Description" class="control-label">Description</label>
                <textarea asp-for="Description" class="form-control" rows="3" required></textarea>
                <span asp-validation-for="Description" class="text-danger"></span>
            </div>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label asp-for="Quantity" class="control-label">Quantity</label>
                        <input asp-for="Quantity" type="number" step="0.01" min="0.01" class="form-control" required />
                        <span asp-validation-for="Quantity" class="text-danger"></span>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label asp-for="UnitPrice" class="control-label">Unit Price</label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input asp-for="UnitPrice" type="number" step="0.01" min="0" class="form-control" required />
                        </div>
                        <span asp-validation-for="UnitPrice" class="text-danger"></span>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label class="control-label">Total Price</label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">$</span>
                            </div>
                            <input type="text" id="totalPrice" class="form-control" readonly />
                        </div>
                        <small class="form-text text-muted">Calculated automatically based on quantity and unit price.</small>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <button type="submit" class="btn btn-primary">Add Item</button>
                <a asp-action="Items" asp-route-id="@(invoice != null ? invoice.Id : 0)" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        $(document).ready(function() {
            // Calculate total price when quantity or unit price changes
            $('#Quantity, #UnitPrice').on('input', function() {
                calculateTotal();
            });
            
            function calculateTotal() {
                var quantity = parseFloat($('#Quantity').val()) || 0;
                var unitPrice = parseFloat($('#UnitPrice').val()) || 0;
                var totalPrice = quantity * unitPrice;
                
                $('#totalPrice').val(totalPrice.toFixed(2));
            }
            
            // Initial calculation
            calculateTotal();
        });
    </script>
}
