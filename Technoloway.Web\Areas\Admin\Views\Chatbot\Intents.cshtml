@model IEnumerable<Technoloway.Core.Entities.ChatbotIntent>
@{
    ViewData["Title"] = "Chatbot Intents";
}

<!-- Hidden anti-forgery token for JavaScript access -->
@Html.AntiForgeryToken()

<div class="chatbot-admin">
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-brain me-2 text-primary"></i>
                        Chatbot Intents
                    </h2>
                    <p class="text-muted mb-0">Manage conversation intents and user interactions</p>
                </div>
                <a href="@Url.Action("CreateIntent")" class="btn btn-primary btn-lg shadow-sm">
                    <i class="fas fa-plus me-2"></i>
                    Add New Intent
                </a>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="rounded-circle bg-primary bg-opacity-10 p-3">
                            <i class="fas fa-brain fa-lg text-primary"></i>
                        </div>
                    </div>
                    <h4 class="mb-1">@Model.Count()</h4>
                    <p class="text-muted mb-0 small">Total Intents</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="rounded-circle bg-success bg-opacity-10 p-3">
                            <i class="fas fa-check-circle fa-lg text-success"></i>
                        </div>
                    </div>
                    <h4 class="mb-1">@Model.Count(i => i.IsActive)</h4>
                    <p class="text-muted mb-0 small">Active Intents</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="rounded-circle bg-warning bg-opacity-10 p-3">
                            <i class="fas fa-pause-circle fa-lg text-warning"></i>
                        </div>
                    </div>
                    <h4 class="mb-1">@Model.Count(i => !i.IsActive)</h4>
                    <p class="text-muted mb-0 small">Inactive Intents</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="rounded-circle bg-info bg-opacity-10 p-3">
                            <i class="fas fa-clock fa-lg text-info"></i>
                        </div>
                    </div>
                    <h4 class="mb-1">@(Model.Any() ? Model.Max(i => i.CreatedAt).ToString("MMM dd") : "N/A")</h4>
                    <p class="text-muted mb-0 small">Latest Added</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    @if (Model.Any())
    {
        <div class="row">
            @foreach (var intent in Model)
            {
                <div class="col-lg-6 col-xl-4 mb-4">
                    <div class="card border-0 shadow-sm h-100 intent-card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center mb-2">
                                        @if (!string.IsNullOrEmpty(intent.IconClass))
                                        {
                                            <i class="@intent.IconClass me-2 text-primary"></i>
                                        }
                                        else
                                        {
                                            <i class="fas fa-brain me-2 text-primary"></i>
                                        }
                                        <h5 class="card-title mb-0">@intent.DisplayName</h5>
                                    </div>
                                    <p class="text-muted small mb-0">
                                        <code class="bg-light px-2 py-1 rounded">@intent.Name</code>
                                    </p>
                                </div>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end">
                                        <li>
                                            <a class="dropdown-item" href="@Url.Action("EditIntent", new { id = intent.Id })">
                                                <i class="fas fa-edit me-2"></i>Edit Intent
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="@Url.Action("Keywords", new { intentId = intent.Id })">
                                                <i class="fas fa-key me-2"></i>Manage Keywords
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="@Url.Action("Responses", new { intentId = intent.Id })">
                                                <i class="fas fa-comments me-2"></i>Manage Responses
                                            </a>
                                        </li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <form id="<EMAIL>" method="post" action="@Url.Action("DeleteIntent", "Chatbot", new { Area = "Admin", id = intent.Id })" style="display: inline;" onsubmit="return confirm('⚠️ PERMANENT DELETE WARNING ⚠️\n\nAre you sure you want to PERMANENTLY delete the intent \'@intent.DisplayName\'?\n\nThis will PERMANENTLY remove from the database:\n• The intent itself\n• All @intent.Keywords.Count keywords\n• All @intent.Responses.Count responses\n• All related quick actions\n\nThis action CANNOT be undone!')">
                                                @Html.AntiForgeryToken()
                                                <button type="submit" class="dropdown-item text-danger">
                                                    <i class="fas fa-trash me-2"></i>Delete Intent
                                                </button>
                                            </form>
                                        </li>
                                    </ul>
                                </div>
                            </div>

                            <div class="mb-3">
                                @if (!string.IsNullOrEmpty(intent.Description))
                                {
                                    <p class="card-text text-muted small">@intent.Description</p>
                                }
                                else
                                {
                                    <p class="card-text text-muted small fst-italic">No description provided</p>
                                }
                            </div>

                            <div class="row g-2 mb-3">
                                <div class="col-6">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-key text-info me-2"></i>
                                        <span class="small text-muted">Keywords:</span>
                                        <span class="badge bg-info ms-2">@intent.Keywords.Count</span>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-comments text-success me-2"></i>
                                        <span class="small text-muted">Responses:</span>
                                        <span class="badge bg-success ms-2">@intent.Responses.Count</span>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    @if (intent.IsActive)
                                    {
                                        <span class="badge bg-success-subtle text-success border border-success-subtle">
                                            <i class="fas fa-check-circle me-1"></i>Active
                                        </span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-secondary-subtle text-secondary border border-secondary-subtle">
                                            <i class="fas fa-pause-circle me-1"></i>Inactive
                                        </span>
                                    }
                                </div>
                                <small class="text-muted">
                                    Order: @intent.DisplayOrder
                                </small>
                            </div>
                        </div>
                        <div class="card-footer bg-transparent border-0 pt-0">
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="@Url.Action("EditIntent", new { id = intent.Id })"
                                   class="btn btn-outline-primary btn-sm flex-fill">
                                    <i class="fas fa-edit me-1"></i>Edit
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    }
    else
    {
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center py-5">
                        <div class="empty-state">
                            <div class="mb-4">
                                <div class="rounded-circle bg-primary bg-opacity-10 p-4 d-inline-flex">
                                    <i class="fas fa-brain fa-3x text-primary"></i>
                                </div>
                            </div>
                            <h4 class="mb-3">No Intents Found</h4>
                            <p class="text-muted mb-4">Get started by creating your first chatbot intent to handle user conversations.</p>
                            <a href="@Url.Action("CreateIntent")" class="btn btn-primary btn-lg">
                                <i class="fas fa-plus me-2"></i>
                                Create Your First Intent
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>
</div>

@section Scripts {
    <script>
        // Add debugging to the existing delete forms (optional - can be removed in production)
        $(document).ready(function() {
            $('form[id^="delete-form-"]').on('submit', function(e) {
                console.log('Delete form submitting:', {
                    action: $(this).attr('action'),
                    method: $(this).attr('method'),
                    id: $(this).attr('id')
                });

                // Let the form submit normally
                return true;
            });
        });
    </script>
}
