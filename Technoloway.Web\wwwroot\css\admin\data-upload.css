/* Data Upload Manager Styles */

.page-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.page-title {
    font-size: 2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.page-subtitle {
    color: #6c757d;
    margin-bottom: 0;
}

/* Upload Steps */
.upload-step {
    padding: 2rem 0;
    border-bottom: 1px solid #e9ecef;
}

.upload-step:last-child {
    border-bottom: none;
}

.step-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
}

.step-number {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    background: #007bff;
    color: white;
    border-radius: 50%;
    font-weight: 600;
    margin-right: 1rem;
    font-size: 0.9rem;
}

/* File Drop Zone */
.file-drop-zone {
    border: 2px dashed #dee2e6;
    border-radius: 0.5rem;
    padding: 3rem 2rem;
    text-align: center;
    background: #f8f9fa;
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-drop-zone:hover {
    border-color: #007bff;
    background: #e3f2fd;
}

.file-drop-zone.dragover {
    border-color: #28a745;
    background: #d4edda;
}

.drop-zone-content h5 {
    color: #495057;
    margin-bottom: 0.5rem;
}

.drop-zone-content p {
    margin-bottom: 1.5rem;
}

/* Supported Formats */
.supported-formats {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 0.5rem;
    border: 1px solid #e9ecef;
}

.supported-formats h6 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1rem;
}

.supported-formats ul li {
    padding: 0.25rem 0;
    color: #6c757d;
}

/* File Info */
#fileInfo {
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Data Preview Table */
#previewTable {
    font-size: 0.875rem;
}

#previewTable th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
    border-top: none;
}

#previewTable td {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Field Mapping */
.mapping-row {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 0.5rem;
    border: 1px solid #e9ecef;
}

.mapping-row .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.mapping-arrow {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    font-size: 1.25rem;
    margin: 0 1rem;
}

/* Validation Results */
.validation-error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 0.75rem;
    border-radius: 0.375rem;
    margin-bottom: 0.5rem;
}

.validation-warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    padding: 0.75rem;
    border-radius: 0.375rem;
    margin-bottom: 0.5rem;
}

/* Upload Progress */
#uploadProgress {
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Statistics */
.stat-item {
    text-align: center;
    padding: 1rem;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    line-height: 1;
}

.stat-label {
    font-size: 0.875rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 0.5rem;
}

/* Upload History */
.table th {
    font-weight: 600;
    color: #495057;
    border-top: none;
}

.table td {
    vertical-align: middle;
}

.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-title {
        font-size: 1.5rem;
    }
    
    .file-drop-zone {
        padding: 2rem 1rem;
    }
    
    .mapping-row {
        flex-direction: column;
        text-align: center;
    }
    
    .mapping-arrow {
        transform: rotate(90deg);
        margin: 1rem 0;
    }
    
    .stat-number {
        font-size: 1.5rem;
    }
}

/* Loading States */
.btn.loading {
    position: relative;
    color: transparent !important;
}

.btn.loading::after {
    content: "";
    position: absolute;
    width: 1rem;
    height: 1rem;
    top: 50%;
    left: 50%;
    margin-left: -0.5rem;
    margin-top: -0.5rem;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Custom Alerts */
.alert-custom {
    border: none;
    border-radius: 0.5rem;
    padding: 1rem 1.5rem;
}

.alert-custom .alert-icon {
    font-size: 1.25rem;
    margin-right: 0.75rem;
}

/* Form Enhancements */
.form-control:focus,
.form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-check-input:checked {
    background-color: #007bff;
    border-color: #007bff;
}

/* Card Enhancements */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
}

.card-header {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    border-radius: 0.5rem 0.5rem 0 0 !important;
}

/* Button Enhancements */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
    padding: 0.5rem 1rem;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* Modal Enhancements */
.modal-content {
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    border-radius: 0.5rem 0.5rem 0 0;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 0.5rem 0.5rem;
}

/* Utility Classes */
.text-truncate-custom {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.border-dashed {
    border-style: dashed !important;
}

.cursor-pointer {
    cursor: pointer;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .file-drop-zone {
        background: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .file-drop-zone:hover {
        background: #374151;
        border-color: #60a5fa;
    }
    
    .supported-formats {
        background: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .mapping-row {
        background: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
}
