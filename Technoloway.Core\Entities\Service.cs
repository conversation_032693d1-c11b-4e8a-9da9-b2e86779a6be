using Technoloway.Core.Common;
using System.ComponentModel.DataAnnotations;

namespace Technoloway.Core.Entities;

public class Service : BaseEntity
{
    [Required]
    public int CategID { get; set; }

    [Required]
    [StringLength(255)]
    public string Name { get; set; } = string.Empty;

    [Required]
    public string Description { get; set; } = string.Empty;

    [StringLength(100)]
    public string? IconClass { get; set; }

    [Required]
    public decimal Price { get; set; }

    public int? ServDiscountRate { get; set; }

    public decimal? ServTotalDiscount { get; set; }

    [StringLength(50)]
    public string? ServManager { get; set; }

    public bool IsActive { get; set; } = true;
    public int DisplayOrder { get; set; }

    // Navigation properties
    public Category Category { get; set; } = null!;
    public ICollection<Project> Projects { get; set; } = new List<Project>();
    public ICollection<ServiceOption> ServiceOptions { get; set; } = new List<ServiceOption>();
    public ICollection<OrderDetail> OrderDetails { get; set; } = new List<OrderDetail>();
}
