@page
@model Technoloway.Web.Areas.Identity.Pages.Account.ForgotPasswordModel

@{
    ViewData["Title"] = "Forgot Password";
    Layout = "_ClientLoginLayout";
}

<div class="modern-client-login-header">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="login-header-content text-center">
                    <div class="login-breadcrumb">
                        <a href="/" class="breadcrumb-link">
                            <i class="fas fa-home"></i>
                            <span>Home</span>
                        </a>
                        <span class="breadcrumb-separator">•</span>
                        <a asp-page="./Login" class="breadcrumb-link">
                            <i class="fas fa-sign-in-alt"></i>
                            <span>Login</span>
                        </a>
                        <span class="breadcrumb-separator">•</span>
                        <span class="breadcrumb-current">Forgot Password</span>
                    </div>
                    <h1 class="login-title">
                        <span class="title-highlight">Reset</span> Password
                    </h1>
                    <p class="login-subtitle">
                        Enter your email address and we'll send you a link to reset your password
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modern Forgot Password Section -->
<section class="modern-section client-login-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-5 col-md-7">
                <div class="modern-card login-card">
                    <div class="card-header text-center">
                        <div class="login-logo">
                            <img src="~/images/technoloway-logo.svg" alt="Technoloway Logo" class="brand-logo" />
                        </div>
                        <h3 class="card-title">
                            <span class="title-highlight">Password</span> Recovery
                        </h3>
                        <p class="card-subtitle">
                            We'll help you get back into your account
                        </p>
                    </div>

                    <div class="card-body">
                        <form method="post" class="modern-form">
                            <div asp-validation-summary="ModelOnly" class="validation-summary"></div>

                            <div class="form-group">
                                <label asp-for="Input.Email" class="form-label">
                                    <i class="fas fa-envelope"></i>
                                    <span>Email Address</span>
                                </label>
                                <input asp-for="Input.Email" class="form-input" placeholder="Enter your email address" autocomplete="username" />
                                <span asp-validation-for="Input.Email" class="form-error"></span>
                            </div>

                            <button type="submit" class="btn-modern primary full-width">
                                <i class="fas fa-paper-plane"></i>
                                <span>Send Reset Link</span>
                            </button>
                        </form>

                        <div class="login-footer">
                            <div class="footer-links">
                                <a href="/" class="footer-link">
                                    <i class="fas fa-arrow-left"></i>
                                    <span>Back to Website</span>
                                </a>
                                <a asp-page="./Login" class="footer-link">
                                    <i class="fas fa-sign-in-alt"></i>
                                    <span>Back to Login</span>
                                </a>
                            </div>
                            <div class="additional-links">
                                <a asp-page="./Register" class="additional-link">
                                    <i class="fas fa-user-plus"></i>
                                    <span>Don't have an account? Register</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Help Notice -->
                <div class="client-portal-notice">
                    <div class="portal-icon">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <div class="portal-content">
                        <h5>Need Help?</h5>
                        <p>If you're having trouble accessing your account, please contact our support team for assistance.</p>
                        <a href="/contact" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-envelope"></i> Contact Support
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
