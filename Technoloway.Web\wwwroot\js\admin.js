// Enhanced Admin Dashboard JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize sidebar functionality
    initializeSidebar();

    // Initialize navigation
    initializeNavigation();

    // Initialize animations
    initializeAnimations();

    // Initialize theme
    initializeTheme();

    // Initialize dashboard features
    initializeDashboardFeatures();

    // Initialize search functionality
    initializeSearch();

    // Initialize notifications
    initializeNotifications();
});

// Sidebar functionality
function initializeSidebar() {
    const sidebarToggle = document.getElementById('sidebarToggle');
    const wrapper = document.querySelector('#wrapper');
    const sidebar = document.querySelector('.modern-sidebar');

    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function(e) {
            e.preventDefault();
            wrapper.classList.toggle('toggled');

            // Add animation class
            sidebar.classList.add('sidebar-animating');
            setTimeout(() => {
                sidebar.classList.remove('sidebar-animating');
            }, 300);
        });
    }

    // Close sidebar on outside click (mobile)
    document.addEventListener('click', function(e) {
        if (window.innerWidth < 768) {
            const isClickInsideSidebar = sidebar.contains(e.target);
            const isToggleButton = sidebarToggle.contains(e.target);

            if (!isClickInsideSidebar && !isToggleButton && !wrapper.classList.contains('toggled')) {
                wrapper.classList.add('toggled');
            }
        }
    });
}

// Enhanced navigation functionality
function initializeNavigation() {
    const currentPath = window.location.pathname.toLowerCase();
    const navItems = document.querySelectorAll('.nav-item');

    // Remove active class from all items
    navItems.forEach(item => item.classList.remove('active'));

    // Add active class to current page
    navItems.forEach(item => {
        const href = item.getAttribute('href');
        if (href) {
            const hrefPath = href.toLowerCase();

            // Check for exact match or if current path contains the nav path
            if (currentPath === hrefPath ||
                (hrefPath !== '/admin' && currentPath.includes(hrefPath)) ||
                (currentPath.includes('/admin/home') && hrefPath.includes('/admin/home'))) {
                item.classList.add('active');

                // Add pulse animation to active item
                const icon = item.querySelector('.nav-icon');
                if (icon) {
                    icon.style.animation = 'pulse 2s infinite';
                }
            }
        }
    });

    // Add hover effects
    navItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(8px)';

            const icon = this.querySelector('.nav-icon');
            if (icon && !this.classList.contains('active')) {
                icon.style.transform = 'scale(1.1) rotate(5deg)';
            }
        });

        item.addEventListener('mouseleave', function() {
            if (!this.classList.contains('active')) {
                this.style.transform = 'translateX(0)';
            } else {
                this.style.transform = 'translateX(4px)';
            }

            const icon = this.querySelector('.nav-icon');
            if (icon && !this.classList.contains('active')) {
                icon.style.transform = 'scale(1)';
            }
        });
    });
}

// Animation enhancements
function initializeAnimations() {
    // Stagger animation for nav items
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach((item, index) => {
        item.style.animationDelay = `${(index + 1) * 0.1}s`;
    });

    // Brand logo animation
    const brandIcon = document.querySelector('.brand-icon');
    if (brandIcon) {
        brandIcon.addEventListener('mouseenter', function() {
            this.style.transform = 'rotate(5deg) scale(1.1)';
        });

        brandIcon.addEventListener('mouseleave', function() {
            this.style.transform = 'rotate(0deg) scale(1)';
        });
    }

    // User avatar animation
    const userAvatar = document.querySelector('.user-avatar');
    if (userAvatar) {
        userAvatar.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.1) rotate(5deg)';
        });

        userAvatar.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1) rotate(0deg)';
        });
    }

    // Sidebar user hover effect
    const sidebarUser = document.querySelector('.sidebar-user');
    if (sidebarUser) {
        sidebarUser.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 8px 16px rgba(0, 0, 0, 0.15)';
        });

        sidebarUser.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = 'none';
        });
    }
}

// Theme and visual enhancements
function initializeTheme() {
    // Add smooth scrolling to sidebar
    const sidebar = document.querySelector('.modern-sidebar');
    if (sidebar) {
        sidebar.style.scrollBehavior = 'smooth';
    }

    // Add loading animation
    document.body.classList.add('admin-loaded');

    // Enhance nav section titles
    const sectionTitles = document.querySelectorAll('.nav-section-title');
    sectionTitles.forEach(title => {
        title.addEventListener('mouseenter', function() {
            this.style.color = 'rgba(255, 255, 255, 0.8)';
        });

        title.addEventListener('mouseleave', function() {
            this.style.color = 'rgba(255, 255, 255, 0.5)';
        });
    });
}

// Utility functions
function addRippleEffect(element, event) {
    const ripple = document.createElement('span');
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;

    ripple.style.width = ripple.style.height = size + 'px';
    ripple.style.left = x + 'px';
    ripple.style.top = y + 'px';
    ripple.classList.add('ripple');

    element.appendChild(ripple);

    setTimeout(() => {
        ripple.remove();
    }, 600);
}

// Add ripple effect to nav items
document.addEventListener('DOMContentLoaded', function() {
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        item.addEventListener('click', function(e) {
            addRippleEffect(this, e);
        });
    });
});

// Responsive handling
window.addEventListener('resize', function() {
    const wrapper = document.querySelector('#wrapper');

    if (window.innerWidth >= 768) {
        wrapper.classList.remove('toggled');
    } else {
        wrapper.classList.add('toggled');
    }
});

// Add CSS for ripple effect
const style = document.createElement('style');
style.textContent = `
    .nav-item {
        position: relative;
        overflow: hidden;
    }

    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }

    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }

    body.admin-loaded .modern-sidebar {
        animation: slideInLeft 0.5s ease-out;
    }

    .sidebar-animating {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    }
`;
document.head.appendChild(style);

// Dashboard Features
function initializeDashboardFeatures() {
    // Initialize real-time clock
    updateCurrentTime();
    setInterval(updateCurrentTime, 1000);

    // Initialize KPI card animations
    animateKPICards();

    // Initialize progress circles
    initializeProgressCircles();

    // Initialize refresh buttons
    initializeRefreshButtons();

    // Initialize filter buttons
    initializeFilterButtons();

    // Initialize tooltips
    initializeTooltips();

    // Initialize clickable elements
    initializeClickableElements();
}

function updateCurrentTime() {
    const timeElement = document.getElementById('currentTime');
    if (timeElement) {
        const now = new Date();
        const timeString = now.toLocaleString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
        timeElement.textContent = ` • ${timeString}`;
    }
}

function animateKPICards() {
    const kpiCards = document.querySelectorAll('.kpi-card');
    kpiCards.forEach((card, index) => {
        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 150);
    });
}

function initializeProgressCircles() {
    const progressCircles = document.querySelectorAll('.progress-circle');
    progressCircles.forEach(circle => {
        const percentage = circle.getAttribute('data-percentage');
        if (percentage) {
            animateProgressCircle(circle, percentage);
        }
    });
}

function animateProgressCircle(circle, percentage) {
    let currentPercentage = 0;
    const increment = percentage / 50; // 50 steps for smooth animation

    const animation = setInterval(() => {
        currentPercentage += increment;
        if (currentPercentage >= percentage) {
            currentPercentage = percentage;
            clearInterval(animation);
        }
        circle.style.setProperty('--progress', currentPercentage + '%');
    }, 20);
}

function initializeRefreshButtons() {
    const refreshButtons = document.querySelectorAll('.refresh-btn');
    refreshButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            this.style.transform = 'rotate(360deg)';
            setTimeout(() => {
                this.style.transform = 'rotate(0deg)';
            }, 500);

            // Add loading state
            this.classList.add('loading');
            setTimeout(() => {
                this.classList.remove('loading');
            }, 1000);
        });
    });
}

function initializeFilterButtons() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    filterButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            // Remove active class from siblings
            const siblings = this.parentElement.querySelectorAll('.filter-btn');
            siblings.forEach(sibling => sibling.classList.remove('active'));

            // Add active class to clicked button
            this.classList.add('active');

            // Add ripple effect
            addRippleEffect(this, event);
        });
    });
}

function initializeTooltips() {
    const tooltipElements = document.querySelectorAll('[title]');
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', showTooltip);
        element.addEventListener('mouseleave', hideTooltip);
    });
}

function showTooltip(event) {
    const tooltip = document.createElement('div');
    tooltip.className = 'custom-tooltip';
    tooltip.textContent = event.target.getAttribute('title');

    // Remove title to prevent default tooltip
    event.target.setAttribute('data-title', event.target.getAttribute('title'));
    event.target.removeAttribute('title');

    document.body.appendChild(tooltip);

    const rect = event.target.getBoundingClientRect();
    tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
    tooltip.style.top = rect.top - tooltip.offsetHeight - 8 + 'px';

    setTimeout(() => tooltip.classList.add('show'), 10);
}

function hideTooltip(event) {
    const tooltip = document.querySelector('.custom-tooltip');
    if (tooltip) {
        tooltip.remove();
    }

    // Restore title
    const title = event.target.getAttribute('data-title');
    if (title) {
        event.target.setAttribute('title', title);
        event.target.removeAttribute('data-title');
    }
}

// Search Functionality
function initializeSearch() {
    const searchInput = document.querySelector('.search-input');
    const searchResults = document.getElementById('searchResults');

    if (searchInput && searchResults) {
        let searchTimeout;

        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();

            if (query.length > 2) {
                searchTimeout = setTimeout(() => {
                    performSearch(query, searchResults);
                }, 300);
            } else {
                searchResults.style.display = 'none';
            }
        });

        // Hide results when clicking outside
        document.addEventListener('click', function(e) {
            if (!searchInput.contains(e.target) && !searchResults.contains(e.target)) {
                searchResults.style.display = 'none';
            }
        });
    }
}

function performSearch(query, resultsContainer) {
    // Mock search results - in real implementation, this would be an API call
    const mockResults = [
        { type: 'project', title: 'E-commerce Website', url: '/Admin/Projects/Details/1' },
        { type: 'client', title: 'Acme Corporation', url: '/Admin/Clients/Details/1' },
        { type: 'invoice', title: 'INV-2024-001', url: '/Admin/Invoices/Details/1' }
    ].filter(item => item.title.toLowerCase().includes(query.toLowerCase()));

    if (mockResults.length > 0) {
        resultsContainer.innerHTML = mockResults.map(result => `
            <div class="search-result-item">
                <div class="result-icon">
                    <i class="fas fa-${getIconForType(result.type)}"></i>
                </div>
                <div class="result-content">
                    <div class="result-title">${result.title}</div>
                    <div class="result-type">${result.type}</div>
                </div>
            </div>
        `).join('');

        resultsContainer.style.display = 'block';
    } else {
        resultsContainer.innerHTML = '<div class="no-results">No results found</div>';
        resultsContainer.style.display = 'block';
    }
}

function getIconForType(type) {
    const icons = {
        'project': 'project-diagram',
        'client': 'user-tie',
        'invoice': 'file-invoice'
    };
    return icons[type] || 'search';
}

// Notifications
function initializeNotifications() {
    // Mark notifications as read
    const markAllRead = document.querySelector('.mark-all-read');
    if (markAllRead) {
        markAllRead.addEventListener('click', function() {
            const unreadItems = document.querySelectorAll('.notification-item.unread');
            unreadItems.forEach(item => {
                item.classList.remove('unread');
            });

            // Update badge
            const badge = document.querySelector('.notification-badge');
            if (badge) {
                badge.textContent = '0';
                badge.style.display = 'none';
            }
        });
    }

    // Auto-refresh notifications every 30 seconds
    setInterval(refreshNotifications, 30000);
}

function refreshNotifications() {
    // In real implementation, this would fetch new notifications from API
    console.log('Refreshing notifications...');
}

// Add custom tooltip styles
const tooltipStyle = document.createElement('style');
tooltipStyle.textContent = `
    .custom-tooltip {
        position: absolute;
        background: rgba(0, 0, 0, 0.9);
        color: white;
        padding: 0.5rem 0.75rem;
        border-radius: 6px;
        font-size: 0.8rem;
        z-index: 10000;
        opacity: 0;
        transform: translateY(4px);
        transition: all 0.2s ease;
        pointer-events: none;
        white-space: nowrap;
    }

    .custom-tooltip.show {
        opacity: 1;
        transform: translateY(0);
    }

    .search-results {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid var(--admin-border);
        border-radius: var(--admin-radius);
        box-shadow: var(--admin-shadow-lg);
        max-height: 300px;
        overflow-y: auto;
        z-index: 1000;
        display: none;
    }

    .search-result-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem;
        border-bottom: 1px solid var(--admin-border);
        cursor: pointer;
        transition: var(--admin-transition);
    }

    .search-result-item:hover {
        background: var(--admin-bg);
    }

    .search-result-item:last-child {
        border-bottom: none;
    }

    .result-icon {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: var(--admin-primary);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 0.8rem;
    }

    .result-content {
        flex: 1;
    }

    .result-title {
        font-weight: 600;
        color: var(--admin-text);
        font-size: 0.875rem;
    }

    .result-type {
        font-size: 0.75rem;
        color: var(--admin-text-muted);
        text-transform: capitalize;
    }

    .no-results {
        padding: 1rem;
        text-align: center;
        color: var(--admin-text-muted);
        font-size: 0.875rem;
    }

    .refresh-btn.loading {
        opacity: 0.6;
        pointer-events: none;
    }
`;
document.head.appendChild(tooltipStyle);

// Clickable Elements
function initializeClickableElements() {
    // Make table rows clickable
    initializeTableRowClicks();

    // Make activity items clickable
    initializeActivityItemClicks();

    // Add loading states to KPI cards
    initializeKPICardClicks();

    // Add click analytics
    initializeClickAnalytics();
}

function initializeTableRowClicks() {
    // Project table rows
    const projectRows = document.querySelectorAll('.data-table-card:first-child .table-row');
    projectRows.forEach(row => {
        row.addEventListener('click', function() {
            const link = this.querySelector('a[href*="Projects"]');
            if (link) {
                addClickEffect(this);
                setTimeout(() => {
                    window.location.href = link.href;
                }, 200);
            }
        });
    });

    // Invoice table rows
    const invoiceRows = document.querySelectorAll('.data-table-card:last-child .table-row');
    invoiceRows.forEach(row => {
        row.addEventListener('click', function() {
            const link = this.querySelector('a[href*="Invoices"]');
            if (link) {
                addClickEffect(this);
                setTimeout(() => {
                    window.location.href = link.href;
                }, 200);
            }
        });
    });
}

function initializeActivityItemClicks() {
    const activityItems = document.querySelectorAll('.activity-item');
    activityItems.forEach(item => {
        item.addEventListener('click', function() {
            const projectLink = this.querySelector('a[href*="Projects"]');
            const clientText = this.querySelector('.activity-title').textContent;

            if (projectLink) {
                addClickEffect(this);
                setTimeout(() => {
                    window.location.href = projectLink.href;
                }, 200);
            } else if (clientText.includes('client')) {
                addClickEffect(this);
                setTimeout(() => {
                    window.location.href = '/Admin/Clients';
                }, 200);
            }
        });
    });
}

function initializeKPICardClicks() {
    const kpiLinks = document.querySelectorAll('.kpi-card-link');
    kpiLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const href = this.getAttribute('href');

            // Add loading state
            this.classList.add('loading');

            // Navigate after animation
            setTimeout(() => {
                window.location.href = href;
            }, 300);
        });
    });
}

function initializeClickAnalytics() {
    // Track clicks for analytics (optional)
    document.addEventListener('click', function(e) {
        const clickableElement = e.target.closest('.kpi-card-link, .quick-stat-link, .table-row, .activity-item');
        if (clickableElement) {
            // Log click for analytics
            const elementType = getElementType(clickableElement);
            const elementData = getElementData(clickableElement);

            console.log('Dashboard Click:', {
                type: elementType,
                data: elementData,
                timestamp: new Date().toISOString()
            });
        }
    });
}

function addClickEffect(element) {
    // Add ripple effect
    const ripple = document.createElement('div');
    ripple.className = 'click-ripple';
    element.appendChild(ripple);

    // Remove ripple after animation
    setTimeout(() => {
        if (ripple.parentNode) {
            ripple.parentNode.removeChild(ripple);
        }
    }, 600);

    // Add temporary highlight
    element.classList.add('clicked');
    setTimeout(() => {
        element.classList.remove('clicked');
    }, 200);
}

function getElementType(element) {
    if (element.classList.contains('kpi-card-link')) return 'kpi-card';
    if (element.classList.contains('quick-stat-link')) return 'quick-stat';
    if (element.classList.contains('table-row')) return 'table-row';
    if (element.classList.contains('activity-item')) return 'activity-item';
    return 'unknown';
}

function getElementData(element) {
    const titleElement = element.querySelector('.kpi-label, .stat-label, .row-title, .activity-title');
    const valueElement = element.querySelector('.kpi-value, .stat-number');

    return {
        title: titleElement ? titleElement.textContent.trim() : '',
        value: valueElement ? valueElement.textContent.trim() : '',
        href: element.href || element.querySelector('a')?.href || ''
    };
}

// Add click effect styles
const clickEffectStyle = document.createElement('style');
clickEffectStyle.textContent = `
    .click-ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(79, 70, 229, 0.3);
        transform: scale(0);
        animation: ripple 0.6s linear;
        pointer-events: none;
        top: 50%;
        left: 50%;
        width: 20px;
        height: 20px;
        margin-left: -10px;
        margin-top: -10px;
    }

    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }

    .clicked {
        background: rgba(79, 70, 229, 0.1) !important;
        transform: scale(0.98) !important;
    }

    .kpi-card-link.loading .kpi-overlay {
        opacity: 1 !important;
        background: rgba(79, 70, 229, 0.9) !important;
    }

    .kpi-card-link.loading .kpi-overlay::after {
        content: '';
        width: 24px;
        height: 24px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-top: 3px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-top: 0.5rem;
    }

    /* Enhanced hover states */
    .table-row:hover,
    .activity-item:hover {
        cursor: pointer;
    }

    .table-row:hover .row-title a,
    .activity-item:hover .activity-title {
        color: var(--admin-primary);
    }

    /* Keyboard navigation */
    .kpi-card-link:focus-visible,
    .quick-stat-link:focus-visible {
        outline: 2px solid var(--admin-primary);
        outline-offset: 2px;
    }

    .table-row:focus-visible,
    .activity-item:focus-visible {
        outline: 2px solid var(--admin-primary);
        outline-offset: 2px;
        border-radius: var(--admin-radius);
    }
`;
document.head.appendChild(clickEffectStyle);

// Make elements keyboard accessible
document.addEventListener('DOMContentLoaded', function() {
    // Add tabindex to clickable elements
    const clickableElements = document.querySelectorAll('.table-row, .activity-item');
    clickableElements.forEach((element, index) => {
        element.setAttribute('tabindex', '0');
        element.setAttribute('role', 'button');

        // Add keyboard event listeners
        element.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.click();
            }
        });
    });
});
