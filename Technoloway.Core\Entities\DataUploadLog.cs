using System.ComponentModel.DataAnnotations;
using Technoloway.Core.Common;

namespace Technoloway.Core.Entities;

public class DataUploadLog : BaseEntity
{
    [Required]
    [StringLength(255)]
    public string FileName { get; set; } = string.Empty;

    [Required]
    [StringLength(50)]
    public string FileType { get; set; } = string.Empty;

    public long FileSizeBytes { get; set; }

    [Required]
    [StringLength(100)]
    public string TargetEntity { get; set; } = string.Empty;

    [Required]
    [StringLength(50)]
    public string Operation { get; set; } = string.Empty; // Insert, Update, Upsert, Truncate

    [Required]
    [StringLength(50)]
    public string Status { get; set; } = string.Empty; // Processing, Completed, Failed, Rolled Back

    public int RecordsProcessed { get; set; }
    public int RecordsSuccessful { get; set; }
    public int RecordsFailed { get; set; }

    [StringLength(450)]
    public string UploadedByUserId { get; set; } = string.Empty;

    [StringLength(255)]
    public string UploadedByUserName { get; set; } = string.Empty;

    public DateTime StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }

    public string? ErrorDetails { get; set; }
    public string? ValidationErrors { get; set; }
    public string? FieldMappings { get; set; } // JSON string of field mappings

    [StringLength(500)]
    public string? BackupFilePath { get; set; } // For rollback purposes

    public bool CanRollback { get; set; } = false;
    public DateTime? RolledBackAt { get; set; }

    [StringLength(450)]
    public string? RolledBackByUserId { get; set; }
}
