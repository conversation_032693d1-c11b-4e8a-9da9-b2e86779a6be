{"ConnectionStrings": {"DefaultConnection": "#{ConnectionString}#"}, "Logging": {"LogLevel": {"Default": "Warning", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Error"}, "ApplicationInsights": {"LogLevel": {"Default": "Information"}}}, "AllowedHosts": "#{AllowedHosts}#", "Stripe": {"PublishableKey": "#{StripePublishableKey}#", "SecretKey": "#{<PERSON>e<PERSON><PERSON><PERSON><PERSON><PERSON>}#", "WebhookSecret": "#{StripeWebhookSecret}#"}, "GoogleMaps": {"ApiKey": "#{GoogleMapsApiKey}#"}, "Security": {"RequireEmailConfirmation": true, "EnableAccountLockout": true, "MaxFailedAccessAttempts": 5, "LockoutTimeSpan": "00:15:00", "PasswordRequireDigit": true, "PasswordRequireLowercase": true, "PasswordRequireUppercase": true, "PasswordRequireNonAlphanumeric": true, "PasswordRequiredLength": 12, "SessionTimeoutMinutes": 20, "RequireHttps": true, "EnableHsts": true, "HstsMaxAge": "365.00:00:00"}, "FileUpload": {"MaxFileSizeBytes": ********, "AllowedImageExtensions": [".jpg", ".jpeg", ".png", ".webp"], "AllowedDocumentExtensions": [".pdf"], "UploadPath": "#{UploadPath}#", "ScanForViruses": true, "EnableContentTypeValidation": true, "EnableFileSignatureValidation": true}, "Caching": {"DefaultCacheTimeMinutes": 30, "SlidingExpirationMinutes": 10, "AbsoluteExpirationMinutes": 60}, "RateLimiting": {"EnableRateLimiting": true, "RequestsPerMinute": 100, "BurstLimit": 200}}