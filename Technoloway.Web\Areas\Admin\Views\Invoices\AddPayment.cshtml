@model Technoloway.Core.Entities.Payment

@{
    ViewData["Title"] = "Add Payment";
    Layout = "_AdminLayout";
    var invoice = ViewBag.Invoice as Technoloway.Core.Entities.Invoice;
    var balance = ViewBag.Balance as decimal? ?? 0;
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Add Payment for Invoice #@(invoice != null ? invoice.InvoiceNumber : "")</h1>
    <div>
        <a asp-action="Payments" asp-route-id="@(invoice != null ? invoice.Id : 0)" class="btn btn-sm btn-warning shadow-sm">
            <i class="fas fa-money-bill-wave fa-sm text-white-50"></i> View Payments
        </a>
        <a asp-action="Details" asp-route-id="@(invoice != null ? invoice.Id : 0)" class="btn btn-sm btn-info shadow-sm">
            <i class="fas fa-eye fa-sm text-white-50"></i> View Invoice
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Payment Details</h6>
            </div>
            <div class="card-body">
                <form asp-action="AddPayment" method="post">
                    <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                    <input type="hidden" asp-for="InvoiceId" />

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="TransactionId" class="control-label">Transaction ID</label>
                                <input asp-for="TransactionId" class="form-control" placeholder="Leave blank for auto-generation" />
                                <span asp-validation-for="TransactionId" class="text-danger"></span>
                                <small class="form-text text-muted">If left blank, a transaction ID will be automatically generated.</small>
                            </div>

                            <div class="form-group mb-3">
                                <label asp-for="PaymentDate" class="control-label">Payment Date</label>
                                <input asp-for="PaymentDate" type="date" class="form-control" value="@DateTime.UtcNow.ToString("yyyy-MM-dd")" />
                                <span asp-validation-for="PaymentDate" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Amount" class="control-label">Amount</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input asp-for="Amount" type="number" step="0.01" min="0.01" class="form-control" required />
                                </div>
                                <span asp-validation-for="Amount" class="text-danger"></span>
                                <small class="form-text text-muted">Current balance: @balance.ToString("C")</small>
                            </div>

                            <div class="form-group mb-3">
                                <label asp-for="PaymentMethod" class="control-label">Payment Method</label>
                                <select asp-for="PaymentMethod" class="form-control" required>
                                    <option value="">-- Select Payment Method --</option>
                                    <option value="Credit Card">Credit Card</option>
                                    <option value="PayPal">PayPal</option>
                                    <option value="Bank Transfer">Bank Transfer</option>
                                    <option value="Cash">Cash</option>
                                    <option value="Check">Check</option>
                                    <option value="Other">Other</option>
                                </select>
                                <span asp-validation-for="PaymentMethod" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="Status" class="control-label">Status</label>
                        <select asp-for="Status" class="form-control">
                            <option value="Completed">Completed</option>
                            <option value="Pending">Pending</option>
                            <option value="Failed">Failed</option>
                            <option value="Refunded">Refunded</option>
                        </select>
                        <span asp-validation-for="Status" class="text-danger"></span>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="Notes" class="control-label">Notes</label>
                        <textarea asp-for="Notes" class="form-control" rows="3"></textarea>
                        <span asp-validation-for="Notes" class="text-danger"></span>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">Add Payment</button>
                        <a asp-action="Payments" asp-route-id="@(invoice != null ? invoice.Id : 0)" class="btn btn-secondary">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Invoice Summary</h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    <h4>Invoice #@(invoice != null ? invoice.InvoiceNumber : "")</h4>
                    <p>
                        @switch (invoice?.Status)
                        {
                            case "Pending":
                                <span class="badge bg-warning">Pending</span>
                                break;
                            case "Paid":
                                <span class="badge bg-success">Paid</span>
                                break;
                            case "Overdue":
                                <span class="badge bg-danger">Overdue</span>
                                break;
                            case "Cancelled":
                                <span class="badge bg-secondary">Cancelled</span>
                                break;
                            default:
                                <span class="badge bg-info">@invoice?.Status</span>
                                break;
                        }
                    </p>
                </div>

                <dl class="row">
                    <dt class="col-sm-6">Issue Date</dt>
                    <dd class="col-sm-6">@(invoice != null ? invoice.IssueDate.ToString("yyyy-MM-dd") : "-")</dd>

                    <dt class="col-sm-6">Due Date</dt>
                    <dd class="col-sm-6">@(invoice != null ? invoice.DueDate.ToString("yyyy-MM-dd") : "-")</dd>

                    <dt class="col-sm-6">Amount</dt>
                    <dd class="col-sm-6">@(invoice != null ? invoice.Amount.ToString("C") : "$0.00")</dd>

                    <dt class="col-sm-6">Tax</dt>
                    <dd class="col-sm-6">@(invoice != null ? invoice.TaxAmount.ToString("C") : "$0.00")</dd>

                    <dt class="col-sm-6">Total</dt>
                    <dd class="col-sm-6"><strong>@(invoice != null ? invoice.TotalAmount.ToString("C") : "$0.00")</strong></dd>

                    <dt class="col-sm-6">Balance</dt>
                    <dd class="col-sm-6"><strong class="@(balance <= 0 ? "text-success" : "text-danger")">@balance.ToString("C")</strong></dd>
                </dl>

                <div class="alert alert-info mt-3">
                    <p class="mb-0"><i class="fas fa-info-circle"></i> Adding a payment that fully covers the balance will automatically mark the invoice as "Paid".</p>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script>
        $(document).ready(function() {
            // Set the default amount to the remaining balance
            var balance = @balance;
            if (balance > 0) {
                $('#Amount').val(balance.toFixed(2));
            }

            // Warn if amount exceeds balance (with small tolerance for precision)
            $('#Amount').on('input', function() {
                var amount = parseFloat($(this).val()) || 0;
                if (amount > balance + 0.01) {
                    $(this).addClass('is-invalid');
                    $(this).next('.text-danger').remove();
                    $(this).after('<span class="text-danger">Amount exceeds the remaining balance.</span>');
                } else {
                    $(this).removeClass('is-invalid');
                    $(this).next('.text-danger').remove();
                }
            });
        });
    </script>
}
