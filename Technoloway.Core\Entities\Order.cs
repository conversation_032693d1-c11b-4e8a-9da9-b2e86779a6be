using Technoloway.Core.Common;
using System.ComponentModel.DataAnnotations;

namespace Technoloway.Core.Entities;

public class Order : BaseEntity
{
    [Required]
    public int OrderID { get; set; }
    
    [Required]
    [StringLength(40)]
    public string OrderTitle { get; set; } = string.Empty;
    
    [Required]
    public int ClientID { get; set; }
    
    [StringLength(15)]
    public string? OrderManager { get; set; }
    
    public string? OrderDesc { get; set; }
    
    public DateTime? OrderDate { get; set; }
    
    public decimal? OrderCost { get; set; }
    
    public int? OrderDiscountRate { get; set; }
    
    public decimal? OrderTotalDiscount { get; set; }
    
    [StringLength(10)]
    public string? Status { get; set; }
    
    public string? Notes { get; set; }
    
    // Navigation properties
    public Client Client { get; set; } = null!;
    public ICollection<OrderDetail> OrderDetails { get; set; } = new List<OrderDetail>();
    public ICollection<Project> Projects { get; set; } = new List<Project>();
    public ICollection<Contract> Contracts { get; set; } = new List<Contract>();
    public ICollection<Invoice> Invoices { get; set; } = new List<Invoice>();
}
