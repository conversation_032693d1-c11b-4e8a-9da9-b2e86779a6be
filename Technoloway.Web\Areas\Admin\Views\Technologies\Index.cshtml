@model IEnumerable<Technoloway.Core.Entities.Technology>

@{
    ViewData["Title"] = "Technologies";
    Layout = "_AdminLayout";
}

<div class="container-fluid p-4">
    <!-- Success Message -->
    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show mb-4" role="alert">
            <i class="fas fa-check-circle me-2"></i>@TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800 fw-bold">Technologies Management</h1>
            <p class="text-muted mb-0">Manage your technology stack and expertise showcase</p>
        </div>
        <div class="d-flex gap-2">
            <button class="btn-modern-admin secondary">
                <i class="fas fa-filter"></i>
                Filter
            </button>
            <button class="btn-modern-admin secondary">
                <i class="fas fa-sort"></i>
                Reorder
            </button>
            <a asp-action="Create" class="btn-modern-admin primary">
                <i class="fas fa-plus"></i>
                Add New Technology
            </a>
        </div>
    </div>

    <!-- Technologies Stats Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon me-3">
                        <i class="fas fa-microchip"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Total Technologies</p>
                        <h3 class="admin-stat-number">@Model.Count()</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card success h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon success me-3">
                        <i class="fas fa-image"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">With Icons</p>
                        <h3 class="admin-stat-number">@Model.Count(t => !string.IsNullOrEmpty(t.IconUrl))</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card info h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon info me-3">
                        <i class="fas fa-calendar-plus"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">This Month</p>
                        <h3 class="admin-stat-number">@Model.Count(t => t.CreatedAt.Month == DateTime.Now.Month && t.CreatedAt.Year == DateTime.Now.Year)</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="admin-stat-card warning h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon warning me-3">
                        <i class="fas fa-sort-numeric-up"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Avg. Order</p>
                        <h3 class="admin-stat-number">@(Model.Any() ? Math.Round(Model.Average(t => t.DisplayOrder), 0) : 0)</h3>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Technologies Management Card -->
    <div class="admin-card">
        <div class="card-header bg-white border-bottom d-flex align-items-center justify-content-between p-4">
            <div>
                <h5 class="mb-0 fw-bold text-gray-800">All Technologies</h5>
                <p class="text-muted mb-0 small">@Model.Count() total technologies</p>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-download me-1"></i>
                    Export
                </button>
                <button class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-sync me-1"></i>
                    Refresh
                </button>
            </div>
        </div>
        <div class="card-body p-0">
            @if (Model.Any())
            {
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="dataTable">
                        <thead>
                            <tr>
                                <th class="border-0 fw-semibold">Technology</th>
                                <th class="border-0 fw-semibold">Icon</th>
                                <th class="border-0 fw-semibold">Description</th>
                                <th class="border-0 fw-semibold">Order</th>
                                <th class="border-0 fw-semibold">Created</th>
                                <th class="border-0 fw-semibold">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model.OrderBy(t => t.DisplayOrder))
                            {
                                <tr>
                                    <td class="border-0">
                                        <div class="d-flex align-items-center">
                                            <div class="admin-stat-icon me-3" style="width: 40px; height: 40px; font-size: 1rem;">
                                                @if (!string.IsNullOrEmpty(item.IconUrl))
                                                {
                                                    <img src="@item.IconUrl" alt="@item.Name" style="width: 28px; height: 28px; object-fit: contain;" />
                                                }
                                                else
                                                {
                                                    <i class="fas fa-microchip text-white"></i>
                                                }
                                            </div>
                                            <div>
                                                <div class="fw-semibold text-gray-800">@item.Name</div>
                                                <div class="text-muted small">Technology Stack</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="border-0">
                                        @if (!string.IsNullOrEmpty(item.IconUrl))
                                        {
                                            <span class="badge bg-success">
                                                <i class="fas fa-image me-1"></i>Custom Icon
                                            </span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-light text-dark">
                                                <i class="fas fa-microchip me-1"></i>Default
                                            </span>
                                        }
                                    </td>
                                    <td class="border-0">
                                        <div class="text-muted" style="max-width: 300px;">
                                            @if (!string.IsNullOrEmpty(item.Description))
                                            {
                                                @if (item.Description.Length > 60)
                                                {
                                                    <span title="@item.Description">@(item.Description.Substring(0, 60))...</span>
                                                }
                                                else
                                                {
                                                    @item.Description
                                                }
                                            }
                                            else
                                            {
                                                <span class="text-muted fst-italic">No description</span>
                                            }
                                        </div>
                                    </td>
                                    <td class="border-0">
                                        <span class="badge bg-light text-dark">#@item.DisplayOrder</span>
                                    </td>
                                    <td class="border-0">
                                        <span class="text-muted">@item.CreatedAt.ToString("MMM dd, yyyy")</span>
                                    </td>
                                    <td class="border-0">
                                        <div class="d-flex gap-1">
                                            <a asp-action="Details" asp-route-id="@item.Id"
                                               class="btn btn-outline-info btn-sm" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a asp-action="Edit" asp-route-id="@item.Id"
                                               class="btn btn-outline-primary btn-sm" title="Edit Technology">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button class="btn btn-outline-secondary btn-sm" title="Move Up"
                                                    onclick="moveOrder(@item.Id, 'up')">
                                                <i class="fas fa-arrow-up"></i>
                                            </button>
                                            <button class="btn btn-outline-secondary btn-sm" title="Move Down"
                                                    onclick="moveOrder(@item.Id, 'down')">
                                                <i class="fas fa-arrow-down"></i>
                                            </button>
                                            <a asp-action="Delete" asp-route-id="@item.Id"
                                               class="btn btn-outline-danger btn-sm" title="Delete Technology"
                                               onclick="return confirm('Are you sure you want to delete this technology?')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
            else
            {
                <div class="text-center py-5">
                    <i class="fas fa-microchip text-muted mb-3" style="font-size: 3rem;"></i>
                    <h5 class="text-muted">No technologies found</h5>
                    <p class="text-muted">Add your first technology to showcase your expertise.</p>
                    <a asp-action="Create" class="btn-modern-admin primary">
                        <i class="fas fa-plus me-2"></i>
                        Add First Technology
                    </a>
                </div>
            }
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#dataTable').DataTable({
                "pageLength": 10,
                "responsive": true,
                "order": [[3, "asc"]], // Sort by Display Order by default
                "columnDefs": [
                    { "orderable": false, "targets": [0, 1, 5] } // Disable sorting for Technology, Icon, and Actions columns
                ]
            });
        });

        // Function to move technology order
        function moveOrder(technologyId, direction) {
            if (confirm(`Are you sure you want to move this technology ${direction}?`)) {
                // Here you would make an AJAX call to update the display order
                // For now, we'll just reload the page
                // $.post('/Admin/Technologies/MoveOrder', { id: technologyId, direction: direction })
                //     .done(function() {
                //         location.reload();
                //     });

                // Temporary: Just show an alert
                alert(`Technology moved ${direction} successfully! (This would normally update via AJAX)`);
            }
        }

        // Add hover effects to stat cards
        $('.admin-stat-card').hover(
            function() {
                $(this).addClass('shadow-lg');
            },
            function() {
                $(this).removeClass('shadow-lg');
            }
        );

        // Add technology statistics calculations
        function updateTechnologyStats() {
            const totalTechs = @Model.Count();
            const withIcons = @Model.Count(t => !string.IsNullOrEmpty(t.IconUrl));
            const thisMonth = @Model.Count(t => t.CreatedAt.Month == DateTime.Now.Month && t.CreatedAt.Year == DateTime.Now.Year);

            if (totalTechs > 0) {
                const iconPercentage = Math.round((withIcons / totalTechs) * 100);
                console.log(`Technologies with icons: ${iconPercentage}%`);
                console.log(`New technologies this month: ${thisMonth}`);
            }
        }

        updateTechnologyStats();

        // Add tooltip functionality for truncated descriptions
        $('[title]').tooltip();
    </script>
}
