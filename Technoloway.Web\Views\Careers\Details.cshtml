@model Technoloway.Core.Entities.JobListing

@{
    ViewData["Title"] = Model.Title;
    ViewData["MetaDescription"] = $"Apply for the {Model.Title} position at Technoloway. {Model.Description.Substring(0, <PERSON>.Min(Model.Description.Length, 150))}";
    ViewData["MetaKeywords"] = $"careers, jobs, {Model.Title.ToLower()}, employment, software development, tech jobs";
}

<!-- <PERSON>er -->
<div class="bg-dark text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-md-8">
                <h1 class="fw-bold">@Model.Title</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="/" class="text-white">Home</a></li>
                        <li class="breadcrumb-item"><a asp-action="Index" class="text-white">Careers</a></li>
                        <li class="breadcrumb-item active text-white" aria-current="page">@Model.Title</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- Job Details Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mb-4 mb-lg-0">
                <div class="card border-0 shadow mb-4">
                    <div class="card-body p-4">
                        <div class="d-flex flex-wrap mb-4">
                            <span class="badge bg-primary me-2 mb-2">@Model.EmploymentType</span>
                            <span class="badge bg-secondary me-2 mb-2"><i class="fas fa-map-marker-alt me-1"></i> @Model.Location</span>
                            @if (Model.IsRemote)
                            {
                                <span class="badge bg-success me-2 mb-2"><i class="fas fa-laptop-house me-1"></i> Remote</span>
                            }
                            @if (Model.SalaryMin.HasValue && Model.SalaryMax.HasValue)
                            {
                                <span class="badge bg-info text-dark me-2 mb-2">
                                    <i class="fas fa-money-bill-wave me-1"></i> 
                                    @Model.SalaryMin.Value.ToString("C0") - @Model.SalaryMax.Value.ToString("C0") @Model.SalaryCurrency
                                </span>
                            }
                            @if (Model.ExpiresAt.HasValue)
                            {
                                <span class="badge bg-warning text-dark me-2 mb-2">
                                    <i class="fas fa-calendar-alt me-1"></i> 
                                    Expires: @Model.ExpiresAt.Value.ToString("MMM dd, yyyy")
                                </span>
                            }
                        </div>
                        
                        <h3 class="fw-bold mb-3">Job Description</h3>
                        <div class="mb-4">
                            @Html.Raw(Model.Description.Replace("\n", "<br />"))
                        </div>
                        
                        <h3 class="fw-bold mb-3">Requirements</h3>
                        <div class="mb-4">
                            @Html.Raw(Model.Requirements.Replace("\n", "<br />"))
                        </div>
                        
                        <div class="text-center mt-4">
                            <a asp-action="Apply" asp-route-id="@Model.Id" class="btn btn-primary btn-lg">
                                <i class="fas fa-paper-plane me-2"></i> Apply Now
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="card border-0 shadow mb-4">
                    <div class="card-body p-4">
                        <h3 class="fw-bold mb-3">Job Summary</h3>
                        <ul class="list-unstyled">
                            <li class="mb-3">
                                <div class="d-flex">
                                    <div class="me-3 text-primary">
                                        <i class="fas fa-briefcase"></i>
                                    </div>
                                    <div>
                                        <h6 class="fw-bold">Job Type</h6>
                                        <p>@Model.EmploymentType</p>
                                    </div>
                                </div>
                            </li>
                            <li class="mb-3">
                                <div class="d-flex">
                                    <div class="me-3 text-primary">
                                        <i class="fas fa-map-marker-alt"></i>
                                    </div>
                                    <div>
                                        <h6 class="fw-bold">Location</h6>
                                        <p>@Model.Location @(Model.IsRemote ? "(Remote Available)" : "")</p>
                                    </div>
                                </div>
                            </li>
                            @if (Model.SalaryMin.HasValue && Model.SalaryMax.HasValue)
                            {
                                <li class="mb-3">
                                    <div class="d-flex">
                                        <div class="me-3 text-primary">
                                            <i class="fas fa-money-bill-wave"></i>
                                        </div>
                                        <div>
                                            <h6 class="fw-bold">Salary Range</h6>
                                            <p>@Model.SalaryMin.Value.ToString("C0") - @Model.SalaryMax.Value.ToString("C0") @Model.SalaryCurrency</p>
                                        </div>
                                    </div>
                                </li>
                            }
                            <li>
                                <div class="d-flex">
                                    <div class="me-3 text-primary">
                                        <i class="fas fa-calendar-alt"></i>
                                    </div>
                                    <div>
                                        <h6 class="fw-bold">Posted Date</h6>
                                        <p>@Model.CreatedAt.ToString("MMMM dd, yyyy")</p>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
                
                <div class="card border-0 shadow">
                    <div class="card-body p-4">
                        <h3 class="fw-bold mb-3">Share This Job</h3>
                        <div class="d-flex justify-content-center">
                            <a href="#" class="btn btn-outline-primary me-2" onclick="window.open('https://www.linkedin.com/sharing/share-offsite/?url=' + encodeURIComponent(window.location.href), '_blank'); return false;">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                            <a href="#" class="btn btn-outline-primary me-2" onclick="window.open('https://twitter.com/intent/tweet?url=' + encodeURIComponent(window.location.href) + '&text=' + encodeURIComponent('Check out this job opportunity: @Model.Title at Technoloway'), '_blank'); return false;">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="#" class="btn btn-outline-primary me-2" onclick="window.open('https://www.facebook.com/sharer/sharer.php?u=' + encodeURIComponent(window.location.href), '_blank'); return false;">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="#" class="btn btn-outline-primary" onclick="navigator.clipboard.writeText(window.location.href); alert('Link copied to clipboard!'); return false;">
                                <i class="fas fa-link"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Jobs Section -->
<section class="py-5 bg-light">
    <div class="container">
        <h2 class="fw-bold mb-4 text-center">Other Opportunities</h2>
        <div class="text-center">
            <a asp-action="Index" class="btn btn-primary btn-lg">
                <i class="fas fa-search me-2"></i> View All Job Openings
            </a>
        </div>
    </div>
</section>
