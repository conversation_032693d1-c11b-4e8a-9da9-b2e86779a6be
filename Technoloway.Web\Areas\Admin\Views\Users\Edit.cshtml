@model Technoloway.Web.Areas.Admin.Models.EditUserViewModel

@{
    ViewData["Title"] = "Edit User";
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Edit User</h1>
    <div>
        <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-info shadow-sm me-2">
            <i class="fas fa-eye fa-sm text-white-50"></i> View Details
        </a>
        <a asp-action="Index" class="btn btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Users
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-user-edit me-2"></i>Edit User: @Model.Email
                </h6>
            </div>
            <div class="card-body">
                <form asp-action="Edit" method="post">
                    <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                    <input type="hidden" asp-for="Id" />

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Email" class="form-label">Email Address *</label>
                                <input asp-for="Email" class="form-control" />
                                <span asp-validation-for="Email" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="UserName" class="form-label">Username *</label>
                                <input asp-for="UserName" class="form-control" readonly />
                                <span asp-validation-for="UserName" class="text-danger"></span>
                                <small class="form-text text-muted">Username is automatically set to email address.</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="PhoneNumber" class="form-label">Phone Number</label>
                                <input asp-for="PhoneNumber" class="form-control" placeholder="+1234567890" />
                                <span asp-validation-for="PhoneNumber" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <div class="form-check mt-4">
                                    <input asp-for="PhoneNumberConfirmed" class="form-check-input" type="checkbox" />
                                    <label asp-for="PhoneNumberConfirmed" class="form-check-label">
                                        Phone Number Confirmed
                                    </label>
                                    <span asp-validation-for="PhoneNumberConfirmed" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-check mb-3">
                                <input asp-for="EmailConfirmed" class="form-check-input" type="checkbox" />
                                <label asp-for="EmailConfirmed" class="form-check-label">
                                    Email Confirmed
                                </label>
                                <span asp-validation-for="EmailConfirmed" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check mb-3">
                                <input asp-for="TwoFactorEnabled" class="form-check-input" type="checkbox" />
                                <label asp-for="TwoFactorEnabled" class="form-check-label">
                                    Two Factor Authentication
                                </label>
                                <span asp-validation-for="TwoFactorEnabled" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check mb-3">
                                <input asp-for="LockoutEnabled" class="form-check-input" type="checkbox" />
                                <label asp-for="LockoutEnabled" class="form-check-label">
                                    Lockout Enabled
                                </label>
                                <span asp-validation-for="LockoutEnabled" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label class="form-label">User Roles</label>
                        <div class="row">
                            @foreach (var role in Model.AllRoles)
                            {
                                <div class="col-md-4 mb-2">
                                    <div class="form-check">
                                        <input type="checkbox" 
                                               name="SelectedRoles" 
                                               value="@role" 
                                               class="form-check-input" 
                                               id="role_@role"
                                               @(Model.SelectedRoles != null && Model.SelectedRoles.Contains(role) ? "checked" : "") />
                                        <label class="form-check-label" for="role_@role">
                                            @role
                                        </label>
                                    </div>
                                </div>
                            }
                        </div>
                        <small class="form-text text-muted">Select the roles for this user.</small>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Save Changes
                        </button>
                        <a asp-action="Index" class="btn btn-secondary ms-2">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                        <a asp-action="ResetPassword" asp-route-id="@Model.Id" class="btn btn-warning ms-2">
                            <i class="fas fa-key me-2"></i>Reset Password
                        </a>
                        <a asp-action="Delete" asp-route-id="@Model.Id" class="btn btn-danger ms-2">
                            <i class="fas fa-trash me-2"></i>Delete User
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-info">
                    <i class="fas fa-info-circle me-2"></i>User Information
                </h6>
            </div>
            <div class="card-body">
                <p><strong>User ID:</strong> @Model.Id</p>
                <p><strong>Current Roles:</strong></p>
                @if (Model.SelectedRoles != null && Model.SelectedRoles.Any())
                {
                    @foreach (var role in Model.SelectedRoles)
                    {
                        <span class="badge bg-primary me-1">@role</span>
                    }
                }
                else
                {
                    <span class="text-muted">No roles assigned</span>
                }
            </div>
        </div>

        <div class="card shadow mt-3">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>Important Notes
                </h6>
            </div>
            <div class="card-body">
                <ul class="small">
                    <li><strong>Email Changes:</strong> Will update the username automatically</li>
                    <li><strong>Role Changes:</strong> Take effect immediately</li>
                    <li><strong>Lockout:</strong> When enabled, user can be locked out after failed attempts</li>
                    <li><strong>Two Factor:</strong> Requires user to set up authenticator app</li>
                </ul>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        // Auto-update username when email changes
        document.getElementById('Email').addEventListener('input', function() {
            document.getElementById('UserName').value = this.value;
        });
    </script>
}
