@model List<Technoloway.Core.Entities.Feedback>
@{
    ViewData["Title"] = "My Feedback";
    Layout = "~/Areas/Client/Views/Shared/_ClientLayout.cshtml";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="mb-0">
                    <i class="fas fa-comments text-primary me-2"></i>
                    My Feedback
                </h2>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#feedbackModal">
                    <i class="fas fa-plus me-1"></i> Send New Feedback
                </button>
            </div>

            <!-- Feedback Statistics -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">@ViewBag.TotalFeedbacks</h4>
                                    <p class="mb-0">Total Feedback</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-comments fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">@ViewBag.PendingFeedbacks</h4>
                                    <p class="mb-0">Pending</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="mb-0">@ViewBag.ResolvedFeedbacks</h4>
                                    <p class="mb-0">Resolved</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Feedback List -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Feedback History</h5>
                </div>
                <div class="card-body">
                    @if (Model.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Subject</th>
                                        <th>Type</th>
                                        <th>Priority</th>
                                        <th>Status</th>
                                        <th>Rating</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var feedback in Model)
                                    {
                                        <tr>
                                            <td>@feedback.CreatedAt.ToString("MMM dd, yyyy")</td>
                                            <td>
                                                <strong>@feedback.Subject</strong>
                                                @if (feedback.Project != null)
                                                {
                                                    <br><small class="text-muted">Project: @feedback.Project.Name</small>
                                                }
                                            </td>
                                            <td>
                                                <span class="badge bg-info">@feedback.FeedbackType</span>
                                            </td>
                                            <td>
                                                @{
                                                    var priorityClass = feedback.Priority switch
                                                    {
                                                        "Critical" => "bg-danger",
                                                        "High" => "bg-warning",
                                                        "Medium" => "bg-primary",
                                                        "Low" => "bg-secondary",
                                                        _ => "bg-secondary"
                                                    };
                                                }
                                                <span class="badge @priorityClass">@feedback.Priority</span>
                                            </td>
                                            <td>
                                                @{
                                                    var statusClass = feedback.Status switch
                                                    {
                                                        "New" => "bg-info",
                                                        "In Review" => "bg-warning",
                                                        "In Progress" => "bg-primary",
                                                        "Resolved" => "bg-success",
                                                        "Closed" => "bg-secondary",
                                                        _ => "bg-secondary"
                                                    };
                                                }
                                                <span class="badge @statusClass">@feedback.Status</span>
                                            </td>
                                            <td>
                                                @if (feedback.Rating.HasValue)
                                                {
                                                    <div class="rating-display">
                                                        @for (int i = 1; i <= 5; i++)
                                                        {
                                                            <i class="fas fa-star @(i <= feedback.Rating ? "text-warning" : "text-muted")"></i>
                                                        }
                                                    </div>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">No rating</span>
                                                }
                                            </td>
                                            <td>
                                                <a href="@Url.Action("Details", new { id = feedback.Id })" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye"></i> View
                                                </a>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No feedback submitted yet</h5>
                            <p class="text-muted">Click the "Send New Feedback" button to share your thoughts with us.</p>
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#feedbackModal">
                                <i class="fas fa-plus me-1"></i> Send Your First Feedback
                            </button>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .rating-display {
        font-size: 0.9rem;
    }
    
    .card {
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border: 1px solid rgba(0, 0, 0, 0.125);
    }
    
    .table th {
        border-top: none;
        font-weight: 600;
        color: #495057;
    }
    
    .badge {
        font-size: 0.75rem;
    }
</style>
