using Microsoft.EntityFrameworkCore;
using Technoloway.Infrastructure.Data;
using Technoloway.Core.Entities;

namespace Technoloway.Web
{
    public class InsertServiceData
    {
        public static async Task ExecuteAsync(ApplicationDbContext context)
        {
            Console.WriteLine("Inserting comprehensive service data...");

            // Clear existing data to avoid conflicts
            await context.Database.ExecuteSqlRawAsync("DELETE FROM ServiceOptionFeatures");
            await context.Database.ExecuteSqlRawAsync("DELETE FROM ServiceOptions");
            await context.Database.ExecuteSqlRawAsync("DELETE FROM Services WHERE Id > 4"); // Keep the first 4 services

            // Get existing categories
            var webDevCategory = await context.Categories.FirstOrDefaultAsync(c => c.CategName == "Web Development");
            var mobileDevCategory = await context.Categories.FirstOrDefaultAsync(c => c.CategName == "Mobile App Development");
            var designCategory = await context.Categories.FirstOrDefaultAsync(c => c.CategName == "Design");
            var managementCategory = await context.Categories.FirstOrDefaultAsync(c => c.CategName == "Management");

            if (webDevCategory == null || mobileDevCategory == null || designCategory == null || managementCategory == null)
            {
                Console.WriteLine("Required categories not found. Please ensure categories are inserted first.");
                return;
            }

            // Insert additional services
            var services = new List<Service>
            {
                // Web Development Services
                new Service
                {
                    CategID = webDevCategory.Id,
                    Name = "E-commerce Website",
                    Description = "Complete e-commerce solution with payment integration, inventory management, and admin dashboard.",
                    IconClass = "fas fa-shopping-cart",
                    Price = 8500,
                    ServDiscountRate = 10,
                    ServTotalDiscount = 850,
                    ServManager = "John Smith",
                    IsActive = true,
                    DisplayOrder = 5
                },
                new Service
                {
                    CategID = webDevCategory.Id,
                    Name = "Corporate Website",
                    Description = "Professional corporate website with CMS, contact forms, and SEO optimization.",
                    IconClass = "fas fa-building",
                    Price = 4500,
                    ServDiscountRate = 5,
                    ServTotalDiscount = 225,
                    ServManager = "Sarah Johnson",
                    IsActive = true,
                    DisplayOrder = 6
                },
                new Service
                {
                    CategID = webDevCategory.Id,
                    Name = "Web Application",
                    Description = "Custom web application with user authentication, database integration, and API development.",
                    IconClass = "fas fa-code",
                    Price = 12000,
                    ServDiscountRate = 15,
                    ServTotalDiscount = 1800,
                    ServManager = "Mike Wilson",
                    IsActive = true,
                    DisplayOrder = 7
                },
                // Mobile Development Services
                new Service
                {
                    CategID = mobileDevCategory.Id,
                    Name = "iOS App Development",
                    Description = "Native iOS application development with App Store deployment and maintenance.",
                    IconClass = "fab fa-apple",
                    Price = 9500,
                    ServDiscountRate = 12,
                    ServTotalDiscount = 1140,
                    ServManager = "Emily Davis",
                    IsActive = true,
                    DisplayOrder = 8
                },
                new Service
                {
                    CategID = mobileDevCategory.Id,
                    Name = "Android App Development",
                    Description = "Native Android application development with Google Play Store deployment.",
                    IconClass = "fab fa-android",
                    Price = 8500,
                    ServDiscountRate = 10,
                    ServTotalDiscount = 850,
                    ServManager = "David Brown",
                    IsActive = true,
                    DisplayOrder = 9
                },
                // Design Services
                new Service
                {
                    CategID = designCategory.Id,
                    Name = "Brand Identity Design",
                    Description = "Complete brand identity package including logo, color palette, typography, and brand guidelines.",
                    IconClass = "fas fa-palette",
                    Price = 3500,
                    ServDiscountRate = 8,
                    ServTotalDiscount = 280,
                    ServManager = "Lisa Anderson",
                    IsActive = true,
                    DisplayOrder = 10
                },
                new Service
                {
                    CategID = designCategory.Id,
                    Name = "Website Redesign",
                    Description = "Complete website redesign with modern UI/UX principles and responsive design.",
                    IconClass = "fas fa-magic",
                    Price = 5500,
                    ServDiscountRate = 10,
                    ServTotalDiscount = 550,
                    ServManager = "Alex Thompson",
                    IsActive = true,
                    DisplayOrder = 11
                },
                // Management Services
                new Service
                {
                    CategID = managementCategory.Id,
                    Name = "Project Management",
                    Description = "End-to-end project management services with agile methodology and regular reporting.",
                    IconClass = "fas fa-tasks",
                    Price = 2500,
                    ServDiscountRate = 5,
                    ServTotalDiscount = 125,
                    ServManager = "Robert Garcia",
                    IsActive = true,
                    DisplayOrder = 12
                },
                new Service
                {
                    CategID = managementCategory.Id,
                    Name = "Digital Strategy Consulting",
                    Description = "Comprehensive digital strategy consulting to optimize your online presence and business processes.",
                    IconClass = "fas fa-chart-line",
                    Price = 4000,
                    ServDiscountRate = 7,
                    ServTotalDiscount = 280,
                    ServManager = "Jennifer Lee",
                    IsActive = true,
                    DisplayOrder = 13
                }
            };

            context.Services.AddRange(services);
            await context.SaveChangesAsync();
            Console.WriteLine($"Inserted {services.Count} services successfully.");

            // Now insert service options for each service
            await InsertServiceOptions(context);
            
            Console.WriteLine("Service data insertion completed successfully!");
        }

        private static async Task InsertServiceOptions(ApplicationDbContext context)
        {
            Console.WriteLine("Inserting service options...");

            // Get all services
            var services = await context.Services.ToListAsync();
            var serviceOptions = new List<ServiceOption>();

            foreach (var service in services)
            {
                switch (service.Name)
                {
                    case "E-commerce Website":
                        serviceOptions.AddRange(new[]
                        {
                            new ServiceOption
                            {
                                OptID = GetNextOptID(serviceOptions),
                                ServID = service.Id,
                                OptName = "Basic Package",
                                OptCost = 6500,
                                OptDiscountRate = 5,
                                OptTotalDiscount = 325,
                                OptAvailability = "Available",
                                OptDesc = "Basic e-commerce setup with 50 products, payment gateway, and basic admin panel."
                            },
                            new ServiceOption
                            {
                                OptID = GetNextOptID(serviceOptions),
                                ServID = service.Id,
                                OptName = "Premium Package",
                                OptCost = 8500,
                                OptDiscountRate = 10,
                                OptTotalDiscount = 850,
                                OptAvailability = "Available",
                                OptDesc = "Advanced e-commerce with unlimited products, multiple payment gateways, inventory management."
                            },
                            new ServiceOption
                            {
                                OptID = GetNextOptID(serviceOptions),
                                ServID = service.Id,
                                OptName = "Enterprise Package",
                                OptCost = 12500,
                                OptDiscountRate = 15,
                                OptTotalDiscount = 1875,
                                OptAvailability = "Available",
                                OptDesc = "Full-featured e-commerce with multi-vendor support, advanced analytics, and custom integrations."
                            }
                        });
                        break;

                    case "Corporate Website":
                        serviceOptions.AddRange(new[]
                        {
                            new ServiceOption
                            {
                                OptID = GetNextOptID(serviceOptions),
                                ServID = service.Id,
                                OptName = "Standard Package",
                                OptCost = 3500,
                                OptDiscountRate = 3,
                                OptTotalDiscount = 105,
                                OptAvailability = "Available",
                                OptDesc = "5-page corporate website with contact form and basic SEO."
                            },
                            new ServiceOption
                            {
                                OptID = GetNextOptID(serviceOptions),
                                ServID = service.Id,
                                OptName = "Professional Package",
                                OptCost = 4500,
                                OptDiscountRate = 5,
                                OptTotalDiscount = 225,
                                OptAvailability = "Available",
                                OptDesc = "10-page website with CMS, blog, advanced SEO, and social media integration."
                            },
                            new ServiceOption
                            {
                                OptID = GetNextOptID(serviceOptions),
                                ServID = service.Id,
                                OptName = "Premium Package",
                                OptCost = 6500,
                                OptDiscountRate = 8,
                                OptTotalDiscount = 520,
                                OptAvailability = "Available",
                                OptDesc = "Unlimited pages with advanced CMS, e-commerce integration, and custom features."
                            }
                        });
                        break;

                    case "Web Application":
                        serviceOptions.AddRange(new[]
                        {
                            new ServiceOption
                            {
                                OptID = GetNextOptID(serviceOptions),
                                ServID = service.Id,
                                OptName = "MVP Package",
                                OptCost = 8500,
                                OptDiscountRate = 10,
                                OptTotalDiscount = 850,
                                OptAvailability = "Available",
                                OptDesc = "Minimum viable product with core features and basic user management."
                            },
                            new ServiceOption
                            {
                                OptID = GetNextOptID(serviceOptions),
                                ServID = service.Id,
                                OptName = "Full-Featured Package",
                                OptCost = 12000,
                                OptDiscountRate = 15,
                                OptTotalDiscount = 1800,
                                OptAvailability = "Available",
                                OptDesc = "Complete web application with advanced features, API integration, and admin dashboard."
                            },
                            new ServiceOption
                            {
                                OptID = GetNextOptID(serviceOptions),
                                ServID = service.Id,
                                OptName = "Enterprise Package",
                                OptCost = 18000,
                                OptDiscountRate = 20,
                                OptTotalDiscount = 3600,
                                OptAvailability = "Available",
                                OptDesc = "Enterprise-grade application with scalability, security, and custom integrations."
                            }
                        });
                        break;
                }
            }

            context.ServiceOptions.AddRange(serviceOptions);
            await context.SaveChangesAsync();
            Console.WriteLine($"Inserted {serviceOptions.Count} service options successfully.");

            // Insert service option features
            await InsertServiceOptionFeatures(context, serviceOptions);
        }

        private static int GetNextOptID(List<ServiceOption> existingOptions)
        {
            return existingOptions.Count == 0 ? 1 : existingOptions.Max(o => o.OptID) + 1;
        }

        private static async Task InsertServiceOptionFeatures(ApplicationDbContext context, List<ServiceOption> serviceOptions)
        {
            Console.WriteLine("Inserting service option features...");

            var features = new List<ServiceOptionFeature>();

            // Load service options with their services
            var optionsWithServices = await context.ServiceOptions
                .Include(so => so.Service)
                .ToListAsync();

            foreach (var option in optionsWithServices)
            {
                switch (option.OptName)
                {
                    case "Basic Package":
                        if (option.Service?.Name == "E-commerce Website")
                        {
                            features.AddRange(new[]
                            {
                                new ServiceOptionFeature
                                {
                                    FeatID = GetNextFeatID(features),
                                    OptID = option.Id,
                                    FeatName = "Product Catalog",
                                    FeatCost = 1500,
                                    FeatDiscountRate = 5,
                                    FeatTotalDiscount = 75,
                                    FeatAvailability = "Included",
                                    FeatDesc = "Up to 50 products with images and descriptions"
                                },
                                new ServiceOptionFeature
                                {
                                    FeatID = GetNextFeatID(features),
                                    OptID = option.Id,
                                    FeatName = "Payment Gateway",
                                    FeatCost = 800,
                                    FeatDiscountRate = 3,
                                    FeatTotalDiscount = 24,
                                    FeatAvailability = "Included",
                                    FeatDesc = "Single payment gateway integration (PayPal or Stripe)"
                                },
                                new ServiceOptionFeature
                                {
                                    FeatID = GetNextFeatID(features),
                                    OptID = option.Id,
                                    FeatName = "Basic Admin Panel",
                                    FeatCost = 1200,
                                    FeatDiscountRate = 5,
                                    FeatTotalDiscount = 60,
                                    FeatAvailability = "Included",
                                    FeatDesc = "Order management and basic reporting"
                                }
                            });
                        }
                        break;

                    case "Premium Package":
                        if (option.Service?.Name == "E-commerce Website")
                        {
                            features.AddRange(new[]
                            {
                                new ServiceOptionFeature
                                {
                                    FeatID = GetNextFeatID(features),
                                    OptID = option.Id,
                                    FeatName = "Unlimited Products",
                                    FeatCost = 2000,
                                    FeatDiscountRate = 8,
                                    FeatTotalDiscount = 160,
                                    FeatAvailability = "Included",
                                    FeatDesc = "Unlimited product catalog with advanced categorization"
                                },
                                new ServiceOptionFeature
                                {
                                    FeatID = GetNextFeatID(features),
                                    OptID = option.Id,
                                    FeatName = "Multiple Payment Gateways",
                                    FeatCost = 1200,
                                    FeatDiscountRate = 10,
                                    FeatTotalDiscount = 120,
                                    FeatAvailability = "Included",
                                    FeatDesc = "Integration with PayPal, Stripe, and local payment methods"
                                },
                                new ServiceOptionFeature
                                {
                                    FeatID = GetNextFeatID(features),
                                    OptID = option.Id,
                                    FeatName = "Inventory Management",
                                    FeatCost = 1500,
                                    FeatDiscountRate = 10,
                                    FeatTotalDiscount = 150,
                                    FeatAvailability = "Included",
                                    FeatDesc = "Real-time inventory tracking and low stock alerts"
                                },
                                new ServiceOptionFeature
                                {
                                    FeatID = GetNextFeatID(features),
                                    OptID = option.Id,
                                    FeatName = "Advanced Analytics",
                                    FeatCost = 800,
                                    FeatDiscountRate = 5,
                                    FeatTotalDiscount = 40,
                                    FeatAvailability = "Included",
                                    FeatDesc = "Sales reports, customer analytics, and performance metrics"
                                }
                            });
                        }
                        break;

                    case "Enterprise Package":
                        if (option.Service?.Name == "E-commerce Website")
                        {
                            features.AddRange(new[]
                            {
                                new ServiceOptionFeature
                                {
                                    FeatID = GetNextFeatID(features),
                                    OptID = option.Id,
                                    FeatName = "Multi-Vendor Support",
                                    FeatCost = 3000,
                                    FeatDiscountRate = 15,
                                    FeatTotalDiscount = 450,
                                    FeatAvailability = "Included",
                                    FeatDesc = "Support for multiple vendors with commission management"
                                },
                                new ServiceOptionFeature
                                {
                                    FeatID = GetNextFeatID(features),
                                    OptID = option.Id,
                                    FeatName = "Advanced Analytics Dashboard",
                                    FeatCost = 1800,
                                    FeatDiscountRate = 12,
                                    FeatTotalDiscount = 216,
                                    FeatAvailability = "Included",
                                    FeatDesc = "Comprehensive analytics with custom reports and data visualization"
                                },
                                new ServiceOptionFeature
                                {
                                    FeatID = GetNextFeatID(features),
                                    OptID = option.Id,
                                    FeatName = "Custom API Integration",
                                    FeatCost = 2500,
                                    FeatDiscountRate = 15,
                                    FeatTotalDiscount = 375,
                                    FeatAvailability = "Included",
                                    FeatDesc = "Integration with third-party services and custom APIs"
                                },
                                new ServiceOptionFeature
                                {
                                    FeatID = GetNextFeatID(features),
                                    OptID = option.Id,
                                    FeatName = "Mobile App Integration",
                                    FeatCost = 2200,
                                    FeatDiscountRate = 10,
                                    FeatTotalDiscount = 220,
                                    FeatAvailability = "Included",
                                    FeatDesc = "API endpoints for mobile app integration"
                                }
                            });
                        }
                        break;

                    case "Standard Package":
                        if (option.Service?.Name == "Corporate Website")
                        {
                            features.AddRange(new[]
                            {
                                new ServiceOptionFeature
                                {
                                    FeatID = GetNextFeatID(features),
                                    OptID = option.Id,
                                    FeatName = "5-Page Website",
                                    FeatCost = 2000,
                                    FeatDiscountRate = 3,
                                    FeatTotalDiscount = 60,
                                    FeatAvailability = "Included",
                                    FeatDesc = "Home, About, Services, Contact, and one additional page"
                                },
                                new ServiceOptionFeature
                                {
                                    FeatID = GetNextFeatID(features),
                                    OptID = option.Id,
                                    FeatName = "Contact Form",
                                    FeatCost = 500,
                                    FeatDiscountRate = 2,
                                    FeatTotalDiscount = 10,
                                    FeatAvailability = "Included",
                                    FeatDesc = "Basic contact form with email notifications"
                                },
                                new ServiceOptionFeature
                                {
                                    FeatID = GetNextFeatID(features),
                                    OptID = option.Id,
                                    FeatName = "Basic SEO",
                                    FeatCost = 800,
                                    FeatDiscountRate = 5,
                                    FeatTotalDiscount = 40,
                                    FeatAvailability = "Included",
                                    FeatDesc = "Meta tags, basic keyword optimization, and sitemap"
                                }
                            });
                        }
                        break;

                    case "Professional Package":
                        if (option.Service?.Name == "Corporate Website")
                        {
                            features.AddRange(new[]
                            {
                                new ServiceOptionFeature
                                {
                                    FeatID = GetNextFeatID(features),
                                    OptID = option.Id,
                                    FeatName = "10-Page Website",
                                    FeatCost = 2800,
                                    FeatDiscountRate = 5,
                                    FeatTotalDiscount = 140,
                                    FeatAvailability = "Included",
                                    FeatDesc = "Comprehensive website with up to 10 pages"
                                },
                                new ServiceOptionFeature
                                {
                                    FeatID = GetNextFeatID(features),
                                    OptID = option.Id,
                                    FeatName = "Content Management System",
                                    FeatCost = 1200,
                                    FeatDiscountRate = 5,
                                    FeatTotalDiscount = 60,
                                    FeatAvailability = "Included",
                                    FeatDesc = "Easy-to-use CMS for content updates"
                                },
                                new ServiceOptionFeature
                                {
                                    FeatID = GetNextFeatID(features),
                                    OptID = option.Id,
                                    FeatName = "Blog Integration",
                                    FeatCost = 800,
                                    FeatDiscountRate = 3,
                                    FeatTotalDiscount = 24,
                                    FeatAvailability = "Included",
                                    FeatDesc = "Integrated blog system with categories and tags"
                                },
                                new ServiceOptionFeature
                                {
                                    FeatID = GetNextFeatID(features),
                                    OptID = option.Id,
                                    FeatName = "Advanced SEO",
                                    FeatCost = 1000,
                                    FeatDiscountRate = 8,
                                    FeatTotalDiscount = 80,
                                    FeatAvailability = "Included",
                                    FeatDesc = "Comprehensive SEO optimization and Google Analytics integration"
                                }
                            });
                        }
                        break;

                    case "MVP Package":
                        if (option.Service?.Name == "Web Application")
                        {
                            features.AddRange(new[]
                            {
                                new ServiceOptionFeature
                                {
                                    FeatID = GetNextFeatID(features),
                                    OptID = option.Id,
                                    FeatName = "User Authentication",
                                    FeatCost = 1500,
                                    FeatDiscountRate = 8,
                                    FeatTotalDiscount = 120,
                                    FeatAvailability = "Included",
                                    FeatDesc = "User registration, login, and basic profile management"
                                },
                                new ServiceOptionFeature
                                {
                                    FeatID = GetNextFeatID(features),
                                    OptID = option.Id,
                                    FeatName = "Core Features",
                                    FeatCost = 4000,
                                    FeatDiscountRate = 10,
                                    FeatTotalDiscount = 400,
                                    FeatAvailability = "Included",
                                    FeatDesc = "Essential application features and functionality"
                                },
                                new ServiceOptionFeature
                                {
                                    FeatID = GetNextFeatID(features),
                                    OptID = option.Id,
                                    FeatName = "Database Integration",
                                    FeatCost = 2000,
                                    FeatDiscountRate = 5,
                                    FeatTotalDiscount = 100,
                                    FeatAvailability = "Included",
                                    FeatDesc = "Database design and integration with CRUD operations"
                                }
                            });
                        }
                        break;

                    case "Full-Featured Package":
                        if (option.Service?.Name == "Web Application")
                        {
                            features.AddRange(new[]
                            {
                                new ServiceOptionFeature
                                {
                                    FeatID = GetNextFeatID(features),
                                    OptID = option.Id,
                                    FeatName = "Advanced User Management",
                                    FeatCost = 2500,
                                    FeatDiscountRate = 12,
                                    FeatTotalDiscount = 300,
                                    FeatAvailability = "Included",
                                    FeatDesc = "Role-based access control and user permissions"
                                },
                                new ServiceOptionFeature
                                {
                                    FeatID = GetNextFeatID(features),
                                    OptID = option.Id,
                                    FeatName = "API Development",
                                    FeatCost = 3000,
                                    FeatDiscountRate = 15,
                                    FeatTotalDiscount = 450,
                                    FeatAvailability = "Included",
                                    FeatDesc = "RESTful API development for third-party integrations"
                                },
                                new ServiceOptionFeature
                                {
                                    FeatID = GetNextFeatID(features),
                                    OptID = option.Id,
                                    FeatName = "Admin Dashboard",
                                    FeatCost = 2200,
                                    FeatDiscountRate = 10,
                                    FeatTotalDiscount = 220,
                                    FeatAvailability = "Included",
                                    FeatDesc = "Comprehensive admin panel with analytics and reporting"
                                },
                                new ServiceOptionFeature
                                {
                                    FeatID = GetNextFeatID(features),
                                    OptID = option.Id,
                                    FeatName = "Third-party Integrations",
                                    FeatCost = 1800,
                                    FeatDiscountRate = 8,
                                    FeatTotalDiscount = 144,
                                    FeatAvailability = "Included",
                                    FeatDesc = "Integration with popular third-party services and APIs"
                                }
                            });
                        }
                        break;
                }
            }

            context.ServiceOptionFeatures.AddRange(features);
            await context.SaveChangesAsync();
            Console.WriteLine($"Inserted {features.Count} service option features successfully.");
        }

        private static int GetNextFeatID(List<ServiceOptionFeature> existingFeatures)
        {
            return existingFeatures.Count == 0 ? 1 : existingFeatures.Max(f => f.FeatID) + 1;
        }
    }
}
