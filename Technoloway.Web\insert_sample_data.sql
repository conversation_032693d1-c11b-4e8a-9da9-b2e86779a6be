-- Insert Categories
INSERT INTO Categories (CategName, CategDesc, ParentID, CreatedAt, IsDeleted) VALUES
('Web Development', 'Complete web development solutions including frontend, backend, and full-stack development', NULL, datetime('now'), 0),
('Mobile App Development', 'Native and cross-platform mobile application development for iOS and Android', NULL, datetime('now'), 0),
('Cloud Services', 'Cloud infrastructure, deployment, and management services', NULL, datetime('now'), 0),
('Digital Marketing', 'SEO, social media marketing, and digital advertising solutions', NULL, datetime('now'), 0);

-- Insert Subcategories
INSERT INTO Categories (CategName, CategDesc, ParentID, CreatedAt, IsDeleted) VALUES
('Frontend Development', 'User interface and user experience development', 1, datetime('now'), 0),
('Backend Development', 'Server-side development and API creation', 1, datetime('now'), 0);

-- Insert Services
INSERT INTO Services (Name, Description, Price, CategID, ServManager, IconClass, IsActive, DisplayOrder, CreatedAt, IsDeleted) VALUES
('React Website Development', 'Modern, responsive websites built with React.js framework', 2500.00, 5, 'Frontend Team', 'fab fa-react', 1, 1, datetime('now'), 0),
('Node.js API Development', 'RESTful APIs and backend services using Node.js and Express', 3000.00, 6, 'Backend Team', 'fab fa-node-js', 1, 1, datetime('now'), 0),
('React Native App', 'Cross-platform mobile applications for iOS and Android', 5000.00, 2, 'Mobile Team', 'fas fa-mobile-alt', 1, 1, datetime('now'), 0),
('AWS Cloud Deployment', 'Deploy and manage applications on Amazon Web Services', 1500.00, 3, 'DevOps Team', 'fab fa-aws', 1, 1, datetime('now'), 0),
('SEO Optimization', 'Search engine optimization to improve website visibility', 800.00, 4, 'Marketing Team', 'fas fa-search', 1, 1, datetime('now'), 0);

-- Insert Service Options
INSERT INTO ServiceOptions (OptName, OptDesc, OptCost, OptAvailability, OptDiscountRate, ServID, CreatedAt, IsDeleted) VALUES
-- React Website Options
('Basic React Package', 'Standard React website with basic features', 2500.00, 'Available', 0, 1, datetime('now'), 0),
('Premium React Package', 'Advanced React website with premium features and animations', 4000.00, 'Available', 10, 1, datetime('now'), 0),
-- Node.js API Options
('Basic API Package', 'Simple REST API with basic CRUD operations', 3000.00, 'Available', 0, 2, datetime('now'), 0),
('Enterprise API Package', 'Advanced API with authentication, rate limiting, and monitoring', 5500.00, 'Available', 15, 2, datetime('now'), 0),
-- Mobile App Options
('Basic Mobile App', 'Simple mobile app with core functionality', 5000.00, 'Available', 0, 3, datetime('now'), 0),
('Advanced Mobile App', 'Feature-rich mobile app with advanced integrations', 8000.00, 'Available', 20, 3, datetime('now'), 0);

-- Insert Service Option Features
INSERT INTO ServiceOptionFeatures (FeatName, FeatDesc, FeatCost, FeatAvailability, FeatDiscountRate, OptID, CreatedAt, IsDeleted) VALUES
-- Basic React Package Features
('Responsive Design', 'Mobile-first responsive design that works on all devices', 0, 'Included', 0, 1, datetime('now'), 0),
('Basic SEO Setup', 'Meta tags, structured data, and basic SEO optimization', 200, 'Optional', 0, 1, datetime('now'), 0),
('Contact Form', 'Functional contact form with email integration', 150, 'Optional', 0, 1, datetime('now'), 0),
-- Premium React Package Features
('Advanced Animations', 'Smooth animations and transitions using Framer Motion', 0, 'Included', 0, 2, datetime('now'), 0),
('Content Management System', 'Easy-to-use CMS for content updates', 0, 'Included', 0, 2, datetime('now'), 0),
('Analytics Integration', 'Google Analytics and performance tracking', 100, 'Optional', 0, 2, datetime('now'), 0),
-- Basic API Package Features
('CRUD Operations', 'Create, Read, Update, Delete operations for all entities', 0, 'Included', 0, 3, datetime('now'), 0),
('API Documentation', 'Comprehensive API documentation with Swagger', 300, 'Optional', 0, 3, datetime('now'), 0),
('Unit Testing', 'Comprehensive unit tests for all API endpoints', 500, 'Optional', 10, 3, datetime('now'), 0),
-- Enterprise API Package Features
('JWT Authentication', 'Secure JWT-based authentication and authorization', 0, 'Included', 0, 4, datetime('now'), 0),
('Rate Limiting', 'API rate limiting to prevent abuse', 0, 'Included', 0, 4, datetime('now'), 0),
('Performance Monitoring', 'Real-time API performance monitoring and alerts', 200, 'Optional', 0, 4, datetime('now'), 0),
-- Basic Mobile App Features
('Native UI Components', 'Platform-specific UI components for iOS and Android', 0, 'Included', 0, 5, datetime('now'), 0),
('Offline Support', 'Basic offline functionality and data caching', 800, 'Optional', 0, 5, datetime('now'), 0),
('Push Notifications', 'Firebase push notifications for user engagement', 600, 'Optional', 15, 5, datetime('now'), 0),
-- Advanced Mobile App Features
('Advanced UI/UX', 'Custom animations, gestures, and advanced UI patterns', 0, 'Included', 0, 6, datetime('now'), 0),
('Third-party Integrations', 'Integration with social media, payment gateways, and APIs', 0, 'Included', 0, 6, datetime('now'), 0),
('Advanced Analytics', 'User behavior tracking and advanced analytics', 400, 'Optional', 0, 6, datetime('now'), 0);
