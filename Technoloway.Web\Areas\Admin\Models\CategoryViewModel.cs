using System.ComponentModel.DataAnnotations;
using Technoloway.Core.Entities;

namespace Technoloway.Web.Areas.Admin.Models
{
    public class CategoryViewModel
    {
        public int Id { get; set; }

        [Display(Name = "Parent Category")]
        public int? ParentID { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "Category Name")]
        public string CategName { get; set; } = string.Empty;

        [Display(Name = "Description")]
        public string? CategDesc { get; set; }

        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        // Convert from Entity to ViewModel
        public static CategoryViewModel FromEntity(Category category)
        {
            return new CategoryViewModel
            {
                Id = category.Id,
                ParentID = category.ParentID,
                CategName = category.CategName,
                CategDesc = category.CategDesc,
                CreatedAt = category.CreatedAt,
                UpdatedAt = category.UpdatedAt
            };
        }

        // Convert from ViewModel to Entity
        public Category ToEntity()
        {
            return new Category
            {
                Id = Id,
                ParentID = ParentID,
                CategName = CategName,
                CategDesc = CategDesc,
                CreatedAt = CreatedAt,
                UpdatedAt = UpdatedAt
            };
        }
    }
}
