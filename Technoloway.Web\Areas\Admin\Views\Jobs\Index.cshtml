@model Technoloway.Web.Areas.Admin.ViewModels.JobStatsViewModel

@{
    ViewData["Title"] = "Job Listings";
    Layout = "_AdminLayout";
}

<div class="container-fluid p-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800 fw-bold">Job Listings Management</h1>
            <p class="text-muted mb-0">Manage your career opportunities and job postings</p>
        </div>
        <div class="d-flex gap-2">
            <button class="btn-modern-admin secondary">
                <i class="fas fa-filter"></i>
                Filter
            </button>
            <button class="btn-modern-admin secondary">
                <i class="fas fa-chart-bar"></i>
                Analytics
            </button>
            <a asp-action="Create" class="btn-modern-admin primary">
                <i class="fas fa-plus"></i>
                Add New Job
            </a>
        </div>
    </div>

    <!-- Job Stats Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-2-4 col-lg-4 col-md-6">
            <div class="admin-stat-card h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon me-3">
                        <i class="fas fa-briefcase"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Total Jobs</p>
                        <h3 class="admin-stat-number">@Model.JobListings.Count()</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-2-4 col-lg-4 col-md-6">
            <div class="admin-stat-card success h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon success me-3">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Active Jobs</p>
                        <h3 class="admin-stat-number">@Model.JobListings.Count(j => j.IsActive)</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-2-4 col-lg-4 col-md-6">
            <div class="admin-stat-card info h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon info me-3">
                        <i class="fas fa-laptop-house"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Remote Jobs</p>
                        <h3 class="admin-stat-number">@Model.JobListings.Count(j => j.IsRemote)</h3>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-2-4 col-lg-4 col-md-6">
            <div class="admin-stat-card warning h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon warning me-3">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Expiring Soon</p>
                        <h3 class="admin-stat-number">@Model.JobListings.Count(j => j.ExpiresAt.HasValue && j.ExpiresAt <= DateTime.UtcNow.AddDays(7))</h3>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Applications Card -->
        <div class="col-xl-2-4 col-lg-4 col-md-6">
            <div class="admin-stat-card info h-100 p-4">
                <div class="d-flex align-items-center">
                    <div class="admin-stat-icon info me-3">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="flex-grow-1">
                        <p class="admin-stat-label mb-1">Total Applications</p>
                        <div class="d-flex align-items-baseline gap-2">
                            <h3 class="admin-stat-number mb-0">@Model.TotalApplications</h3>
                            <span class="text-muted small">@Model.PendingApplications pending</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Job Listings Management Card -->
    <div class="admin-card">
        <div class="card-header bg-white border-bottom d-flex align-items-center justify-content-between p-4">
            <div>
                <h5 class="mb-0 fw-bold text-gray-800">All Job Listings</h5>
                <p class="text-muted mb-0 small">@Model.JobListings.Count() total job listings</p>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-download me-1"></i>
                    Export
                </button>
                <button class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-sync me-1"></i>
                    Refresh
                </button>
            </div>
        </div>
        <div class="card-body p-0">
            @if (Model.JobListings.Any())
            {
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="dataTable">
                        <thead>
                            <tr>
                                <th class="border-0 fw-semibold">Job</th>
                                <th class="border-0 fw-semibold">Location</th>
                                <th class="border-0 fw-semibold">Type</th>
                                <th class="border-0 fw-semibold">Salary</th>
                                <th class="border-0 fw-semibold">Status</th>
                                <th class="border-0 fw-semibold">Applications</th>
                                <th class="border-0 fw-semibold">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var item in Model.JobListings)
                            {
                                <tr>
                                    <td class="border-0">
                                        <div class="d-flex align-items-center">
                                            <div class="admin-stat-icon me-3" style="width: 40px; height: 40px; font-size: 1rem;">
                                                <i class="fas fa-briefcase"></i>
                                            </div>
                                            <div>
                                                <div class="fw-semibold text-gray-800">@item.Title</div>
                                                <div class="text-muted small">@item.EmploymentType</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="border-0">
                                        @if (item.IsRemote)
                                        {
                                            <span class="badge bg-info">
                                                <i class="fas fa-laptop-house me-1"></i>Remote
                                            </span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-light text-dark">
                                                <i class="fas fa-map-marker-alt me-1"></i>@item.Location
                                            </span>
                                        }
                                    </td>
                                    <td class="border-0">
                                        <span class="badge bg-light text-dark">
                                            <i class="fas fa-clock me-1"></i>@item.EmploymentType
                                        </span>
                                    </td>
                                    <td class="border-0">
                                        @if (item.SalaryMin.HasValue && item.SalaryMax.HasValue)
                                        {
                                            <div class="fw-semibold">@item.SalaryMin.Value.ToString("C0") - @item.SalaryMax.Value.ToString("C0")</div>
                                            <div class="text-muted small">@item.SalaryCurrency</div>
                                        }
                                        else if (item.SalaryMin.HasValue)
                                        {
                                            <div class="fw-semibold">From @item.SalaryMin.Value.ToString("C0")</div>
                                            <div class="text-muted small">@item.SalaryCurrency</div>
                                        }
                                        else if (item.SalaryMax.HasValue)
                                        {
                                            <div class="fw-semibold">Up to @item.SalaryMax.Value.ToString("C0")</div>
                                            <div class="text-muted small">@item.SalaryCurrency</div>
                                        }
                                        else
                                        {
                                            <span class="text-muted fst-italic">Not specified</span>
                                        }
                                    </td>
                                    <td class="border-0">
                                        <div class="d-flex flex-column gap-1">
                                            @if (item.IsActive)
                                            {
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check me-1"></i>Active
                                                </span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-secondary">
                                                    <i class="fas fa-pause me-1"></i>Inactive
                                                </span>
                                            }
                                            @if (item.ExpiresAt.HasValue && item.ExpiresAt < DateTime.UtcNow)
                                            {
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-exclamation-triangle me-1"></i>Expired
                                                </span>
                                            }
                                            else if (item.ExpiresAt.HasValue && item.ExpiresAt <= DateTime.UtcNow.AddDays(7))
                                            {
                                                <span class="badge bg-warning">
                                                    <i class="fas fa-clock me-1"></i>Expiring Soon
                                                </span>
                                            }
                                        </div>
                                    </td>
                                    <td class="border-0">
                                        <div class="d-flex align-items-center justify-content-center">
                                            @{
                                                var applicationCount = Model.JobApplicationCounts.ContainsKey(item.Id) ? Model.JobApplicationCounts[item.Id] : 0;
                                            }
                                            <a asp-action="Applications" asp-route-id="@item.Id"
                                               class="btn btn-outline-info btn-sm"
                                               title="View @applicationCount application(s)">
                                                <i class="fas fa-users me-1"></i>
                                                Applications (@applicationCount)
                                            </a>
                                        </div>
                                    </td>
                                    <td class="border-0">
                                        <div class="d-flex gap-1">
                                            <a asp-action="Edit" asp-route-id="@item.Id"
                                               class="btn btn-outline-primary btn-sm" title="Edit Job">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a asp-action="Details" asp-route-id="@item.Id"
                                               class="btn btn-outline-info btn-sm" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @if (item.IsActive)
                                            {
                                                <a href="/Careers/Details/@item.Id" target="_blank"
                                                   class="btn btn-outline-success btn-sm" title="View Live">
                                                    <i class="fas fa-external-link-alt"></i>
                                                </a>
                                            }
                                            <a asp-action="Delete" asp-route-id="@item.Id"
                                               class="btn btn-outline-danger btn-sm" title="Delete Job"
                                               onclick="return confirm('Are you sure you want to delete this job listing?')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
            else
            {
                <div class="text-center py-5">
                    <i class="fas fa-briefcase text-muted mb-3" style="font-size: 3rem;"></i>
                    <h5 class="text-muted">No job listings found</h5>
                    <p class="text-muted">Create your first job listing to start hiring.</p>
                    <a asp-action="Create" class="btn-modern-admin primary">
                        <i class="fas fa-plus me-2"></i>
                        Create First Job
                    </a>
                </div>
            }
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#dataTable').DataTable({
                "pageLength": 10,
                "responsive": true,
                "order": [[5, "desc"]], // Sort by Applications column in descending order by default
                "columnDefs": [
                    { "orderable": false, "targets": [0, 6] }, // Disable sorting for Job and Actions columns
                    { "type": "num", "targets": [5] } // Treat Applications column as numeric
                ]
            });
        });

        // Add hover effects to stat cards
        $('.admin-stat-card').hover(
            function() {
                $(this).addClass('shadow-lg');
            },
            function() {
                $(this).removeClass('shadow-lg');
            }
        );

        // Add job statistics calculations
        function updateJobStats() {
            const totalJobs = @Model.JobListings.Count();
            const activeJobs = @Model.JobListings.Count(j => j.IsActive);
            const remoteJobs = @Model.JobListings.Count(j => j.IsRemote);
            const expiringSoon = @Model.JobListings.Count(j => j.ExpiresAt.HasValue && j.ExpiresAt <= DateTime.UtcNow.AddDays(7));

            // Application statistics
            const totalApplications = @Model.TotalApplications;
            const pendingApplications = @Model.PendingApplications;
            const reviewedApplications = @Model.ReviewedApplications;
            const hiredApplications = @Model.HiredApplications;
            const rejectedApplications = @Model.RejectedApplications;

            if (totalJobs > 0) {
                const activePercentage = Math.round((activeJobs / totalJobs) * 100);
                const remotePercentage = Math.round((remoteJobs / totalJobs) * 100);
                console.log(`Job Statistics:`);
                console.log(`Total Jobs: ${totalJobs}`);
                console.log(`Active jobs: ${activeJobs} (${activePercentage}%)`);
                console.log(`Remote jobs: ${remoteJobs} (${remotePercentage}%)`);
                console.log(`Jobs expiring soon: ${expiringSoon}`);
            }

            if (totalApplications > 0) {
                const pendingPercentage = Math.round((pendingApplications / totalApplications) * 100);
                const reviewedPercentage = Math.round((reviewedApplications / totalApplications) * 100);
                const hiredPercentage = Math.round((hiredApplications / totalApplications) * 100);
                const rejectedPercentage = Math.round((rejectedApplications / totalApplications) * 100);

                console.log(`Application Statistics:`);
                console.log(`Total Applications: ${totalApplications}`);
                console.log(`Pending: ${pendingApplications} (${pendingPercentage}%)`);
                console.log(`Reviewed: ${reviewedApplications} (${reviewedPercentage}%)`);
                console.log(`Hired: ${hiredApplications} (${hiredPercentage}%)`);
                console.log(`Rejected: ${rejectedApplications} (${rejectedPercentage}%)`);
            }
        }

        updateJobStats();

        // Enhanced confirmation for delete
        $('a[asp-action="Delete"]').click(function(e) {
            const jobTitle = $(this).closest('tr').find('.fw-semibold').text();
            if (!confirm(`Are you sure you want to delete the job listing "${jobTitle}"?`)) {
                e.preventDefault();
                return false;
            }
        });

        // Add status badge hover effects
        $('.badge').hover(
            function() {
                $(this).css('transform', 'scale(1.05)');
                $(this).css('transition', 'transform 0.2s ease');
            },
            function() {
                $(this).css('transform', 'scale(1)');
            }
        );

        // Add job icon hover effects
        $('.admin-stat-icon').hover(
            function() {
                $(this).css('transform', 'rotate(5deg) scale(1.1)');
                $(this).css('transition', 'transform 0.3s ease');
            },
            function() {
                $(this).css('transform', 'rotate(0deg) scale(1)');
            }
        );
    </script>
}

@section Styles {
    <style>
        /* Custom column class for 5 cards in a row */
        .col-xl-2-4 {
            flex: 0 0 auto;
            width: 20%; /* 100% / 5 = 20% */
        }

        @@media (max-width: 1199.98px) {
            .col-xl-2-4 {
                width: 25%; /* 4 cards per row on lg screens */
            }
        }

        @@media (max-width: 991.98px) {
            .col-xl-2-4 {
                width: 50%; /* 2 cards per row on md screens */
            }
        }

        @@media (max-width: 767.98px) {
            .col-xl-2-4 {
                width: 100%; /* 1 card per row on sm screens */
            }
        }

        /* Enhanced hover effects for application stats */
        .admin-stat-card.info:hover .admin-stat-number {
            color: #36d1dc;
            transition: all 0.3s ease;
        }

        .admin-stat-card.info:hover .text-muted.small {
            color: #5a5c69 !important;
            transform: scale(1.05);
            transition: all 0.3s ease;
        }

        /* Application button styling */
        .btn-outline-info.btn-sm {
            transition: all 0.2s ease;
            white-space: nowrap;
            min-width: 120px;
            font-weight: 500;
        }

        .btn-outline-info.btn-sm:hover {
            transform: scale(1.02);
            box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
        }

        /* Enhanced button text readability */
        .btn-outline-info.btn-sm i {
            opacity: 0.8;
        }

        .btn-outline-info.btn-sm:hover i {
            opacity: 1;
        }

        /* Action buttons consistent sizing */
        .action-btn {
            min-width: 40px !important;
            max-width: 40px !important;
            width: 40px !important;
            min-height: 32px !important;
            max-height: 32px !important;
            height: 32px !important;
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            padding: 0 !important;
            transition: all 0.2s ease;
            box-sizing: border-box;
        }

        .action-btn:hover {
            transform: scale(1.05);
        }

        /* Ensure all small buttons in actions column have same size */
        td:last-child .btn-sm {
            min-width: 40px !important;
            max-width: 40px !important;
            width: 40px !important;
            min-height: 32px !important;
            max-height: 32px !important;
            height: 32px !important;
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            padding: 0 !important;
            box-sizing: border-box;
        }
    </style>
}
