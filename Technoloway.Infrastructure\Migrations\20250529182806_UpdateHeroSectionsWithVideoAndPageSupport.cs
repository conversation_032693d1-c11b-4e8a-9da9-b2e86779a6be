﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Technoloway.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class UpdateHeroSectionsWithVideoAndPageSupport : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "ImageAlt",
                table: "HeroSlides",
                newName: "MediaAlt");

            migrationBuilder.AlterColumn<string>(
                name: "ImageUrl",
                table: "HeroSlides",
                type: "TEXT",
                maxLength: 500,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 500);

            migrationBuilder.AddColumn<string>(
                name: "MediaType",
                table: "HeroSlides",
                type: "TEXT",
                maxLength: 10,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<bool>(
                name: "VideoAutoPlay",
                table: "HeroSlides",
                type: "INTEGER",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "VideoControls",
                table: "HeroSlides",
                type: "INTEGER",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "VideoLoop",
                table: "HeroSlides",
                type: "INTEGER",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "VideoMuted",
                table: "HeroSlides",
                type: "INTEGER",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "VideoUrl",
                table: "HeroSlides",
                type: "TEXT",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PageName",
                table: "HeroSections",
                type: "TEXT",
                maxLength: 50,
                nullable: false,
                defaultValue: "");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "MediaType",
                table: "HeroSlides");

            migrationBuilder.DropColumn(
                name: "VideoAutoPlay",
                table: "HeroSlides");

            migrationBuilder.DropColumn(
                name: "VideoControls",
                table: "HeroSlides");

            migrationBuilder.DropColumn(
                name: "VideoLoop",
                table: "HeroSlides");

            migrationBuilder.DropColumn(
                name: "VideoMuted",
                table: "HeroSlides");

            migrationBuilder.DropColumn(
                name: "VideoUrl",
                table: "HeroSlides");

            migrationBuilder.DropColumn(
                name: "PageName",
                table: "HeroSections");

            migrationBuilder.RenameColumn(
                name: "MediaAlt",
                table: "HeroSlides",
                newName: "ImageAlt");

            migrationBuilder.AlterColumn<string>(
                name: "ImageUrl",
                table: "HeroSlides",
                type: "TEXT",
                maxLength: 500,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 500,
                oldNullable: true);
        }
    }
}
