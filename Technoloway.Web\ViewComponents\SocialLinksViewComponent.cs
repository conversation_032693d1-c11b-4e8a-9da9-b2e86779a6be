using Microsoft.AspNetCore.Mvc;
using Technoloway.Core.Entities;
using Technoloway.Core.Interfaces;

namespace Technoloway.Web.ViewComponents;

public class SocialLinksViewComponent : ViewComponent
{
    private readonly IRepository<SiteSetting> _siteSettingRepository;

    public SocialLinksViewComponent(IRepository<SiteSetting> siteSettingRepository)
    {
        _siteSettingRepository = siteSettingRepository;
    }

    public async Task<IViewComponentResult> InvokeAsync()
    {
        try
        {
            // Get social media and general site settings
            var socialSettings = await _siteSettingRepository.ListAsync(s => s.Group == "Social" && s.IsPublic);
            var generalSettings = await _siteSettingRepository.ListAsync(s => s.Group == "General" && s.IsPublic);

            var socialLinksInfo = new SocialLinksViewModel
            {
                SocialSettings = socialSettings.ToList(),
                SiteName = generalSettings.FirstOrDefault(s => s.Key == "SiteName")?.Value ?? "Technoloway",
                SiteTagline = generalSettings.FirstOrDefault(s => s.Key == "SiteTagline")?.Value ?? "Innovative Software Solutions"
            };

            return View(socialLinksInfo);
        }
        catch (Exception ex)
        {
            // Log error and return default values
            Console.WriteLine($"Error loading social links: {ex.Message}");

            var defaultSocialLinks = new SocialLinksViewModel
            {
                SocialSettings = new List<SiteSetting>(),
                SiteName = "Technoloway",
                SiteTagline = "Innovative Software Solutions"
            };

            return View(defaultSocialLinks);
        }
    }
}

public class SocialLinksViewModel
{
    public List<SiteSetting> SocialSettings { get; set; } = new List<SiteSetting>();
    public string SiteName { get; set; } = string.Empty;
    public string SiteTagline { get; set; } = string.Empty;
}
