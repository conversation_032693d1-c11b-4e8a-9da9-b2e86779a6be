using System.ComponentModel.DataAnnotations;
using Technoloway.Core.Entities;

namespace Technoloway.Web.Areas.Admin.Models
{
    public class ServiceOptionFeatureViewModel
    {
        public int Id { get; set; }

        [Required]
        [Display(Name = "Feature ID")]
        public int FeatID { get; set; }

        [Required]
        [Display(Name = "Option ID")]
        public int OptID { get; set; }

        // Helper property for UI
        public int ServiceOptionId { get; set; }

        [Required]
        [StringLength(50)]
        [Display(Name = "Feature Name")]
        public string FeatName { get; set; } = string.Empty;

        [Display(Name = "Cost")]
        [Range(0, double.MaxValue, ErrorMessage = "Cost must be a positive number")]
        public decimal? FeatCost { get; set; }

        [Display(Name = "Discount Rate (%)")]
        [Range(0, 100, ErrorMessage = "Discount rate must be between 0 and 100")]
        public int? FeatDiscountRate { get; set; }

        [Display(Name = "Total Discount")]
        [Range(0, double.MaxValue, ErrorMessage = "Total discount must be a positive number")]
        public decimal? FeatTotalDiscount { get; set; }

        [StringLength(12)]
        [Display(Name = "Availability")]
        public string? FeatAvailability { get; set; }

        [Display(Name = "Description")]
        public string? FeatDesc { get; set; }

        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        // Convert from Entity to ViewModel
        public static ServiceOptionFeatureViewModel FromEntity(ServiceOptionFeature feature)
        {
            return new ServiceOptionFeatureViewModel
            {
                Id = feature.Id,
                FeatID = feature.FeatID,
                OptID = feature.OptID,
                FeatName = feature.FeatName,
                FeatCost = feature.FeatCost,
                FeatDiscountRate = feature.FeatDiscountRate,
                FeatTotalDiscount = feature.FeatTotalDiscount,
                FeatAvailability = feature.FeatAvailability,
                FeatDesc = feature.FeatDesc,
                CreatedAt = feature.CreatedAt,
                UpdatedAt = feature.UpdatedAt
            };
        }

        // Convert from ViewModel to Entity
        public ServiceOptionFeature ToEntity()
        {
            return new ServiceOptionFeature
            {
                Id = Id,
                FeatID = FeatID,
                OptID = OptID,
                FeatName = FeatName,
                FeatCost = FeatCost,
                FeatDiscountRate = FeatDiscountRate,
                FeatTotalDiscount = FeatTotalDiscount,
                FeatAvailability = FeatAvailability,
                FeatDesc = FeatDesc,
                CreatedAt = CreatedAt,
                UpdatedAt = UpdatedAt
            };
        }
    }
}
