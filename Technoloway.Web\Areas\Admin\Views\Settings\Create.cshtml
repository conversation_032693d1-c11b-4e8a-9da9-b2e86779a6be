@model Technoloway.Web.Areas.Admin.Models.SiteSettingViewModel

@{
    ViewData["Title"] = "Create Setting";
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Create New Setting</h1>
    <a asp-action="Index" class="btn btn-secondary shadow-sm">
        <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Settings
    </a>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-plus me-2"></i>New Site Setting
                </h6>
            </div>
            <div class="card-body">
                <form asp-action="Create" method="post" enctype="multipart/form-data">
                    <div asp-validation-summary="ModelOnly" class="text-danger"></div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Key" class="form-label">Setting Key *</label>
                                <input asp-for="Key" class="form-control" placeholder="e.g., SiteName, CompanyEmail" />
                                <span asp-validation-for="Key" class="text-danger"></span>
                                <small class="form-text text-muted">Unique identifier for this setting (no spaces).</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label asp-for="Group" class="form-label">Group *</label>
                                <select asp-for="Group" class="form-control">
                                    <option value="">Select a group...</option>
                                    <option value="General">General</option>
                                    <option value="Contact">Contact</option>
                                    <option value="Social">Social</option>
                                    <option value="QuickLinks">QuickLinks</option>
                                    <option value="Integration">Integration</option>
                                    <option value="Payment">Payment</option>
                                    <option value="SEO">SEO</option>
                                    <option value="Email">Email</option>
                                    <option value="Security">Security</option>
                                </select>
                                <span asp-validation-for="Group" class="text-danger"></span>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="Value" class="form-label">Value</label>
                        <textarea asp-for="Value" class="form-control" rows="3" placeholder="Enter the setting value"></textarea>
                        <span asp-validation-for="Value" class="text-danger"></span>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="Description" class="form-label">Description</label>
                        <textarea asp-for="Description" class="form-control" rows="2" placeholder="Brief description of what this setting controls"></textarea>
                        <span asp-validation-for="Description" class="text-danger"></span>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="IconFile" class="form-label">Setting Icon</label>
                        <div class="input-group">
                            <span class="input-group-text" id="iconPreview">
                                <i class="fas fa-icons" title="Icon Preview"></i>
                            </span>
                            <input asp-for="IconFile" class="form-control" type="file" accept="image/*" />
                        </div>
                        <span asp-validation-for="IconFile" class="text-danger"></span>
                        <small class="form-text text-muted">
                            Upload a setting icon image. Supported formats: JPG, PNG, GIF, WebP. Maximum size: 5MB. Recommended size: 32x32px or 64x64px.
                        </small>

                        <div id="icon-preview" class="mt-2" style="display: none;">
                            <small class="text-muted">Icon preview:</small><br>
                            <img id="preview-img" style="height: 32px; width: 32px; object-fit: contain; border: 1px solid #ddd; border-radius: 4px; padding: 4px;" />
                        </div>

                        <input type="hidden" asp-for="Icon" />
                    </div>

                    <div class="form-check mb-3">
                        <input asp-for="IsPublic" class="form-check-input" type="checkbox" checked />
                        <label asp-for="IsPublic" class="form-check-label">
                            Is Public Setting
                        </label>
                        <small class="form-text text-muted d-block">
                            Public settings can be accessed by the frontend. Private settings are for admin use only.
                        </small>
                        <span asp-validation-for="IsPublic" class="text-danger"></span>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Create Setting
                        </button>
                        <a asp-action="Index" class="btn btn-secondary ms-2">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-info">
                    <i class="fas fa-lightbulb me-2"></i>Tips
                </h6>
            </div>
            <div class="card-body">
                <h6>Setting Key Guidelines:</h6>
                <ul class="small">
                    <li>Use PascalCase (e.g., SiteName)</li>
                    <li>No spaces or special characters</li>
                    <li>Be descriptive and unique</li>
                </ul>

                <h6 class="mt-3">Common Groups:</h6>
                <ul class="small">
                    <li><strong>General:</strong> Site name, tagline, etc.</li>
                    <li><strong>Contact:</strong> Address, phone, email</li>
                    <li><strong>Social:</strong> Social media URLs</li>
                    <li><strong>QuickLinks:</strong> Footer navigation links</li>
                    <li><strong>Integration:</strong> API keys, external services</li>
                    <li><strong>Payment:</strong> Payment gateway settings</li>
                </ul>

                <div class="alert alert-warning mt-3">
                    <small><i class="fas fa-exclamation-triangle me-1"></i>
                    Sensitive data like API keys should be marked as private.</small>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        $(document).ready(function() {
            console.log("Create page JavaScript loaded");

            // Debug form submission
            $('form').on('submit', function(e) {
                console.log("=== FORM SUBMISSION DEBUG ===");
                console.log("Form is being submitted");

                // Log form data
                var formData = new FormData(this);
                console.log("Form data:");
                for (var pair of formData.entries()) {
                    console.log(pair[0] + ': ' + pair[1]);
                }

                // Check required fields
                var key = $('#Key').val();
                var group = $('#Group').val();
                console.log("Key:", key);
                console.log("Group:", group);

                if (!key || !group) {
                    console.log("Missing required fields!");
                    alert("Please fill in all required fields (Key and Group)");
                    e.preventDefault();
                    return false;
                }

                console.log("Form validation passed, submitting...");
            });

            // File upload preview
            $('#IconFile').on('change', function() {
                var file = this.files[0];
                var preview = $('#icon-preview');
                var previewImg = $('#preview-img');
                var inputGroupIcon = $('#iconPreview');

                if (file) {
                    // Validate file type
                    var validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
                    if (!validTypes.includes(file.type)) {
                        alert('Please select a valid image file (JPG, PNG, GIF, WebP)');
                        $(this).val('');
                        preview.hide();
                        inputGroupIcon.html('<i class="fas fa-icons" title="Icon Preview"></i>');
                        return;
                    }

                    // Validate file size (5MB)
                    if (file.size > 5 * 1024 * 1024) {
                        alert('File size must be less than 5MB');
                        $(this).val('');
                        preview.hide();
                        inputGroupIcon.html('<i class="fas fa-icons" title="Icon Preview"></i>');
                        return;
                    }

                    // Show preview
                    var reader = new FileReader();
                    reader.onload = function(e) {
                        previewImg.attr('src', e.target.result);
                        preview.show();
                        // Update input group icon
                        inputGroupIcon.html('<img src="' + e.target.result + '" style="height: 20px; width: 20px; object-fit: contain;" title="Selected Icon" />');
                    };
                    reader.readAsDataURL(file);
                } else {
                    preview.hide();
                    inputGroupIcon.html('<i class="fas fa-icons" title="Icon Preview"></i>');
                }
            });

            // Drag and drop functionality for file upload
            var dropArea = $('#IconFile').parent();

            dropArea.on('dragover', function(e) {
                e.preventDefault();
                $(this).addClass('border-primary');
            });

            dropArea.on('dragleave', function(e) {
                e.preventDefault();
                $(this).removeClass('border-primary');
            });

            dropArea.on('drop', function(e) {
                e.preventDefault();
                $(this).removeClass('border-primary');

                var files = e.originalEvent.dataTransfer.files;
                if (files.length > 0) {
                    $('#IconFile')[0].files = files;
                    $('#IconFile').trigger('change');
                }
            });
        });
    </script>
}
