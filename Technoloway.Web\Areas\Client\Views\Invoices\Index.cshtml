@model IEnumerable<Technoloway.Core.Entities.Invoice>
@{
    ViewData["Title"] = "My Invoices";
    var selectedStatus = ViewBag.SelectedStatus as string;
    var totalInvoices = ViewBag.TotalInvoices as int? ?? 0;
    var pendingInvoices = ViewBag.PendingInvoices as int? ?? 0;
    var paidInvoices = ViewBag.PaidInvoices as int? ?? 0;
    var overdueInvoices = ViewBag.OverdueInvoices as int? ?? 0;
    var totalAmount = ViewBag.TotalAmount as decimal? ?? 0;
    var paidAmount = ViewBag.PaidAmount as decimal? ?? 0;
    var pendingAmount = ViewBag.PendingAmount as decimal? ?? 0;
}

<div class="container-fluid p-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="text-gradient mb-2">
                        <i class="fas fa-file-invoice-dollar me-2"></i>My Invoices
                    </h1>
                    <p class="text-muted">Manage your invoices and payments</p>
                </div>
                <div class="d-flex gap-2">
                    <a asp-action="Payments" class="btn btn-outline-primary">
                        <i class="fas fa-credit-card me-1"></i> Payment History
                    </a>
                    <button class="btn btn-outline-secondary" onclick="window.print()">
                        <i class="fas fa-print me-1"></i> Print
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="text-primary mb-2">
                        <i class="fas fa-file-invoice fa-2x"></i>
                    </div>
                    <h3 class="mb-1">@totalInvoices</h3>
                    <p class="text-muted mb-0">Total Invoices</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="text-warning mb-2">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                    <h3 class="mb-1">@pendingInvoices</h3>
                    <p class="text-muted mb-0">Pending</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="text-success mb-2">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                    <h3 class="mb-1">@paidInvoices</h3>
                    <p class="text-muted mb-0">Paid</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="text-danger mb-2">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                    <h3 class="mb-1">@overdueInvoices</h3>
                    <p class="text-muted mb-0">Overdue</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Amount Summary -->
    <div class="row mb-4">
        <div class="col-md-4 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <h4 class="text-primary mb-1">$@totalAmount.ToString("N2")</h4>
                    <p class="text-muted mb-0">Total Amount</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <h4 class="text-success mb-1">$@paidAmount.ToString("N2")</h4>
                    <p class="text-muted mb-0">Paid Amount</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <h4 class="text-warning mb-1">$@pendingAmount.ToString("N2")</h4>
                    <p class="text-muted mb-0">Pending Amount</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Tabs -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <ul class="nav nav-pills justify-content-center">
                        <li class="nav-item">
                            <a class="nav-link @(string.IsNullOrEmpty(selectedStatus) ? "active" : "")"
                               asp-action="Index">All Invoices</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link @(selectedStatus == "Pending" ? "active" : "")"
                               asp-action="Index" asp-route-status="Pending">Pending</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link @(selectedStatus == "Paid" ? "active" : "")"
                               asp-action="Index" asp-route-status="Paid">Paid</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link @(selectedStatus == "Overdue" ? "active" : "")"
                               asp-action="Index" asp-route-status="Overdue">Overdue</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Invoices List -->
    <div class="row">
        <div class="col-12">
            @if (Model.Any())
            {
                <div class="card border-0 shadow-sm">
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Invoice #</th>
                                        <th>Project</th>
                                        <th>Issue Date</th>
                                        <th>Due Date</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var invoice in Model)
                                    {
                                        <tr>
                                            <td>
                                                <strong>@invoice.InvoiceNumber</strong>
                                            </td>
                                            <td>
                                                @if (invoice.Order != null)
                                                {
                                                    <a asp-controller="Projects" asp-action="Details" asp-route-id="@invoice.OrderID"
                                                       class="text-decoration-none">
                                                        @invoice.Order.OrderTitle
                                                    </a>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">No Project</span>
                                                }
                                            </td>
                                            <td>@invoice.IssueDate.ToString("MMM dd, yyyy")</td>
                                            <td>
                                                @invoice.DueDate.ToString("MMM dd, yyyy")
                                                @if (invoice.DueDate < DateTime.Now && invoice.Status != "Paid")
                                                {
                                                    <br><small class="text-danger">Overdue</small>
                                                }
                                            </td>
                                            <td>
                                                <strong>$@invoice.TotalAmount.ToString("N2")</strong>
                                            </td>
                                            <td>
                                                @switch (invoice.Status)
                                                {
                                                    case "Paid":
                                                        <span class="badge bg-success">Paid</span>
                                                        break;
                                                    case "Pending":
                                                        <span class="badge bg-warning">Pending</span>
                                                        break;
                                                    case "Overdue":
                                                        <span class="badge bg-danger">Overdue</span>
                                                        break;
                                                    default:
                                                        <span class="badge bg-secondary">@invoice.Status</span>
                                                        break;
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group">
                                                    <a asp-action="Details" asp-route-id="@invoice.Id"
                                                       class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a asp-action="Download" asp-route-id="@invoice.Id"
                                                       class="btn btn-sm btn-outline-secondary">
                                                        <i class="fas fa-download"></i>
                                                    </a>
                                                    @if (invoice.Status != "Paid")
                                                    {
                                                        <button type="button" class="btn btn-sm btn-outline-success"
                                                                onclick="showPaymentModal(@invoice.Id, '@invoice.InvoiceNumber', @invoice.TotalAmount)">
                                                            <i class="fas fa-credit-card"></i>
                                                        </button>
                                                    }
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            }
            else
            {
                <div class="card border-0 shadow-sm">
                    <div class="card-body text-center py-5">
                        <div class="mb-4">
                            <i class="fas fa-file-invoice fa-4x text-muted"></i>
                        </div>
                        <h4 class="text-muted mb-3">No Invoices Found</h4>
                        @if (!string.IsNullOrEmpty(selectedStatus))
                        {
                            <p class="text-muted mb-4">No invoices found with status "@selectedStatus".</p>
                            <a asp-action="Index" class="btn btn-outline-primary">
                                <i class="fas fa-list me-1"></i> View All Invoices
                            </a>
                        }
                        else
                        {
                            <p class="text-muted mb-4">You don't have any invoices yet. Invoices will appear here when they are created for your projects.</p>
                            <a asp-controller="Projects" asp-action="Index" class="btn btn-primary">
                                <i class="fas fa-project-diagram me-1"></i> View Projects
                            </a>
                        }
                    </div>
                </div>
            }
        </div>
    </div>
</div>

<!-- Payment Modal -->
<div class="modal fade" id="paymentModal" tabindex="-1" aria-labelledby="paymentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="paymentModalLabel">Make Payment</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form asp-action="PayInvoice" method="post" id="paymentForm">
                @Html.AntiForgeryToken()
                <div class="modal-body">
                    <input type="hidden" id="invoiceId" name="id" />

                    <div class="mb-3">
                        <label class="form-label">Invoice Number</label>
                        <input type="text" class="form-control" id="invoiceNumber" readonly />
                    </div>

                    <div class="mb-3">
                        <label for="amount" class="form-label">Payment Amount</label>
                        <div class="input-group">
                            <span class="input-group-text">$</span>
                            <input type="number" class="form-control" id="amount" name="amount" step="0.01" min="0.01" required />
                        </div>
                        <div class="form-text">Maximum amount: $<span id="maxAmount"></span></div>
                    </div>

                    <div class="mb-3">
                        <label for="paymentMethod" class="form-label">Payment Method</label>
                        <select class="form-select" id="paymentMethod" name="paymentMethod" required>
                            <option value="">Select Payment Method</option>
                            <option value="Credit Card">Credit Card</option>
                            <option value="Bank Transfer">Bank Transfer</option>
                            <option value="PayPal">PayPal</option>
                            <option value="Check">Check</option>
                        </select>
                    </div>

                    <div id="paymentError" class="alert alert-danger d-none"></div>
                    <div id="paymentSuccess" class="alert alert-success d-none"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success" id="submitPaymentBtn">
                        <i class="fas fa-credit-card me-1"></i> Make Payment
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.text-gradient {
    background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-info) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.card:hover {
    transform: translateY(-2px);
    transition: transform 0.2s ease-in-out;
}

.nav-pills .nav-link {
    border-radius: 25px;
    margin: 0 5px;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}
</style>

<script>
function showPaymentModal(invoiceId, invoiceNumber, totalAmount) {
    document.getElementById('invoiceId').value = invoiceId;
    document.getElementById('invoiceNumber').value = invoiceNumber;
    document.getElementById('amount').value = totalAmount.toFixed(2);
    document.getElementById('maxAmount').textContent = totalAmount.toFixed(2);

    var modal = new bootstrap.Modal(document.getElementById('paymentModal'));
    modal.show();
}

// Handle payment form submission
document.getElementById('paymentForm').addEventListener('submit', function(e) {
    e.preventDefault();
    submitPayment();
});

// Validate payment amount
document.getElementById('amount').addEventListener('input', function() {
    var amount = parseFloat(this.value);
    var maxAmount = parseFloat(document.getElementById('maxAmount').textContent);

    if (amount > maxAmount) {
        this.setCustomValidity('Amount cannot exceed the invoice total');
    } else if (amount <= 0) {
        this.setCustomValidity('Amount must be greater than 0');
    } else {
        this.setCustomValidity('');
    }
});

function submitPayment() {
    const form = document.getElementById('paymentForm');
    const submitBtn = document.getElementById('submitPaymentBtn');
    const errorDiv = document.getElementById('paymentError');
    const successDiv = document.getElementById('paymentSuccess');

    // Hide previous messages
    errorDiv.classList.add('d-none');
    successDiv.classList.add('d-none');

    // Disable submit button
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Processing...';

    // Get form data
    const formData = new FormData(form);

    // Add AJAX header
    fetch(form.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            successDiv.textContent = data.message;
            successDiv.classList.remove('d-none');

            // Close modal after 2 seconds and refresh page
            setTimeout(() => {
                const modal = bootstrap.Modal.getInstance(document.getElementById('paymentModal'));
                modal.hide();
                location.reload();
            }, 2000);
        } else {
            errorDiv.textContent = data.message;
            errorDiv.classList.remove('d-none');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        errorDiv.textContent = 'An error occurred while processing the payment. Please try again.';
        errorDiv.classList.remove('d-none');
    })
    .finally(() => {
        // Re-enable submit button
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="fas fa-credit-card me-1"></i> Make Payment';
    });
}
</script>
