using System.ComponentModel.DataAnnotations;
using Technoloway.Core.Entities;

namespace Technoloway.Web.Areas.Admin.Models;

public class ClientViewModel
{
    public ClientViewModel()
    {
        CompanyName = string.Empty;
        ContactName = string.Empty;
        ContactEmail = string.Empty;
        ContactPhone = string.Empty;
        Address = string.Empty;
        City = string.Empty;
        State = string.Empty;
        ZipCode = string.Empty;
        Country = string.Empty;
        LogoUrl = string.Empty;
        UserId = string.Empty;
    }

    public int Id { get; set; }

    public string UserId { get; set; } = string.Empty; // Identity user ID

    [Required(ErrorMessage = "Company name is required")]
    [StringLength(200, ErrorMessage = "Company name cannot exceed 200 characters")]
    public string CompanyName { get; set; } = string.Empty;

    [Required(ErrorMessage = "Contact name is required")]
    [StringLength(100, ErrorMessage = "Contact name cannot exceed 100 characters")]
    public string ContactName { get; set; } = string.Empty;

    [Required(ErrorMessage = "Contact email is required")]
    [EmailAddress(ErrorMessage = "Please enter a valid email address")]
    [StringLength(100, ErrorMessage = "Email cannot exceed 100 characters")]
    public string ContactEmail { get; set; } = string.Empty;

    [StringLength(20, ErrorMessage = "Phone number cannot exceed 20 characters")]
    public string ContactPhone { get; set; } = string.Empty;

    [StringLength(200, ErrorMessage = "Address cannot exceed 200 characters")]
    public string Address { get; set; } = string.Empty;

    [StringLength(100, ErrorMessage = "City cannot exceed 100 characters")]
    public string City { get; set; } = string.Empty;

    [StringLength(100, ErrorMessage = "State cannot exceed 100 characters")]
    public string State { get; set; } = string.Empty;

    [StringLength(20, ErrorMessage = "ZIP code cannot exceed 20 characters")]
    public string ZipCode { get; set; } = string.Empty;

    [StringLength(100, ErrorMessage = "Country cannot exceed 100 characters")]
    public string Country { get; set; } = string.Empty;

    // Current logo URL - NOT required for form validation
    public string? LogoUrl { get; set; } = string.Empty;

    // For file upload
    [Display(Name = "Company Logo")]
    public IFormFile? LogoFile { get; set; }

    // For tracking
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }

    // Convert from Entity to ViewModel
    public static ClientViewModel FromEntity(Technoloway.Core.Entities.Client client)
    {
        return new ClientViewModel
        {
            Id = client.Id,
            UserId = client.UserId,
            CompanyName = client.CompanyName,
            ContactName = client.ContactName,
            ContactEmail = client.ContactEmail,
            ContactPhone = client.ContactPhone,
            Address = client.Address,
            City = client.City,
            State = client.State,
            ZipCode = client.ZipCode,
            Country = client.Country,
            LogoUrl = client.LogoUrl,
            CreatedAt = client.CreatedAt,
            UpdatedAt = client.UpdatedAt
        };
    }

    // Convert from ViewModel to Entity
    public Technoloway.Core.Entities.Client ToEntity()
    {
        return new Technoloway.Core.Entities.Client
        {
            Id = this.Id,
            UserId = this.UserId ?? string.Empty,
            CompanyName = this.CompanyName ?? string.Empty,
            ContactName = this.ContactName ?? string.Empty,
            ContactEmail = this.ContactEmail ?? string.Empty,
            ContactPhone = this.ContactPhone ?? string.Empty,
            Address = this.Address ?? string.Empty,
            City = this.City ?? string.Empty,
            State = this.State ?? string.Empty,
            ZipCode = this.ZipCode ?? string.Empty,
            Country = this.Country ?? string.Empty,
            LogoUrl = this.LogoUrl ?? string.Empty,
            CreatedAt = this.CreatedAt,
            UpdatedAt = this.UpdatedAt
        };
    }

    // Update existing entity with ViewModel data
    public void UpdateEntity(Technoloway.Core.Entities.Client client)
    {
        client.UserId = this.UserId ?? string.Empty;
        client.CompanyName = this.CompanyName ?? string.Empty;
        client.ContactName = this.ContactName ?? string.Empty;
        client.ContactEmail = this.ContactEmail ?? string.Empty;
        client.ContactPhone = this.ContactPhone ?? string.Empty;
        client.Address = this.Address ?? string.Empty;
        client.City = this.City ?? string.Empty;
        client.State = this.State ?? string.Empty;
        client.ZipCode = this.ZipCode ?? string.Empty;
        client.Country = this.Country ?? string.Empty;
        client.LogoUrl = this.LogoUrl ?? string.Empty;
    }
}
