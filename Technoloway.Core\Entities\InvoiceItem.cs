using Technoloway.Core.Common;

namespace Technoloway.Core.Entities;

public class InvoiceItem : BaseEntity
{
    public string Description { get; set; } = string.Empty;
    public decimal Quantity { get; set; }
    public decimal UnitPrice { get; set; }
    public decimal TotalPrice { get; set; }
    
    // Navigation properties
    public int InvoiceId { get; set; }
    public Invoice Invoice { get; set; } = null!;
}
