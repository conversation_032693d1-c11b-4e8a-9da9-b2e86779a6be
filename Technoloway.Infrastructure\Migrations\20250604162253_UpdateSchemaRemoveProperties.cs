﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Technoloway.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class UpdateSchemaRemoveProperties : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Invoices_Projects_ProjectId",
                table: "Invoices");

            migrationBuilder.DropForeignKey(
                name: "FK_Projects_Services_ServiceId",
                table: "Projects");

            migrationBuilder.DropColumn(
                name: "CompletionDate",
                table: "Projects");

            migrationBuilder.DropColumn(
                name: "Content",
                table: "ChatbotResponses");

            migrationBuilder.DropColumn(
                name: "Title",
                table: "ChatbotResponses");

            migrationBuilder.DropColumn(
                name: "Value",
                table: "ChatbotQuickActions");

            migrationBuilder.DropColumn(
                name: "IsCaseSensitive",
                table: "ChatbotKeywords");

            migrationBuilder.DropColumn(
                name: "AuthorId",
                table: "BlogPosts");

            migrationBuilder.DropColumn(
                name: "AuthorName",
                table: "BlogPosts");

            migrationBuilder.DropColumn(
                name: "MetaDescription",
                table: "BlogPosts");

            migrationBuilder.DropColumn(
                name: "MetaKeywords",
                table: "BlogPosts");

            migrationBuilder.DropColumn(
                name: "MetaTitle",
                table: "BlogPosts");

            migrationBuilder.RenameColumn(
                name: "Label",
                table: "ChatbotQuickActions",
                newName: "Text");

            migrationBuilder.AlterColumn<string>(
                name: "TwitterUrl",
                table: "TeamMembers",
                type: "TEXT",
                maxLength: 500,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "TEXT");

            migrationBuilder.AlterColumn<string>(
                name: "PhotoUrl",
                table: "TeamMembers",
                type: "TEXT",
                maxLength: 500,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "TEXT");

            migrationBuilder.AlterColumn<string>(
                name: "LinkedInUrl",
                table: "TeamMembers",
                type: "TEXT",
                maxLength: 500,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "TEXT");

            migrationBuilder.AlterColumn<string>(
                name: "GithubUrl",
                table: "TeamMembers",
                type: "TEXT",
                maxLength: 500,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "TEXT");

            migrationBuilder.AlterColumn<string>(
                name: "Email",
                table: "TeamMembers",
                type: "TEXT",
                maxLength: 255,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "TEXT");

            migrationBuilder.AlterColumn<string>(
                name: "Bio",
                table: "TeamMembers",
                type: "TEXT",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "TEXT");

            migrationBuilder.AddColumn<string>(
                name: "Address",
                table: "TeamMembers",
                type: "TEXT",
                maxLength: 40,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "BirthDate",
                table: "TeamMembers",
                type: "TEXT",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "City",
                table: "TeamMembers",
                type: "TEXT",
                maxLength: 15,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Country",
                table: "TeamMembers",
                type: "TEXT",
                maxLength: 15,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "EmpResumeUrl",
                table: "TeamMembers",
                type: "TEXT",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Gender",
                table: "TeamMembers",
                type: "TEXT",
                maxLength: 10,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "HireDate",
                table: "TeamMembers",
                type: "TEXT",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "MaritalStatus",
                table: "TeamMembers",
                type: "TEXT",
                maxLength: 10,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Notes",
                table: "TeamMembers",
                type: "TEXT",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PayrollMethod",
                table: "TeamMembers",
                type: "TEXT",
                maxLength: 10,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Phone",
                table: "TeamMembers",
                type: "TEXT",
                maxLength: 12,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<decimal>(
                name: "Salary",
                table: "TeamMembers",
                type: "TEXT",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SocialSecurityNo",
                table: "TeamMembers",
                type: "TEXT",
                maxLength: 15,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "State",
                table: "TeamMembers",
                type: "TEXT",
                maxLength: 15,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ZipCode",
                table: "TeamMembers",
                type: "TEXT",
                maxLength: 10,
                nullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "IconClass",
                table: "Services",
                type: "TEXT",
                maxLength: 100,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "TEXT");

            migrationBuilder.AddColumn<int>(
                name: "CategID",
                table: "Services",
                type: "INTEGER",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "ServDiscountRate",
                table: "Services",
                type: "INTEGER",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ServManager",
                table: "Services",
                type: "TEXT",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "ServTotalDiscount",
                table: "Services",
                type: "TEXT",
                nullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "ServiceId",
                table: "Projects",
                type: "INTEGER",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "INTEGER");

            migrationBuilder.AddColumn<decimal>(
                name: "EstimateCost",
                table: "Projects",
                type: "TEXT",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "EstimateEffort",
                table: "Projects",
                type: "TEXT",
                maxLength: 10,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "EstimateTime",
                table: "Projects",
                type: "TEXT",
                maxLength: 10,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "OrderID",
                table: "Projects",
                type: "INTEGER",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<DateTime>(
                name: "ProjCompletionDate",
                table: "Projects",
                type: "TEXT",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ProjGoals",
                table: "Projects",
                type: "TEXT",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ProjManager",
                table: "Projects",
                type: "TEXT",
                maxLength: 10,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "ProjStartDate",
                table: "Projects",
                type: "TEXT",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Status",
                table: "Projects",
                type: "TEXT",
                maxLength: 10,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ContID",
                table: "Invoices",
                type: "INTEGER",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "OrderID",
                table: "Invoices",
                type: "INTEGER",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AlterColumn<string>(
                name: "ContactPhone",
                table: "Clients",
                type: "TEXT",
                maxLength: 20,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 20);

            migrationBuilder.AddColumn<string>(
                name: "CompanyWebsite",
                table: "Clients",
                type: "TEXT",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ContactFax",
                table: "Clients",
                type: "TEXT",
                maxLength: 20,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ContactPosition",
                table: "Clients",
                type: "TEXT",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Notes",
                table: "Clients",
                type: "TEXT",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ResponseText",
                table: "ChatbotResponses",
                type: "TEXT",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ActionValue",
                table: "ChatbotQuickActions",
                type: "TEXT",
                maxLength: 500,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AlterColumn<string>(
                name: "FeaturedImageUrl",
                table: "BlogPosts",
                type: "TEXT",
                maxLength: 500,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "TEXT");

            migrationBuilder.AlterColumn<string>(
                name: "Excerpt",
                table: "BlogPosts",
                type: "TEXT",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "TEXT");

            migrationBuilder.AlterColumn<string>(
                name: "Categories",
                table: "BlogPosts",
                type: "TEXT",
                maxLength: 500,
                nullable: true,
                oldClrType: typeof(string),
                oldType: "TEXT");

            migrationBuilder.CreateTable(
                name: "Categories",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    CategID = table.Column<int>(type: "INTEGER", nullable: false),
                    ParentID = table.Column<int>(type: "INTEGER", nullable: false),
                    CategName = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    CategDesc = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    IsDeleted = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Categories", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Categories_Categories_ParentID",
                        column: x => x.ParentID,
                        principalTable: "Categories",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Orders",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    OrderID = table.Column<int>(type: "INTEGER", nullable: false),
                    OrderTitle = table.Column<string>(type: "TEXT", maxLength: 40, nullable: false),
                    ClientID = table.Column<int>(type: "INTEGER", nullable: false),
                    OrderManager = table.Column<string>(type: "TEXT", maxLength: 15, nullable: true),
                    OrderDesc = table.Column<string>(type: "TEXT", nullable: true),
                    OrderDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    OrderCost = table.Column<decimal>(type: "TEXT", nullable: true),
                    OrderDiscountRate = table.Column<int>(type: "INTEGER", nullable: true),
                    OrderTotalDiscount = table.Column<decimal>(type: "TEXT", nullable: true),
                    Status = table.Column<string>(type: "TEXT", maxLength: 10, nullable: true),
                    Notes = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    IsDeleted = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Orders", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Orders_Clients_ClientID",
                        column: x => x.ClientID,
                        principalTable: "Clients",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "PayrollRecords",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    TransNo = table.Column<int>(type: "INTEGER", nullable: false),
                    TeamMemberID = table.Column<int>(type: "INTEGER", nullable: false),
                    PayDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    WorkHours = table.Column<int>(type: "INTEGER", nullable: true),
                    PayRate = table.Column<decimal>(type: "TEXT", nullable: true),
                    GrossPay = table.Column<decimal>(type: "TEXT", nullable: true),
                    Taxes = table.Column<decimal>(type: "TEXT", nullable: true),
                    Bonus = table.Column<decimal>(type: "TEXT", nullable: true),
                    Deduction = table.Column<decimal>(type: "TEXT", nullable: true),
                    NetPay = table.Column<decimal>(type: "TEXT", nullable: true),
                    PayMethod = table.Column<string>(type: "TEXT", maxLength: 10, nullable: true),
                    Status = table.Column<string>(type: "TEXT", maxLength: 10, nullable: true),
                    Notes = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    IsDeleted = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PayrollRecords", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PayrollRecords_TeamMembers_TeamMemberID",
                        column: x => x.TeamMemberID,
                        principalTable: "TeamMembers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "ServiceOptions",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    OptID = table.Column<int>(type: "INTEGER", nullable: false),
                    ServID = table.Column<int>(type: "INTEGER", nullable: false),
                    OptName = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    OptCost = table.Column<decimal>(type: "TEXT", nullable: true),
                    OptDiscountRate = table.Column<int>(type: "INTEGER", nullable: true),
                    OptTotalDiscount = table.Column<decimal>(type: "TEXT", nullable: true),
                    OptAvailability = table.Column<string>(type: "TEXT", maxLength: 12, nullable: true),
                    OptDesc = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    IsDeleted = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ServiceOptions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ServiceOptions_Services_ServID",
                        column: x => x.ServID,
                        principalTable: "Services",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Tasks",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    TaskID = table.Column<int>(type: "INTEGER", nullable: false),
                    ProjNo = table.Column<int>(type: "INTEGER", nullable: false),
                    TeamMemberID = table.Column<int>(type: "INTEGER", nullable: false),
                    TaskDes = table.Column<string>(type: "TEXT", nullable: true),
                    TaskStartDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    TaskEndDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    WorkHours = table.Column<int>(type: "INTEGER", nullable: true),
                    PayRate = table.Column<decimal>(type: "TEXT", nullable: true),
                    Status = table.Column<string>(type: "TEXT", maxLength: 10, nullable: true),
                    Notes = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    IsDeleted = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Tasks", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Tasks_Projects_ProjNo",
                        column: x => x.ProjNo,
                        principalTable: "Projects",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Tasks_TeamMembers_TeamMemberID",
                        column: x => x.TeamMemberID,
                        principalTable: "TeamMembers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Contracts",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    ContID = table.Column<int>(type: "INTEGER", nullable: false),
                    ContName = table.Column<string>(type: "TEXT", maxLength: 40, nullable: false),
                    ProjID = table.Column<int>(type: "INTEGER", nullable: false),
                    ClientID = table.Column<int>(type: "INTEGER", nullable: false),
                    OrderID = table.Column<int>(type: "INTEGER", nullable: false),
                    ContManager = table.Column<string>(type: "TEXT", maxLength: 40, nullable: true),
                    ContServType = table.Column<string>(type: "TEXT", maxLength: 10, nullable: true),
                    ContLang = table.Column<string>(type: "TEXT", maxLength: 10, nullable: true),
                    AgreementDesc = table.Column<string>(type: "TEXT", nullable: true),
                    ContValue = table.Column<decimal>(type: "TEXT", nullable: true),
                    ContValueCurr = table.Column<string>(type: "TEXT", maxLength: 10, nullable: true),
                    BillingType = table.Column<string>(type: "TEXT", maxLength: 10, nullable: true),
                    NextBillDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    ContIssueDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    ContSignMethod = table.Column<string>(type: "TEXT", maxLength: 10, nullable: true),
                    ContSignedDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    ContExecutedDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    ContExpiryDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    ContStatus = table.Column<string>(type: "TEXT", maxLength: 10, nullable: true),
                    LastUpdateDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    LastUpdateUser = table.Column<string>(type: "TEXT", maxLength: 40, nullable: true),
                    ContFile = table.Column<byte[]>(type: "BLOB", nullable: true),
                    FileUploadDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    Comments = table.Column<string>(type: "TEXT", nullable: true),
                    Notes = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    IsDeleted = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Contracts", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Contracts_Clients_ClientID",
                        column: x => x.ClientID,
                        principalTable: "Clients",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Contracts_Orders_OrderID",
                        column: x => x.OrderID,
                        principalTable: "Orders",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_Contracts_Projects_ProjID",
                        column: x => x.ProjID,
                        principalTable: "Projects",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "ServiceOptionFeatures",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    FeatID = table.Column<int>(type: "INTEGER", nullable: false),
                    OptID = table.Column<int>(type: "INTEGER", nullable: false),
                    FeatName = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    FeatCost = table.Column<decimal>(type: "TEXT", nullable: true),
                    FeatDiscountRate = table.Column<int>(type: "INTEGER", nullable: true),
                    FeatTotalDiscount = table.Column<decimal>(type: "TEXT", nullable: true),
                    FeatAvailability = table.Column<string>(type: "TEXT", maxLength: 12, nullable: true),
                    FeatDesc = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    IsDeleted = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ServiceOptionFeatures", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ServiceOptionFeatures_ServiceOptions_OptID",
                        column: x => x.OptID,
                        principalTable: "ServiceOptions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "OrderDetails",
                columns: table => new
                {
                    Id = table.Column<int>(type: "INTEGER", nullable: false)
                        .Annotation("Sqlite:Autoincrement", true),
                    OrderDetailID = table.Column<int>(type: "INTEGER", nullable: false),
                    OrderID = table.Column<int>(type: "INTEGER", nullable: false),
                    ServID = table.Column<int>(type: "INTEGER", nullable: false),
                    OptID = table.Column<int>(type: "INTEGER", nullable: true),
                    FeatID = table.Column<int>(type: "INTEGER", nullable: true),
                    CostEach = table.Column<decimal>(type: "TEXT", nullable: true),
                    DiscountRate = table.Column<int>(type: "INTEGER", nullable: true),
                    TotalDiscount = table.Column<decimal>(type: "TEXT", nullable: true),
                    AddedDate = table.Column<DateTime>(type: "TEXT", nullable: true),
                    Comments = table.Column<string>(type: "TEXT", nullable: true),
                    Notes = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "TEXT", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "TEXT", nullable: true),
                    IsDeleted = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OrderDetails", x => x.Id);
                    table.ForeignKey(
                        name: "FK_OrderDetails_Orders_OrderID",
                        column: x => x.OrderID,
                        principalTable: "Orders",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_OrderDetails_ServiceOptionFeatures_FeatID",
                        column: x => x.FeatID,
                        principalTable: "ServiceOptionFeatures",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_OrderDetails_ServiceOptions_OptID",
                        column: x => x.OptID,
                        principalTable: "ServiceOptions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_OrderDetails_Services_ServID",
                        column: x => x.ServID,
                        principalTable: "Services",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Services_CategID",
                table: "Services",
                column: "CategID");

            migrationBuilder.CreateIndex(
                name: "IX_Projects_OrderID",
                table: "Projects",
                column: "OrderID");

            migrationBuilder.CreateIndex(
                name: "IX_Invoices_ContID",
                table: "Invoices",
                column: "ContID");

            migrationBuilder.CreateIndex(
                name: "IX_Invoices_OrderID",
                table: "Invoices",
                column: "OrderID");

            migrationBuilder.CreateIndex(
                name: "IX_Categories_ParentID",
                table: "Categories",
                column: "ParentID");

            migrationBuilder.CreateIndex(
                name: "IX_Contracts_ClientID",
                table: "Contracts",
                column: "ClientID");

            migrationBuilder.CreateIndex(
                name: "IX_Contracts_OrderID",
                table: "Contracts",
                column: "OrderID");

            migrationBuilder.CreateIndex(
                name: "IX_Contracts_ProjID",
                table: "Contracts",
                column: "ProjID");

            migrationBuilder.CreateIndex(
                name: "IX_OrderDetails_FeatID",
                table: "OrderDetails",
                column: "FeatID");

            migrationBuilder.CreateIndex(
                name: "IX_OrderDetails_OptID",
                table: "OrderDetails",
                column: "OptID");

            migrationBuilder.CreateIndex(
                name: "IX_OrderDetails_OrderID",
                table: "OrderDetails",
                column: "OrderID");

            migrationBuilder.CreateIndex(
                name: "IX_OrderDetails_ServID",
                table: "OrderDetails",
                column: "ServID");

            migrationBuilder.CreateIndex(
                name: "IX_Orders_ClientID",
                table: "Orders",
                column: "ClientID");

            migrationBuilder.CreateIndex(
                name: "IX_PayrollRecords_TeamMemberID",
                table: "PayrollRecords",
                column: "TeamMemberID");

            migrationBuilder.CreateIndex(
                name: "IX_ServiceOptionFeatures_OptID",
                table: "ServiceOptionFeatures",
                column: "OptID");

            migrationBuilder.CreateIndex(
                name: "IX_ServiceOptions_ServID",
                table: "ServiceOptions",
                column: "ServID");

            migrationBuilder.CreateIndex(
                name: "IX_Tasks_ProjNo",
                table: "Tasks",
                column: "ProjNo");

            migrationBuilder.CreateIndex(
                name: "IX_Tasks_TeamMemberID",
                table: "Tasks",
                column: "TeamMemberID");

            migrationBuilder.AddForeignKey(
                name: "FK_Invoices_Contracts_ContID",
                table: "Invoices",
                column: "ContID",
                principalTable: "Contracts",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Invoices_Orders_OrderID",
                table: "Invoices",
                column: "OrderID",
                principalTable: "Orders",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Invoices_Projects_ProjectId",
                table: "Invoices",
                column: "ProjectId",
                principalTable: "Projects",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Projects_Orders_OrderID",
                table: "Projects",
                column: "OrderID",
                principalTable: "Orders",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Projects_Services_ServiceId",
                table: "Projects",
                column: "ServiceId",
                principalTable: "Services",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Services_Categories_CategID",
                table: "Services",
                column: "CategID",
                principalTable: "Categories",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Invoices_Contracts_ContID",
                table: "Invoices");

            migrationBuilder.DropForeignKey(
                name: "FK_Invoices_Orders_OrderID",
                table: "Invoices");

            migrationBuilder.DropForeignKey(
                name: "FK_Invoices_Projects_ProjectId",
                table: "Invoices");

            migrationBuilder.DropForeignKey(
                name: "FK_Projects_Orders_OrderID",
                table: "Projects");

            migrationBuilder.DropForeignKey(
                name: "FK_Projects_Services_ServiceId",
                table: "Projects");

            migrationBuilder.DropForeignKey(
                name: "FK_Services_Categories_CategID",
                table: "Services");

            migrationBuilder.DropTable(
                name: "Categories");

            migrationBuilder.DropTable(
                name: "Contracts");

            migrationBuilder.DropTable(
                name: "OrderDetails");

            migrationBuilder.DropTable(
                name: "PayrollRecords");

            migrationBuilder.DropTable(
                name: "Tasks");

            migrationBuilder.DropTable(
                name: "Orders");

            migrationBuilder.DropTable(
                name: "ServiceOptionFeatures");

            migrationBuilder.DropTable(
                name: "ServiceOptions");

            migrationBuilder.DropIndex(
                name: "IX_Services_CategID",
                table: "Services");

            migrationBuilder.DropIndex(
                name: "IX_Projects_OrderID",
                table: "Projects");

            migrationBuilder.DropIndex(
                name: "IX_Invoices_ContID",
                table: "Invoices");

            migrationBuilder.DropIndex(
                name: "IX_Invoices_OrderID",
                table: "Invoices");

            migrationBuilder.DropColumn(
                name: "Address",
                table: "TeamMembers");

            migrationBuilder.DropColumn(
                name: "BirthDate",
                table: "TeamMembers");

            migrationBuilder.DropColumn(
                name: "City",
                table: "TeamMembers");

            migrationBuilder.DropColumn(
                name: "Country",
                table: "TeamMembers");

            migrationBuilder.DropColumn(
                name: "EmpResumeUrl",
                table: "TeamMembers");

            migrationBuilder.DropColumn(
                name: "Gender",
                table: "TeamMembers");

            migrationBuilder.DropColumn(
                name: "HireDate",
                table: "TeamMembers");

            migrationBuilder.DropColumn(
                name: "MaritalStatus",
                table: "TeamMembers");

            migrationBuilder.DropColumn(
                name: "Notes",
                table: "TeamMembers");

            migrationBuilder.DropColumn(
                name: "PayrollMethod",
                table: "TeamMembers");

            migrationBuilder.DropColumn(
                name: "Phone",
                table: "TeamMembers");

            migrationBuilder.DropColumn(
                name: "Salary",
                table: "TeamMembers");

            migrationBuilder.DropColumn(
                name: "SocialSecurityNo",
                table: "TeamMembers");

            migrationBuilder.DropColumn(
                name: "State",
                table: "TeamMembers");

            migrationBuilder.DropColumn(
                name: "ZipCode",
                table: "TeamMembers");

            migrationBuilder.DropColumn(
                name: "CategID",
                table: "Services");

            migrationBuilder.DropColumn(
                name: "ServDiscountRate",
                table: "Services");

            migrationBuilder.DropColumn(
                name: "ServManager",
                table: "Services");

            migrationBuilder.DropColumn(
                name: "ServTotalDiscount",
                table: "Services");

            migrationBuilder.DropColumn(
                name: "EstimateCost",
                table: "Projects");

            migrationBuilder.DropColumn(
                name: "EstimateEffort",
                table: "Projects");

            migrationBuilder.DropColumn(
                name: "EstimateTime",
                table: "Projects");

            migrationBuilder.DropColumn(
                name: "OrderID",
                table: "Projects");

            migrationBuilder.DropColumn(
                name: "ProjCompletionDate",
                table: "Projects");

            migrationBuilder.DropColumn(
                name: "ProjGoals",
                table: "Projects");

            migrationBuilder.DropColumn(
                name: "ProjManager",
                table: "Projects");

            migrationBuilder.DropColumn(
                name: "ProjStartDate",
                table: "Projects");

            migrationBuilder.DropColumn(
                name: "Status",
                table: "Projects");

            migrationBuilder.DropColumn(
                name: "ContID",
                table: "Invoices");

            migrationBuilder.DropColumn(
                name: "OrderID",
                table: "Invoices");

            migrationBuilder.DropColumn(
                name: "CompanyWebsite",
                table: "Clients");

            migrationBuilder.DropColumn(
                name: "ContactFax",
                table: "Clients");

            migrationBuilder.DropColumn(
                name: "ContactPosition",
                table: "Clients");

            migrationBuilder.DropColumn(
                name: "Notes",
                table: "Clients");

            migrationBuilder.DropColumn(
                name: "ResponseText",
                table: "ChatbotResponses");

            migrationBuilder.DropColumn(
                name: "ActionValue",
                table: "ChatbotQuickActions");

            migrationBuilder.RenameColumn(
                name: "Text",
                table: "ChatbotQuickActions",
                newName: "Label");

            migrationBuilder.AlterColumn<string>(
                name: "TwitterUrl",
                table: "TeamMembers",
                type: "TEXT",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 500,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "PhotoUrl",
                table: "TeamMembers",
                type: "TEXT",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 500,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "LinkedInUrl",
                table: "TeamMembers",
                type: "TEXT",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 500,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "GithubUrl",
                table: "TeamMembers",
                type: "TEXT",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 500,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Email",
                table: "TeamMembers",
                type: "TEXT",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 255,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Bio",
                table: "TeamMembers",
                type: "TEXT",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "IconClass",
                table: "Services",
                type: "TEXT",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 100,
                oldNullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "ServiceId",
                table: "Projects",
                type: "INTEGER",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "INTEGER",
                oldNullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "CompletionDate",
                table: "Projects",
                type: "TEXT",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AlterColumn<string>(
                name: "ContactPhone",
                table: "Clients",
                type: "TEXT",
                maxLength: 20,
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 20,
                oldNullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Content",
                table: "ChatbotResponses",
                type: "TEXT",
                maxLength: 2000,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Title",
                table: "ChatbotResponses",
                type: "TEXT",
                maxLength: 200,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Value",
                table: "ChatbotQuickActions",
                type: "TEXT",
                maxLength: 200,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<bool>(
                name: "IsCaseSensitive",
                table: "ChatbotKeywords",
                type: "INTEGER",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AlterColumn<string>(
                name: "FeaturedImageUrl",
                table: "BlogPosts",
                type: "TEXT",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 500,
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Excerpt",
                table: "BlogPosts",
                type: "TEXT",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldNullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Categories",
                table: "BlogPosts",
                type: "TEXT",
                nullable: false,
                defaultValue: "",
                oldClrType: typeof(string),
                oldType: "TEXT",
                oldMaxLength: 500,
                oldNullable: true);

            migrationBuilder.AddColumn<string>(
                name: "AuthorId",
                table: "BlogPosts",
                type: "TEXT",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "AuthorName",
                table: "BlogPosts",
                type: "TEXT",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "MetaDescription",
                table: "BlogPosts",
                type: "TEXT",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "MetaKeywords",
                table: "BlogPosts",
                type: "TEXT",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "MetaTitle",
                table: "BlogPosts",
                type: "TEXT",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddForeignKey(
                name: "FK_Invoices_Projects_ProjectId",
                table: "Invoices",
                column: "ProjectId",
                principalTable: "Projects",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_Projects_Services_ServiceId",
                table: "Projects",
                column: "ServiceId",
                principalTable: "Services",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
