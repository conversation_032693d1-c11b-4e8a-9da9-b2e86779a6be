@model Technoloway.Core.Entities.SiteSetting

@{
    ViewData["Title"] = "Delete Setting";
}

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Delete Setting</h1>
    <a asp-action="Index" class="btn btn-secondary shadow-sm">
        <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Settings
    </a>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card shadow border-danger">
            <div class="card-header bg-danger text-white py-3">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-exclamation-triangle me-2"></i>Confirm Deletion
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Warning:</strong> You are about to delete this setting. This action cannot be undone.
                </div>

                <h5>Are you sure you want to delete this setting?</h5>
                
                <div class="table-responsive mt-4">
                    <table class="table table-bordered">
                        <tr>
                            <th width="30%">Setting Key:</th>
                            <td><strong>@Model.Key</strong></td>
                        </tr>
                        <tr>
                            <th>Group:</th>
                            <td>@Model.Group</td>
                        </tr>
                        <tr>
                            <th>Current Value:</th>
                            <td>
                                @if (Model.Key.ToLower().Contains("password") || Model.Key.ToLower().Contains("secret") || Model.Key.ToLower().Contains("key"))
                                {
                                    <span class="text-muted">••••••••</span>
                                }
                                else
                                {
                                    @if (Model.Value.Length > 100)
                                    {
                                        <span>@Model.Value.Substring(0, 100)...</span>
                                    }
                                    else
                                    {
                                        <span>@Model.Value</span>
                                    }
                                }
                            </td>
                        </tr>
                        <tr>
                            <th>Description:</th>
                            <td>@Model.Description</td>
                        </tr>
                        <tr>
                            <th>Visibility:</th>
                            <td>
                                @if (Model.IsPublic)
                                {
                                    <span class="badge bg-success">Public</span>
                                }
                                else
                                {
                                    <span class="badge bg-warning text-dark">Private</span>
                                }
                            </td>
                        </tr>
                        <tr>
                            <th>Created:</th>
                            <td>@Model.CreatedAt.ToString("MMM dd, yyyy 'at' h:mm tt")</td>
                        </tr>
                        @if (Model.UpdatedAt.HasValue)
                        {
                            <tr>
                                <th>Last Updated:</th>
                                <td>@Model.UpdatedAt.Value.ToString("MMM dd, yyyy 'at' h:mm tt")</td>
                            </tr>
                        }
                    </table>
                </div>

                <form asp-action="Delete" method="post" class="mt-4">
                    <input type="hidden" asp-for="Id" />
                    <div class="d-flex justify-content-end">
                        <a asp-action="Index" class="btn btn-secondary me-2">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                        <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-primary me-2">
                            <i class="fas fa-edit me-2"></i>Edit Instead
                        </a>
                        <button type="submit" class="btn btn-danger" onclick="return confirm('Are you absolutely sure you want to delete this setting?')">
                            <i class="fas fa-trash me-2"></i>Delete Setting
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row justify-content-center mt-4">
    <div class="col-md-8">
        <div class="card shadow border-info">
            <div class="card-header bg-info text-white py-3">
                <h6 class="m-0 font-weight-bold">
                    <i class="fas fa-info-circle me-2"></i>Important Information
                </h6>
            </div>
            <div class="card-body">
                <ul class="mb-0">
                    <li>Deleting a setting will perform a <strong>soft delete</strong> - the data will be marked as deleted but not permanently removed from the database.</li>
                    <li>If this setting is used by the application, removing it may cause functionality issues.</li>
                    <li>Consider editing the setting value instead of deleting it if you need to change its configuration.</li>
                    <li>You can always create a new setting with the same key if needed later.</li>
                </ul>
            </div>
        </div>
    </div>
</div>
