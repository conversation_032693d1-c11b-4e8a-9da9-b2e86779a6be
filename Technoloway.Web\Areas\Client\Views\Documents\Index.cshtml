@model IEnumerable<Technoloway.Core.Entities.ProjectDocument>

@{
    ViewData["Title"] = "Documents";
    var projects = ViewBag.Projects as List<Technoloway.Core.Entities.Project>;
    var selectedProjectId = ViewBag.SelectedProjectId as int?;
    var selectedCategory = ViewBag.SelectedCategory as string;
    var totalDocuments = ViewBag.TotalDocuments as int? ?? 0;
    var totalSize = ViewBag.TotalSize as long? ?? 0;
    var categories = ViewBag.Categories as dynamic;
}

<div class="container-fluid p-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="text-gradient mb-2">
                        <i class="fas fa-folder-open me-2"></i>Document Center
                    </h1>
                    <p class="text-muted">Access and manage your project documents</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary btn-sm" onclick="location.reload()">
                        <i class="fas fa-sync-alt me-1"></i> Refresh
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-6 col-lg-3 mb-3">
            <div class="card stats-card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-primary bg-gradient rounded-3 p-3">
                                <i class="fas fa-file-alt text-white fa-lg"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Total Documents</h6>
                            <h4 class="mb-0">@totalDocuments</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-3 mb-3">
            <div class="card stats-card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-gradient rounded-3 p-3">
                                <i class="fas fa-hdd text-white fa-lg"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Total Size</h6>
                            <h4 class="mb-0">@(totalSize / 1024 / 1024) MB</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-3 mb-3">
            <div class="card stats-card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-info bg-gradient rounded-3 p-3">
                                <i class="fas fa-project-diagram text-white fa-lg"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Projects</h6>
                            <h4 class="mb-0">@(projects?.Count ?? 0)</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-3 mb-3">
            <div class="card stats-card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-warning bg-gradient rounded-3 p-3">
                                <i class="fas fa-tags text-white fa-lg"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-1">Categories</h6>
                            <h4 class="mb-0">@(categories?.Count ?? 0)</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            <label for="projectFilter" class="form-label">Filter by Project</label>
                            <select name="projectId" id="projectFilter" class="form-select">
                                <option value="">All Projects</option>
                                @if (projects != null)
                                {
                                    @foreach (var project in projects)
                                    {
                                        <option value="@project.Id" selected="@(selectedProjectId == project.Id)">
                                            @project.Name
                                        </option>
                                    }
                                }
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="categoryFilter" class="form-label">Filter by Category</label>
                            <select name="category" id="categoryFilter" class="form-select">
                                <option value="">All Categories</option>
                                @if (categories != null)
                                {
                                    @foreach (var category in categories)
                                    {
                                        <option value="@category.Type" selected="@(selectedCategory == category.Type)">
                                            @category.Type.ToUpper() (@category.Count)
                                        </option>
                                    }
                                }
                            </select>
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-filter me-1"></i> Apply Filters
                            </button>
                            <a href="@Url.Action("Index")" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i> Clear
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Documents List -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-file-alt me-2"></i>Documents
                        @if (selectedProjectId.HasValue)
                        {
                            var selectedProject = projects?.FirstOrDefault(p => p.Id == selectedProjectId.Value);
                            if (selectedProject != null)
                            {
                                <span class="text-muted">- @selectedProject.Name</span>
                            }
                        }
                    </h5>
                </div>
                <div class="card-body">
                    @if (!Model.Any())
                    {
                        <div class="text-center py-5">
                            <div class="mb-4">
                                <i class="fas fa-folder-open fa-4x text-muted"></i>
                            </div>
                            <h5 class="text-muted">No Documents Found</h5>
                            <p class="text-muted">
                                @if (selectedProjectId.HasValue || !string.IsNullOrEmpty(selectedCategory))
                                {
                                    <span>No documents match your current filters.</span>
                                }
                                else
                                {
                                    <span>No documents have been uploaded to your projects yet.</span>
                                }
                            </p>
                            @if (selectedProjectId.HasValue || !string.IsNullOrEmpty(selectedCategory))
                            {
                                <a href="@Url.Action("Index")" class="btn btn-outline-primary">
                                    <i class="fas fa-times me-1"></i> Clear Filters
                                </a>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="row">
                            @foreach (var document in Model)
                            {
                                <div class="col-lg-4 col-md-6 mb-4">
                                    <div class="card document-card h-100 border-0 shadow-sm">
                                        <div class="card-body">
                                            <div class="d-flex align-items-start mb-3">
                                                <div class="file-icon me-3">
                                                    @{
                                                        string fileExtension = System.IO.Path.GetExtension(document.FileName).TrimStart('.').ToLower();
                                                        string iconClass = "fas fa-file text-muted";

                                                        if (fileExtension.Equals("pdf"))
                                                            iconClass = "fas fa-file-pdf text-danger";
                                                        else if (fileExtension.Equals("doc") || fileExtension.Equals("docx"))
                                                            iconClass = "fas fa-file-word text-primary";
                                                        else if (fileExtension.Equals("xls") || fileExtension.Equals("xlsx"))
                                                            iconClass = "fas fa-file-excel text-success";
                                                        else if (fileExtension.Equals("ppt") || fileExtension.Equals("pptx"))
                                                            iconClass = "fas fa-file-powerpoint text-warning";
                                                        else if (fileExtension.Equals("jpg") || fileExtension.Equals("jpeg") || fileExtension.Equals("png") || fileExtension.Equals("gif"))
                                                            iconClass = "fas fa-file-image text-info";
                                                        else if (fileExtension.Equals("zip") || fileExtension.Equals("rar"))
                                                            iconClass = "fas fa-file-archive text-secondary";
                                                    }
                                                    <i class="@iconClass fa-2x"></i>
                                                </div>
                                                <div class="flex-grow-1">
                                                    <h6 class="card-title mb-1">@document.FileName</h6>
                                                    <small class="text-muted">@document.FileType.ToUpper()</small>
                                                </div>
                                            </div>

                                            <div class="document-meta mb-3">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <span class="text-muted small">Size:</span>
                                                    <span class="badge bg-light text-dark">@(document.FileSize / 1024) KB</span>
                                                </div>
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <span class="text-muted small">Project:</span>
                                                    <span class="small">@(projects?.FirstOrDefault(p => p.Id == document.ProjectId)?.Name ?? "Unknown")</span>
                                                </div>
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <span class="text-muted small">Uploaded:</span>
                                                    <span class="small">@document.CreatedAt.ToString("MMM dd, yyyy")</span>
                                                </div>
                                            </div>

                                            <div class="document-actions">
                                                <div class="btn-group w-100" role="group">
                                                    <a asp-action="Details" asp-route-id="@document.Id" class="btn btn-outline-primary btn-sm">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    @{
                                                        bool canPreview = fileExtension.Equals("pdf") || fileExtension.Equals("jpg") ||
                                                                         fileExtension.Equals("jpeg") || fileExtension.Equals("png") ||
                                                                         fileExtension.Equals("gif") || fileExtension.Equals("txt") ||
                                                                         fileExtension.Equals("md");
                                                    }
                                                    @if (canPreview)
                                                    {
                                                        <a asp-action="Preview" asp-route-id="@document.Id" class="btn btn-outline-info btn-sm">
                                                            <i class="fas fa-search"></i>
                                                        </a>
                                                    }
                                                    <a asp-action="Download" asp-route-id="@document.Id" class="btn btn-outline-success btn-sm">
                                                        <i class="fas fa-download"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.text-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stats-card {
    transition: transform 0.2s ease-in-out;
}

.stats-card:hover {
    transform: translateY(-2px);
}

.document-card {
    transition: all 0.2s ease-in-out;
}

.document-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.file-icon {
    min-width: 50px;
}

.document-meta {
    font-size: 0.875rem;
}

.document-actions .btn {
    border-radius: 0;
}

.document-actions .btn:first-child {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

.document-actions .btn:last-child {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-submit form when filters change
    const projectFilter = document.getElementById('projectFilter');
    const categoryFilter = document.getElementById('categoryFilter');

    if (projectFilter) {
        projectFilter.addEventListener('change', function() {
            this.form.submit();
        });
    }

    if (categoryFilter) {
        categoryFilter.addEventListener('change', function() {
            this.form.submit();
        });
    }
});
</script>
