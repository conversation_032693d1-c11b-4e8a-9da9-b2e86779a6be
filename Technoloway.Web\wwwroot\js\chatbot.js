// TechnoloWay Chatbot Logic
class TechnoloWayChatbot {
    constructor() {
        this.isOpen = false;
        this.isMinimized = false;
        this.conversationState = 'welcome';
        this.userContext = {};
        this.messageHistory = [];
        
        this.init();
    }

    init() {
        this.createChatbotHTML();
        this.bindEvents();
        this.loadInitialContent();
        this.setupAutoGreeting();
    }

    createChatbotHTML() {
        const chatbotHTML = `
            <!-- Chatbot Toggle Button -->
            <div id="chatbot-toggle" class="chatbot-toggle">
                <i class="fas fa-comments"></i>
                <span class="notification-badge" id="notification-badge">1</span>
            </div>

            <!-- Chatbot Container -->
            <div id="chatbot-container" class="chatbot-container hidden">
                <!-- Header -->
                <div class="chatbot-header">
                    <div class="header-content">
                        <div class="bot-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="bot-info">
                            <h3>Alex</h3>
                            <span class="status online">
                                <i class="fas fa-circle"></i>
                                Online
                            </span>
                        </div>
                    </div>
                    <div class="header-actions">
                        <button id="minimize-btn" class="action-btn" title="Minimize">
                            <i class="fas fa-minus"></i>
                        </button>
                        <button id="close-btn" class="action-btn" title="Close">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>

                <!-- Messages Area -->
                <div id="messages-container" class="messages-container">
                    <div class="welcome-message">
                        <div class="message bot-message">
                            <div class="message-avatar">
                                <i class="fas fa-robot"></i>
                            </div>
                            <div class="message-content">
                                <div class="message-bubble" id="welcome-message-content">
                                    <!-- Welcome message will be loaded from database -->
                                    <p>👋 Hi there! Loading...</p>
                                </div>
                                <div class="message-time">Just now</div>
                            </div>
                        </div>
                        
                        <!-- Quick Action Buttons - Will be loaded from database -->
                        <div class="quick-actions" id="initial-quick-actions">
                            <!-- Quick actions will be loaded from database -->
                        </div>
                    </div>
                </div>

                <!-- Typing Indicator -->
                <div id="typing-indicator" class="typing-indicator hidden">
                    <div class="message bot-message">
                        <div class="message-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="message-content">
                            <div class="typing-dots">
                                <span></span>
                                <span></span>
                                <span></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Input Area -->
                <div class="input-area">
                    <div class="input-container">
                        <input 
                            type="text" 
                            id="message-input" 
                            placeholder="Type your message..."
                            autocomplete="off"
                        >
                        <button id="send-btn" class="send-btn" disabled>
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                    
                    <!-- Suggested Responses -->
                    <div id="suggested-responses" class="suggested-responses hidden">
                        <!-- Dynamic suggestions will be inserted here -->
                    </div>
                </div>

                <!-- Footer -->
                <div class="chatbot-footer">
                    <span>Powered by TechnoloWay AI</span>
                </div>
            </div>
        `;

        // Append to body
        document.body.insertAdjacentHTML('beforeend', chatbotHTML);
    }

    bindEvents() {
        // Toggle chatbot
        document.getElementById('chatbot-toggle').addEventListener('click', () => {
            this.toggleChatbot();
        });

        // Header actions
        document.getElementById('minimize-btn').addEventListener('click', () => {
            this.minimizeChatbot();
        });

        document.getElementById('close-btn').addEventListener('click', () => {
            this.closeChatbot();
        });

        // Message input
        const messageInput = document.getElementById('message-input');
        const sendBtn = document.getElementById('send-btn');

        messageInput.addEventListener('input', (e) => {
            sendBtn.disabled = !e.target.value.trim();
        });

        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !sendBtn.disabled) {
                this.sendMessage();
            }
        });

        sendBtn.addEventListener('click', () => {
            this.sendMessage();
        });

        // Quick actions
        document.querySelectorAll('.quick-action-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = e.currentTarget.dataset.action;
                this.handleQuickAction(action);
            });
        });
    }

    toggleChatbot() {
        const container = document.getElementById('chatbot-container');
        const toggle = document.getElementById('chatbot-toggle');
        const badge = document.getElementById('notification-badge');

        if (this.isOpen) {
            this.closeChatbot();
        } else {
            container.classList.remove('hidden');
            container.classList.add('fade-in');
            toggle.style.display = 'none';
            badge.style.display = 'none';
            this.isOpen = true;
            
            // Focus input
            setTimeout(() => {
                document.getElementById('message-input').focus();
            }, 300);
        }
    }

    closeChatbot() {
        const container = document.getElementById('chatbot-container');
        const toggle = document.getElementById('chatbot-toggle');

        container.classList.add('hidden');
        toggle.style.display = 'flex';
        this.isOpen = false;
        this.isMinimized = false;
    }

    minimizeChatbot() {
        const container = document.getElementById('chatbot-container');
        this.isMinimized = !this.isMinimized;
        
        if (this.isMinimized) {
            container.classList.add('minimized');
        } else {
            container.classList.remove('minimized');
        }
    }

    sendMessage() {
        const input = document.getElementById('message-input');
        const message = input.value.trim();
        
        if (!message) return;

        // Add user message
        this.addMessage(message, 'user');
        input.value = '';
        document.getElementById('send-btn').disabled = true;

        // Show typing indicator
        this.showTypingIndicator();

        // Process message
        setTimeout(() => {
            this.processUserMessage(message);
        }, 1000 + Math.random() * 1000); // Simulate processing time
    }

    addMessage(content, sender, options = {}) {
        const messagesContainer = document.getElementById('messages-container');
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message slide-up`;

        const avatar = sender === 'bot' ? '<i class="fas fa-robot"></i>' : '<i class="fas fa-user"></i>';
        const time = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

        messageDiv.innerHTML = `
            <div class="message-avatar">${avatar}</div>
            <div class="message-content">
                <div class="message-bubble">
                    ${typeof content === 'string' ? `<p>${content}</p>` : content}
                </div>
                <div class="message-time">${time}</div>
            </div>
        `;

        messagesContainer.appendChild(messageDiv);

        // Add quick actions if provided
        if (options.quickActions) {
            this.addQuickActions(options.quickActions);
        }

        // Add suggestions if provided
        if (options.suggestions) {
            this.showSuggestions(options.suggestions);
        }

        this.scrollToBottom();
        this.messageHistory.push({ content, sender, timestamp: new Date() });
    }

    addQuickActions(actions) {
        const messagesContainer = document.getElementById('messages-container');
        const actionsDiv = document.createElement('div');
        actionsDiv.className = 'quick-actions slide-up';

        actions.forEach(action => {
            const btn = document.createElement('button');
            btn.className = 'quick-action-btn';
            btn.dataset.action = action.value;
            btn.innerHTML = `<i class="${action.icon}"></i> ${action.label}`;
            btn.addEventListener('click', () => {
                this.handleQuickAction(action.value);
            });
            actionsDiv.appendChild(btn);
        });

        messagesContainer.appendChild(actionsDiv);
        this.scrollToBottom();
    }

    showTypingIndicator() {
        document.getElementById('typing-indicator').classList.remove('hidden');
        this.scrollToBottom();
    }

    hideTypingIndicator() {
        document.getElementById('typing-indicator').classList.add('hidden');
    }

    scrollToBottom() {
        const container = document.getElementById('messages-container');
        container.scrollTop = container.scrollHeight;
    }

    async processUserMessage(message) {
        this.hideTypingIndicator();

        try {
            // Call the API to get response with real database data
            const response = await fetch('/api/chatbot/message', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: message,
                    context: this.userContext
                })
            });

            if (response.ok) {
                const data = await response.json();
                this.addMessage(data.content, 'bot', {
                    quickActions: data.quickActions,
                    suggestions: data.suggestions
                });
            } else {
                // Fallback response if API fails
                this.addMessage(
                    "I'm having trouble connecting to our database right now. Please try again in a moment or contact our team directly.",
                    'bot',
                    {
                        quickActions: [
                            { label: 'Try Again', value: 'retry', icon: 'fas fa-redo' },
                            { label: 'Contact Us', value: 'contact', icon: 'fas fa-envelope' }
                        ]
                    }
                );
            }
        } catch (error) {
            console.error('API Error:', error);
            // Fallback response for connection errors
            this.addMessage(
                "I'm having trouble connecting to our database right now. Please try again in a moment or contact our team directly.",
                'bot',
                {
                    quickActions: [
                        { label: 'Try Again', value: 'retry', icon: 'fas fa-redo' },
                        { label: 'Contact Us', value: 'contact', icon: 'fas fa-envelope' }
                    ]
                }
            );
        }
    }

    // Load initial content from database
    async loadInitialContent() {
        try {
            // Load welcome message and initial quick actions from database
            const response = await fetch('/api/chatbot/message', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: 'welcome',
                    context: { isInitial: true }
                })
            });

            if (response.ok) {
                const data = await response.json();

                // Update welcome message
                const welcomeContent = document.getElementById('welcome-message-content');
                if (welcomeContent) {
                    welcomeContent.innerHTML = data.content;
                }

                // Update initial quick actions
                const quickActionsContainer = document.getElementById('initial-quick-actions');
                if (quickActionsContainer && data.quickActions) {
                    quickActionsContainer.innerHTML = '';
                    data.quickActions.forEach(action => {
                        const btn = document.createElement('button');
                        btn.className = 'quick-action-btn';
                        btn.dataset.action = action.value;
                        btn.innerHTML = `<i class="${action.icon}"></i> ${action.label}`;
                        btn.addEventListener('click', () => {
                            this.handleQuickAction(action.value);
                        });
                        quickActionsContainer.appendChild(btn);
                    });
                }
            }
        } catch (error) {
            console.error('Error loading initial content:', error);
            // Keep default loading message if database fails
        }
    }

    handleQuickAction(action) {
        // Handle special actions
        if (action === 'retry') {
            this.addMessage('Let me try again', 'user');
            setTimeout(() => this.processUserMessage('help'), 500);
            return;
        }

        if (action === 'contact') {
            this.addMessage('I want to contact your team', 'user');
            this.showContactModal();
            return;
        }

        if (['call_now', 'schedule', 'email'].includes(action)) {
            this.addMessage(`I'd like to ${action.replace('_', ' ')}`, 'user');
            this.showContactModal();
            return;
        }

        // For all other actions, send to database for processing
        const actionMessages = {
            'services': 'Tell me about your services',
            'quote': 'I need a project quote',
            'portfolio': 'Show me your portfolio',
            'human': 'I want to talk to a human',
            'team': 'Tell me about your team',
            'testimonials': 'Show me client testimonials',
            'projects': 'Show me your projects',
            'about': 'Tell me about your company'
        };

        const userMessage = actionMessages[action] || `I'd like to know more about ${action}`;
        this.addMessage(userMessage, 'user');
        setTimeout(() => this.processUserMessage(action), 500);
    }

    setupAutoGreeting() {
        // Show notification badge after 5 seconds if chatbot hasn't been opened
        setTimeout(() => {
            if (!this.isOpen) {
                document.getElementById('notification-badge').style.display = 'flex';
            }
        }, 5000);
    }

    // All data loading is now handled by the API endpoint
    // The /api/chatbot/message endpoint will process requests and return appropriate responses

    showContactModal() {
        // Create contact modal if it doesn't exist
        if (!document.getElementById('contact-modal')) {
            this.createContactModal();
        }
        document.getElementById('contact-modal').classList.remove('hidden');
    }

    createContactModal() {
        const modalHTML = `
            <div id="contact-modal" class="modal hidden">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>Contact Information</h3>
                        <button class="close-modal" onclick="window.technolowayChatbot.closeContactModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <form id="contact-form" class="contact-form">
                        <div class="form-group">
                            <label for="contact-name">Name *</label>
                            <input type="text" id="contact-name" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="contact-email">Email *</label>
                            <input type="email" id="contact-email" name="email" required>
                        </div>
                        <div class="form-group">
                            <label for="contact-phone">Phone</label>
                            <input type="tel" id="contact-phone" name="phone">
                        </div>
                        <div class="form-group">
                            <label for="contact-company">Company</label>
                            <input type="text" id="contact-company" name="company">
                        </div>
                        <div class="form-group">
                            <label for="contact-method">Preferred Contact Method</label>
                            <select id="contact-method" name="contactMethod">
                                <option value="email">Email</option>
                                <option value="phone">Phone Call</option>
                                <option value="video">Video Call</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="contact-time">Best Time to Contact</label>
                            <select id="contact-time" name="contactTime">
                                <option value="morning">Morning (9 AM - 12 PM)</option>
                                <option value="afternoon">Afternoon (12 PM - 5 PM)</option>
                                <option value="evening">Evening (5 PM - 8 PM)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="contact-message">Additional Message</label>
                            <textarea id="contact-message" name="message" rows="3" placeholder="Tell us about your project..."></textarea>
                        </div>
                        <div class="form-actions">
                            <button type="button" class="btn-secondary" onclick="window.technolowayChatbot.closeContactModal()">Cancel</button>
                            <button type="submit" class="btn-primary">Submit</button>
                        </div>
                    </form>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Add form submission handler
        document.getElementById('contact-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleContactSubmission();
        });
    }

    closeContactModal() {
        document.getElementById('contact-modal').classList.add('hidden');
    }

    async handleContactSubmission() {
        const form = document.getElementById('contact-form');
        const formData = new FormData(form);

        const contactData = {
            name: formData.get('name'),
            email: formData.get('email'),
            phone: formData.get('phone'),
            company: formData.get('company'),
            contactMethod: formData.get('contactMethod'),
            contactTime: formData.get('contactTime'),
            message: formData.get('message'),
            projectType: this.userContext.projectType,
            budgetRange: this.userContext.budgetRange,
            timeline: this.userContext.timeline
        };

        try {
            const response = await fetch('/api/chatbot/contact', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(contactData)
            });

            if (response.ok) {
                this.closeContactModal();
                this.addMessage(
                    `Thank you, ${contactData.name}! I've received your contact information. Our team will reach out to you within 2 hours via ${contactData.contactMethod}. 🎉`,
                    'bot'
                );
                form.reset();
            } else {
                alert('There was an error submitting your information. Please try again or contact us directly.');
            }
        } catch (error) {
            console.error('Contact submission error:', error);
            alert('There was an error submitting your information. Please try again or contact us directly.');
        }
    }
}

// Initialize chatbot when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.technolowayChatbot = new TechnoloWayChatbot();
});
