using Technoloway.Core.Common;
using System.ComponentModel.DataAnnotations;

namespace Technoloway.Core.Entities;

public class ServiceOptionFeature : BaseEntity
{
    [Required]
    public int FeatID { get; set; }
    
    [Required]
    public int OptID { get; set; }
    
    [Required]
    [StringLength(50)]
    public string FeatName { get; set; } = string.Empty;
    
    public decimal? FeatCost { get; set; }
    
    public int? FeatDiscountRate { get; set; }
    
    public decimal? FeatTotalDiscount { get; set; }
    
    [StringLength(12)]
    public string? FeatAvailability { get; set; }
    
    public string? FeatDesc { get; set; }
    
    // Navigation properties
    public ServiceOption ServiceOption { get; set; } = null!;
    public ICollection<OrderDetail> OrderDetails { get; set; } = new List<OrderDetail>();
}
