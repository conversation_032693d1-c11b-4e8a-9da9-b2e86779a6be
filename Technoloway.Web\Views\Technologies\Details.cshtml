@model Technoloway.Web.Models.TechnologyDetailsViewModel

@{
    ViewData["Title"] = Model.Technology.Name;
    ViewData["MetaDescription"] = $"Learn more about how we use {Model.Technology.Name} to build innovative software solutions for our clients.";
    ViewData["MetaKeywords"] = $"technology, {Model.Technology.Name.ToLower()}, software development, web development";
}

<!-- <PERSON> Header -->
<div class="bg-dark text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-md-8">
                <h1 class="fw-bold">@Model.Technology.Name</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="/" class="text-white">Home</a></li>
                        <li class="breadcrumb-item"><a asp-action="Index" class="text-white">Technologies</a></li>
                        <li class="breadcrumb-item active text-white" aria-current="page">@Model.Technology.Name</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- Technology Overview -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mb-4 mb-lg-0">
                <div class="card border-0 shadow mb-4">
                    <div class="card-body p-4">
                        <div class="d-flex align-items-center mb-4">
                            <div class="me-4">
                                <img src="@Model.Technology.IconUrl" alt="@Model.Technology.Name" class="img-fluid" style="height: 100px;">
                            </div>
                            <div>
                                <h2 class="fw-bold mb-2">@Model.Technology.Name</h2>
                                <p class="lead mb-0">Cutting-edge technology for modern applications</p>
                            </div>
                        </div>

                        <h3 class="fw-bold mb-3">Overview</h3>
                        <p class="mb-4">@Model.Technology.Description</p>

                        <h3 class="fw-bold mb-3">Why We Use @Model.Technology.Name</h3>
                        <p>At Technoloway, we leverage @Model.Technology.Name for its numerous advantages in building modern, scalable, and maintainable applications. This technology allows us to deliver high-quality solutions that meet our clients' specific needs.</p>

                        <div class="row mt-4">
                            <div class="col-md-6 mb-3">
                                <div class="d-flex">
                                    <div class="me-3 text-primary">
                                        <i class="fas fa-check-circle fa-2x"></i>
                                    </div>
                                    <div>
                                        <h5 class="fw-bold">Performance</h5>
                                        <p>@Model.Technology.Name offers exceptional performance, ensuring fast and responsive applications.</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="d-flex">
                                    <div class="me-3 text-primary">
                                        <i class="fas fa-check-circle fa-2x"></i>
                                    </div>
                                    <div>
                                        <h5 class="fw-bold">Scalability</h5>
                                        <p>Applications built with @Model.Technology.Name can easily scale to handle growing user bases and data volumes.</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="d-flex">
                                    <div class="me-3 text-primary">
                                        <i class="fas fa-check-circle fa-2x"></i>
                                    </div>
                                    <div>
                                        <h5 class="fw-bold">Security</h5>
                                        <p>@Model.Technology.Name includes robust security features to protect your applications and data.</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="d-flex">
                                    <div class="me-3 text-primary">
                                        <i class="fas fa-check-circle fa-2x"></i>
                                    </div>
                                    <div>
                                        <h5 class="fw-bold">Maintainability</h5>
                                        <p>The architecture of @Model.Technology.Name promotes clean, maintainable code that's easy to update.</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <h3 class="fw-bold mb-3">Our Expertise</h3>
                        <p>Our team has extensive experience with @Model.Technology.Name, having used it in numerous projects across various industries. We stay up-to-date with the latest developments and best practices to ensure we're delivering the highest quality solutions.</p>

                        <div class="text-center mt-4">
                            <a href="/Contact" class="btn btn-primary btn-lg">
                                <i class="fas fa-paper-plane me-2"></i> Discuss Your Project
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Use Cases -->
                <div class="card border-0 shadow mb-4">
                    <div class="card-body p-4">
                        <h3 class="fw-bold mb-4">Common Use Cases</h3>

                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <div class="d-flex">
                                    <div class="me-3 text-primary">
                                        <i class="fas fa-laptop fa-2x"></i>
                                    </div>
                                    <div>
                                        <h5 class="fw-bold">Web Applications</h5>
                                        <p>Building responsive, interactive web applications with modern user interfaces.</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-4">
                                <div class="d-flex">
                                    <div class="me-3 text-primary">
                                        <i class="fas fa-mobile-alt fa-2x"></i>
                                    </div>
                                    <div>
                                        <h5 class="fw-bold">Mobile Applications</h5>
                                        <p>Developing cross-platform mobile apps with native-like performance.</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-4">
                                <div class="d-flex">
                                    <div class="me-3 text-primary">
                                        <i class="fas fa-server fa-2x"></i>
                                    </div>
                                    <div>
                                        <h5 class="fw-bold">API Development</h5>
                                        <p>Creating robust, scalable APIs for integration with other systems.</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-4">
                                <div class="d-flex">
                                    <div class="me-3 text-primary">
                                        <i class="fas fa-cloud fa-2x"></i>
                                    </div>
                                    <div>
                                        <h5 class="fw-bold">Cloud Solutions</h5>
                                        <p>Building cloud-native applications with seamless scalability.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- Technology Info -->
                <div class="card border-0 shadow mb-4">
                    <div class="card-body p-4">
                        <h3 class="fw-bold mb-3">Technology Information</h3>
                        <ul class="list-unstyled">
                            <li class="mb-3">
                                <div class="d-flex">
                                    <div class="me-3 text-primary">
                                        <i class="fas fa-code"></i>
                                    </div>
                                    <div>
                                        <h6 class="fw-bold">Type</h6>
                                        <p>@GetTechnologyType(Model.Technology.Name)</p>
                                    </div>
                                </div>
                            </li>
                            <li class="mb-3">
                                <div class="d-flex">
                                    <div class="me-3 text-primary">
                                        <i class="fas fa-cogs"></i>
                                    </div>
                                    <div>
                                        <h6 class="fw-bold">Primary Use</h6>
                                        <p>@GetPrimaryUse(Model.Technology.Name)</p>
                                    </div>
                                </div>
                            </li>
                            <li>
                                <div class="d-flex">
                                    <div class="me-3 text-primary">
                                        <i class="fas fa-chart-line"></i>
                                    </div>
                                    <div>
                                        <h6 class="fw-bold">Experience Level</h6>
                                        <p>Expert</p>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Related Projects -->
                @if (Model.RelatedProjects != null && Model.RelatedProjects.Any())
                {
                    <div class="card border-0 shadow">
                        <div class="card-body p-4">
                            <h3 class="fw-bold mb-3">Projects Using @Model.Technology.Name</h3>
                            <div class="row">
                                @foreach (var project in Model.RelatedProjects)
                                {
                                    <div class="col-md-6 mb-3">
                                        <div class="card h-100 border-0 shadow-sm">
                                            <img src="@project.ImageUrl" class="card-img-top" alt="@project.Name" style="height: 120px; object-fit: cover;">
                                            <div class="card-body p-3">
                                                <h5 class="card-title h6 fw-bold mb-1">@project.Name</h5>
                                                <p class="card-text small mb-2">@project.Description.Substring(0, Math.Min(project.Description.Length, 60))...</p>
                                                <a asp-controller="Projects" asp-action="Details" asp-route-id="@project.Id" class="btn btn-sm btn-outline-primary">View Project</a>
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8 mb-4 mb-lg-0">
                <h2 class="fw-bold">Ready to Build with @Model.Technology.Name?</h2>
                <p class="lead mb-0">Contact us today to discuss how we can help you leverage @Model.Technology.Name for your business.</p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <a href="/Contact" class="btn btn-primary btn-lg">Get in Touch</a>
            </div>
        </div>
    </div>
</section>

@functions {
    private string GetTechnologyType(string techName)
    {
        return techName switch
        {
            "ASP.NET Core" => "Backend Framework",
            "React" => "Frontend Library",
            "Angular" => "Frontend Framework",
            "Node.js" => "Runtime Environment",
            "Azure" => "Cloud Platform",
            _ => "Development Technology"
        };
    }

    private string GetPrimaryUse(string techName)
    {
        return techName switch
        {
            "ASP.NET Core" => "Web Applications & APIs",
            "React" => "Interactive User Interfaces",
            "Angular" => "Single Page Applications",
            "Node.js" => "Server-Side JavaScript",
            "Azure" => "Cloud Infrastructure & Services",
            _ => "Software Development"
        };
    }
}
