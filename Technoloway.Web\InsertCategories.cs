using Microsoft.EntityFrameworkCore;
using Technoloway.Infrastructure.Data;
using Technoloway.Core.Entities;

namespace Technoloway.Web
{
    public class InsertCategories
    {
        public static async Task ExecuteAsync(ApplicationDbContext context)
        {
            // First, let's clear existing categories to avoid conflicts
            var existingCategories = await context.Categories.ToListAsync();
            if (existingCategories.Any())
            {
                Console.WriteLine($"Found {existingCategories.Count} existing categories. Clearing them...");
                context.Categories.RemoveRange(existingCategories);
                await context.SaveChangesAsync();
            }

            // Reset the identity seed
            await context.Database.ExecuteSqlRawAsync("DELETE FROM sqlite_sequence WHERE name='Categories'");

            // Insert new categories
            var categories = new List<Category>
            {
                new Category
                {
                    Id = 1,
                    CategDesc = "Development services",
                    CategName = "Development",
                    CreatedAt = DateTime.Parse("2025-06-04 16:46:09.8929523"),
                    IsDeleted = false,
                    ParentID = null,
                    UpdatedAt = DateTime.Parse("2025-06-04 16:46:09.8929523")
                },
                new Category
                {
                    Id = 2,
                    CategDesc = "Design services",
                    CategName = "Design",
                    CreatedAt = DateTime.Parse("2025-06-04 16:46:09.8929523"),
                    IsDeleted = false,
                    ParentID = null,
                    UpdatedAt = DateTime.Parse("2025-06-04 16:46:09.8929523")
                },
                new Category
                {
                    Id = 3,
                    CategDesc = "Management services",
                    CategName = "Management",
                    CreatedAt = DateTime.Parse("2025-06-04 16:46:09.8929523"),
                    IsDeleted = false,
                    ParentID = null,
                    UpdatedAt = DateTime.Parse("2025-06-04 16:46:09.8929523")
                },
                new Category
                {
                    Id = 4,
                    CategDesc = "Marketing services",
                    CategName = "Marketing",
                    CreatedAt = DateTime.Parse("2025-06-04 16:46:09.8929523"),
                    IsDeleted = false,
                    ParentID = null,
                    UpdatedAt = DateTime.Parse("2025-06-04 16:46:09.8929523")
                },
                new Category
                {
                    Id = 5,
                    CategDesc = "Maintenance services",
                    CategName = "Maintenance",
                    CreatedAt = DateTime.Parse("2025-06-04 16:46:09.8929523"),
                    IsDeleted = false,
                    ParentID = null,
                    UpdatedAt = DateTime.Parse("2025-06-04 16:46:09.8929523")
                },
                new Category
                {
                    Id = 6,
                    CategDesc = "Support services",
                    CategName = "Support",
                    CreatedAt = DateTime.Parse("2025-06-04 16:46:09.8929523"),
                    IsDeleted = false,
                    ParentID = null,
                    UpdatedAt = DateTime.Parse("2025-06-04 16:46:09.8929523")
                },
                new Category
                {
                    Id = 7,
                    CategDesc = "Web development services",
                    CategName = "Web Development",
                    CreatedAt = DateTime.Parse("2025-06-04 16:46:09.8929523"),
                    IsDeleted = false,
                    ParentID = 1,
                    UpdatedAt = DateTime.Parse("2025-06-04 16:46:09.8929523")
                },
                new Category
                {
                    Id = 8,
                    CategDesc = "Mobile app development services",
                    CategName = "Mobile App Development",
                    CreatedAt = DateTime.Parse("2025-06-04 16:46:09.8929523"),
                    IsDeleted = false,
                    ParentID = 1,
                    UpdatedAt = DateTime.Parse("2025-06-04 16:46:09.8929523")
                },
                new Category
                {
                    Id = 9,
                    CategDesc = "Desktop app development services",
                    CategName = "Desktop App Development",
                    CreatedAt = DateTime.Parse("2025-06-04 16:46:09.8929523"),
                    IsDeleted = false,
                    ParentID = 1,
                    UpdatedAt = DateTime.Parse("2025-06-04 16:46:09.8929523")
                },
                new Category
                {
                    Id = 10,
                    CategDesc = "Database app development services",
                    CategName = "Database App Development",
                    CreatedAt = DateTime.Parse("2025-06-04 16:46:09.8929523"),
                    IsDeleted = false,
                    ParentID = 1,
                    UpdatedAt = DateTime.Parse("2025-06-04 16:46:09.8929523")
                }
            };

            // Add categories to context
            await context.Categories.AddRangeAsync(categories);
            await context.SaveChangesAsync();

            Console.WriteLine($"Successfully inserted {categories.Count} categories.");
        }
    }
}
