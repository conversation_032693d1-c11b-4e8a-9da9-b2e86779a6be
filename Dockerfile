# Use the official .NET 9.0 runtime as base image
FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

# Create non-root user for security
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Use the SDK image to build the application
FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copy project files
COPY ["Technoloway.Web/Technoloway.Web.csproj", "Technoloway.Web/"]
COPY ["Technoloway.Core/Technoloway.Core.csproj", "Technoloway.Core/"]
COPY ["Technoloway.Infrastructure/Technoloway.Infrastructure.csproj", "Technoloway.Infrastructure/"]

# Restore dependencies
RUN dotnet restore "Technoloway.Web/Technoloway.Web.csproj"

# Copy source code
COPY . .

# Build the application
WORKDIR "/src/Technoloway.Web"
RUN dotnet build "Technoloway.Web.csproj" -c Release -o /app/build

# Publish the application
FROM build AS publish
RUN dotnet publish "Technoloway.Web.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Final stage/image
FROM base AS final
WORKDIR /app

# Copy published application
COPY --from=publish /app/publish .

# Create directories for uploads with proper permissions
RUN mkdir -p /app/wwwroot/images/uploads && \
    mkdir -p /app/wwwroot/documents/uploads && \
    chown -R appuser:appuser /app/wwwroot

# Switch to non-root user
USER appuser

# Set environment variables
ENV ASPNETCORE_ENVIRONMENT=Production
ENV ASPNETCORE_URLS=http://+:80

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:80/health || exit 1

ENTRYPOINT ["dotnet", "Technoloway.Web.dll"]
