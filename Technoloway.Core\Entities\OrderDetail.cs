using Technoloway.Core.Common;
using System.ComponentModel.DataAnnotations;

namespace Technoloway.Core.Entities;

public class OrderDetail : BaseEntity
{
    [Required]
    public int OrderDetailID { get; set; }
    
    [Required]
    public int OrderID { get; set; }
    
    [Required]
    public int ServID { get; set; }
    
    public int? OptID { get; set; }
    
    public int? FeatID { get; set; }
    
    public decimal? CostEach { get; set; }
    
    public int? DiscountRate { get; set; }
    
    public decimal? TotalDiscount { get; set; }
    
    public DateTime? AddedDate { get; set; }
    
    public string? Comments { get; set; }
    
    public string? Notes { get; set; }
    
    // Navigation properties
    public Order Order { get; set; } = null!;
    public Service Service { get; set; } = null!;
    public ServiceOption? ServiceOption { get; set; }
    public ServiceOptionFeature? ServiceOptionFeature { get; set; }
}
