using Technoloway.Core.Common;
using System.ComponentModel.DataAnnotations;

namespace Technoloway.Core.Entities;

public class ServiceOption : BaseEntity
{
    [Required]
    public int OptID { get; set; }
    
    [Required]
    public int ServID { get; set; }
    
    [Required]
    [StringLength(50)]
    public string OptName { get; set; } = string.Empty;
    
    public decimal? OptCost { get; set; }
    
    public int? OptDiscountRate { get; set; }
    
    public decimal? OptTotalDiscount { get; set; }
    
    [StringLength(12)]
    public string? OptAvailability { get; set; }
    
    public string? OptDesc { get; set; }
    
    // Navigation properties
    public Service Service { get; set; } = null!;
    public ICollection<ServiceOptionFeature> Features { get; set; } = new List<ServiceOptionFeature>();
    public ICollection<OrderDetail> OrderDetails { get; set; } = new List<OrderDetail>();
}
