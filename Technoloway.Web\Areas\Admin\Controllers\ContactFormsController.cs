using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Technoloway.Core.Entities;
using Technoloway.Core.Interfaces;

namespace Technoloway.Web.Areas.Admin.Controllers;

[Area("Admin")]
[Authorize(Policy = "RequireAdminRole")]
[Route("Admin/[controller]/[action]")]
public class ContactFormsController : Controller
{
    private readonly IRepository<ContactForm> _contactFormRepository;

    public ContactFormsController(IRepository<ContactForm> contactFormRepository)
    {
        _contactFormRepository = contactFormRepository;
    }

    public async Task<IActionResult> Index()
    {
        var contactForms = await _contactFormRepository.GetAll()
            .Where(c => !c.IsDeleted)
            .OrderByDescending(c => c.CreatedAt)
            .ToListAsync();

        return View(contactForms);
    }

    public async Task<IActionResult> Details(int id)
    {
        var contactForm = await _contactFormRepository.GetByIdAsync(id);
        if (contactForm == null || contactForm.IsDeleted)
        {
            return NotFound();
        }

        // Mark as read if not already read
        if (!contactForm.IsRead)
        {
            contactForm.IsRead = true;
            contactForm.ReadAt = DateTime.UtcNow;
            contactForm.UpdatedAt = DateTime.UtcNow;
            await _contactFormRepository.UpdateAsync(contactForm);
        }

        return View(contactForm);
    }

    [HttpPost]
    public async Task<IActionResult> UpdateStatus(int id, string status)
    {
        try
        {
            var contactForm = await _contactFormRepository.GetByIdAsync(id);
            if (contactForm == null || contactForm.IsDeleted)
            {
                return Json(new { success = false, message = "Contact form not found" });
            }

            contactForm.Status = status;
            contactForm.UpdatedAt = DateTime.UtcNow;
            await _contactFormRepository.UpdateAsync(contactForm);

            return Json(new { success = true, status = contactForm.Status });
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error updating contact form status: {ex.Message}");
            return Json(new { success = false, message = "Error updating status" });
        }
    }

    [HttpPost]
    public async Task<IActionResult> ToggleRead(int id)
    {
        try
        {
            var contactForm = await _contactFormRepository.GetByIdAsync(id);
            if (contactForm == null || contactForm.IsDeleted)
            {
                return Json(new { success = false, message = "Contact form not found" });
            }

            contactForm.IsRead = !contactForm.IsRead;
            contactForm.ReadAt = contactForm.IsRead ? DateTime.UtcNow : null;
            contactForm.UpdatedAt = DateTime.UtcNow;
            await _contactFormRepository.UpdateAsync(contactForm);

            return Json(new { success = true, isRead = contactForm.IsRead });
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error toggling contact form read status: {ex.Message}");
            return Json(new { success = false, message = "Error updating read status" });
        }
    }

    public async Task<IActionResult> Delete(int id)
    {
        var contactForm = await _contactFormRepository.GetByIdAsync(id);
        if (contactForm == null || contactForm.IsDeleted)
        {
            return NotFound();
        }

        return View(contactForm);
    }

    [HttpPost, ActionName("Delete")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> DeleteConfirmed(int id)
    {
        try
        {
            var contactForm = await _contactFormRepository.GetByIdAsync(id);
            if (contactForm == null || contactForm.IsDeleted)
            {
                return NotFound();
            }

            await _contactFormRepository.DeleteAsync(contactForm);
            Console.WriteLine($"Contact form soft deleted: ID={id}");

            TempData["SuccessMessage"] = "Contact form deleted successfully!";
            return RedirectToAction(nameof(Index));
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error deleting contact form: {ex.Message}");
            TempData["ErrorMessage"] = "Error deleting contact form.";
            return RedirectToAction(nameof(Index));
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetStats()
    {
        try
        {
            var totalForms = await _contactFormRepository.CountAsync(c => !c.IsDeleted);
            var unreadForms = await _contactFormRepository.CountAsync(c => !c.IsDeleted && !c.IsRead);
            var todayForms = await _contactFormRepository.CountAsync(c => !c.IsDeleted && c.CreatedAt.Date == DateTime.Today);

            return Json(new { 
                success = true, 
                total = totalForms, 
                unread = unreadForms, 
                today = todayForms 
            });
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error getting contact form stats: {ex.Message}");
            return Json(new { success = false, message = "Error getting statistics" });
        }
    }
}
