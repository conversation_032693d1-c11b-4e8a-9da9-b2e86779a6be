using System.ComponentModel.DataAnnotations;
using Technoloway.Core.Common;

namespace Technoloway.Core.Entities;

public class Testimonial : BaseEntity
{
    [Required(ErrorMessage = "Client name is required")]
    [StringLength(100, ErrorMessage = "Client name cannot exceed 100 characters")]
    public string ClientName { get; set; } = string.Empty;

    [Required(ErrorMessage = "Client title is required")]
    [StringLength(100, ErrorMessage = "Client title cannot exceed 100 characters")]
    public string ClientTitle { get; set; } = string.Empty;

    [Required(ErrorMessage = "Client company is required")]
    [StringLength(100, ErrorMessage = "Client company cannot exceed 100 characters")]
    public string ClientCompany { get; set; } = string.Empty;

    [StringLength(500, ErrorMessage = "Photo path cannot exceed 500 characters")]
    public string ClientPhotoUrl { get; set; } = string.Empty;

    [Required(ErrorMessage = "Testimonial content is required")]
    [StringLength(1000, MinimumLength = 10, ErrorMessage = "Content must be between 10 and 1000 characters")]
    public string Content { get; set; } = string.Empty;

    [Required(ErrorMessage = "Rating is required")]
    [Range(1, 5, ErrorMessage = "Rating must be between 1 and 5 stars")]
    public int Rating { get; set; } // 1-5 stars

    public bool IsActive { get; set; } = true;

    [Range(0, int.MaxValue, ErrorMessage = "Display order must be a positive number")]
    public int DisplayOrder { get; set; }
}
