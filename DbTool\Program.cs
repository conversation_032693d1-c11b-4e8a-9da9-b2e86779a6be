using System;
using System.Data.SQLite;

class Program
{
    static void Main(string[] args)
    {
        if (args.Length == 0)
        {
            Console.WriteLine("Usage: Program.exe <database_path>");
            return;
        }

        string dbPath = args[0];
        
        try
        {
            using var connection = new SQLiteConnection($"Data Source={dbPath}");
            connection.Open();
            
            Console.WriteLine($"=== Schema for {dbPath} ===");
            
            // Get all tables
            using var command = new SQLiteCommand("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name;", connection);
            using var reader = command.ExecuteReader();
            
            while (reader.Read())
            {
                string tableName = reader.GetString(0);
                Console.WriteLine($"\nTable: {tableName}");
                
                // Get table schema
                using var schemaCommand = new SQLiteCommand($"PRAGMA table_info({tableName});", connection);
                using var schemaReader = schemaCommand.ExecuteReader();
                
                Console.WriteLine("Columns:");
                while (schemaReader.Read())
                {
                    string columnName = schemaReader.GetString(1);
                    string dataType = schemaReader.GetString(2);
                    bool notNull = schemaReader.GetInt32(3) == 1;
                    string defaultValue = schemaReader.IsDBNull(4) ? "NULL" : schemaReader.GetString(4);
                    bool primaryKey = schemaReader.GetInt32(5) == 1;
                    
                    Console.WriteLine($"  - {columnName} ({dataType}) {(notNull ? "NOT NULL" : "NULL")} {(primaryKey ? "PRIMARY KEY" : "")} DEFAULT: {defaultValue}");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
        }
    }
}
