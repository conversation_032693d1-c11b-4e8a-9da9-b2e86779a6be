@model Technoloway.Core.Entities.Invoice
@{
    ViewData["Title"] = "Invoice Details";
    var items = ViewBag.Items as List<Technoloway.Core.Entities.InvoiceItem>;
    var payments = ViewBag.Payments as List<Technoloway.Core.Entities.Payment>;
    var totalPaid = ViewBag.TotalPaid as decimal? ?? 0;
    var balance = ViewBag.Balance as decimal? ?? 0;
}

<div class="container-fluid p-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="d-flex align-items-center mb-2">
                        <a asp-action="Index" class="btn btn-outline-secondary btn-sm me-3">
                            <i class="fas fa-arrow-left me-1"></i> Back to Invoices
                        </a>
                        <h1 class="mb-0">Invoice @Model.InvoiceNumber</h1>
                    </div>
                    <p class="text-muted">Invoice details and payment information</p>
                </div>
                <div class="d-flex gap-2">
                    <a asp-action="Download" asp-route-id="@Model.Id" class="btn btn-outline-primary">
                        <i class="fas fa-download me-1"></i> Download PDF
                    </a>
                    @if (Model.Status != "Paid" && balance > 0)
                    {
                        <button type="button" class="btn btn-success"
                                onclick="showPaymentModal(@Model.Id, '@Model.InvoiceNumber', @balance)">
                            <i class="fas fa-credit-card me-1"></i> Make Payment
                        </button>
                    }
                    <button class="btn btn-outline-secondary" onclick="window.print()">
                        <i class="fas fa-print me-1"></i> Print
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Invoice Status and Summary -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Invoice Information</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Invoice Number:</strong></td>
                                    <td>@Model.InvoiceNumber</td>
                                </tr>
                                <tr>
                                    <td><strong>Issue Date:</strong></td>
                                    <td>@Model.IssueDate.ToString("MMMM dd, yyyy")</td>
                                </tr>
                                <tr>
                                    <td><strong>Due Date:</strong></td>
                                    <td>
                                        @Model.DueDate.ToString("MMMM dd, yyyy")
                                        @if (Model.DueDate < DateTime.Now && Model.Status != "Paid")
                                        {
                                            <span class="badge bg-danger ms-2">Overdue</span>
                                        }
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Project:</strong></td>
                                    <td>
                                        @if (Model.Order != null)
                                        {
                                            <a asp-controller="Projects" asp-action="Details" asp-route-id="@Model.OrderID"
                                               class="text-decoration-none">
                                                @Model.Order.OrderTitle
                                            </a>
                                        }
                                        else
                                        {
                                            <span class="text-muted">No Project</span>
                                        }
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>Status</h5>
                            <div class="mb-3">
                                @switch (Model.Status)
                                {
                                    case "Paid":
                                        <span class="badge bg-success fs-6">
                                            <i class="fas fa-check-circle me-1"></i>Paid
                                        </span>
                                        break;
                                    case "Pending":
                                        <span class="badge bg-warning fs-6">
                                            <i class="fas fa-clock me-1"></i>Pending
                                        </span>
                                        break;
                                    case "Overdue":
                                        <span class="badge bg-danger fs-6">
                                            <i class="fas fa-exclamation-triangle me-1"></i>Overdue
                                        </span>
                                        break;
                                    default:
                                        <span class="badge bg-secondary fs-6">@Model.Status</span>
                                        break;
                                }
                            </div>

                            @if (!string.IsNullOrEmpty(Model.Notes))
                            {
                                <h6>Notes</h6>
                                <p class="text-muted">@Model.Notes</p>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <h5>Payment Summary</h5>
                    <div class="mb-3">
                        <h3 class="text-primary">$@Model.TotalAmount.ToString("N2")</h3>
                        <p class="text-muted mb-0">Total Amount</p>
                    </div>
                    <div class="mb-3">
                        <h4 class="text-success">$@totalPaid.ToString("N2")</h4>
                        <p class="text-muted mb-0">Amount Paid</p>
                    </div>
                    <div class="mb-3">
                        <h4 class="@(balance > 0 ? "text-warning" : "text-success")">$@balance.ToString("N2")</h4>
                        <p class="text-muted mb-0">Balance Due</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Invoice Items -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Invoice Items</h5>
                </div>
                <div class="card-body p-0">
                    @if (items != null && items.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Description</th>
                                        <th class="text-center">Quantity</th>
                                        <th class="text-end">Unit Price</th>
                                        <th class="text-end">Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var item in items)
                                    {
                                        <tr>
                                            <td>
                                                <strong>@item.Description</strong>
                                            </td>
                                            <td class="text-center">@item.Quantity</td>
                                            <td class="text-end">$@item.UnitPrice.ToString("N2")</td>
                                            <td class="text-end"><strong>$@item.TotalPrice.ToString("N2")</strong></td>
                                        </tr>
                                    }
                                </tbody>
                                <tfoot class="table-light">
                                    <tr>
                                        <th colspan="3" class="text-end">Subtotal:</th>
                                        <th class="text-end">$@items.Sum(i => i.TotalPrice).ToString("N2")</th>
                                    </tr>
                                    @if (Model.TaxAmount > 0)
                                    {
                                        <tr>
                                            <th colspan="3" class="text-end">Tax:</th>
                                            <th class="text-end">$@Model.TaxAmount.ToString("N2")</th>
                                        </tr>
                                    }

                                    <tr class="table-primary">
                                        <th colspan="3" class="text-end">Total Amount:</th>
                                        <th class="text-end">$@Model.TotalAmount.ToString("N2")</th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-list fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">No items found for this invoice</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- Payment History -->
    @if (payments != null && payments.Any())
    {
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white">
                        <h5 class="mb-0">Payment History</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Payment Date</th>
                                        <th>Amount</th>
                                        <th>Method</th>
                                        <th>Status</th>
                                        <th>Notes</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var payment in payments)
                                    {
                                        <tr>
                                            <td>@payment.PaymentDate.ToString("MMM dd, yyyy HH:mm")</td>
                                            <td><strong class="text-success">$@payment.Amount.ToString("N2")</strong></td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    @switch (payment.PaymentMethod)
                                                    {
                                                        case "Credit Card":
                                                            <i class="fas fa-credit-card text-primary me-2"></i>
                                                            break;
                                                        case "Bank Transfer":
                                                            <i class="fas fa-university text-info me-2"></i>
                                                            break;
                                                        case "PayPal":
                                                            <i class="fab fa-paypal text-primary me-2"></i>
                                                            break;
                                                        case "Check":
                                                            <i class="fas fa-money-check text-secondary me-2"></i>
                                                            break;
                                                        default:
                                                            <i class="fas fa-dollar-sign text-muted me-2"></i>
                                                            break;
                                                    }
                                                    @payment.PaymentMethod
                                                </div>
                                            </td>
                                            <td>
                                                @switch (payment.Status)
                                                {
                                                    case "Completed":
                                                        <span class="badge bg-success">Completed</span>
                                                        break;
                                                    case "Pending":
                                                        <span class="badge bg-warning">Pending</span>
                                                        break;
                                                    case "Failed":
                                                        <span class="badge bg-danger">Failed</span>
                                                        break;
                                                    default:
                                                        <span class="badge bg-secondary">@payment.Status</span>
                                                        break;
                                                }
                                            </td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(payment.Notes))
                                                {
                                                    <span class="text-muted">@payment.Notes</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

<!-- Payment Modal -->
<div class="modal fade" id="paymentModal" tabindex="-1" aria-labelledby="paymentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="paymentModalLabel">Make Payment</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form asp-action="PayInvoice" method="post" id="paymentForm">
                @Html.AntiForgeryToken()
                <div class="modal-body">
                    <input type="hidden" id="invoiceId" name="id" />

                    <div class="mb-3">
                        <label class="form-label">Invoice Number</label>
                        <input type="text" class="form-control" id="invoiceNumber" readonly />
                    </div>

                    <div class="mb-3">
                        <label for="amount" class="form-label">Payment Amount</label>
                        <div class="input-group">
                            <span class="input-group-text">$</span>
                            <input type="number" class="form-control" id="amount" name="amount" step="0.01" min="0.01" required />
                        </div>
                        <div class="form-text">Maximum amount: $<span id="maxAmount"></span></div>
                    </div>

                    <div class="mb-3">
                        <label for="paymentMethod" class="form-label">Payment Method</label>
                        <select class="form-select" id="paymentMethod" name="paymentMethod" required>
                            <option value="">Select Payment Method</option>
                            <option value="Credit Card">Credit Card</option>
                            <option value="Bank Transfer">Bank Transfer</option>
                            <option value="PayPal">PayPal</option>
                            <option value="Check">Check</option>
                        </select>
                    </div>

                    <div id="paymentError" class="alert alert-danger d-none"></div>
                    <div id="paymentSuccess" class="alert alert-success d-none"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success" id="submitPaymentBtn">
                        <i class="fas fa-credit-card me-1"></i> Make Payment
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

@@media print {
    .btn, .modal {
        display: none !important;
    }

    .container-fluid {
        padding: 0 !important;
    }

    .card {
        border: none !important;
        box-shadow: none !important;
    }
}
</style>

<script>
function showPaymentModal(invoiceId, invoiceNumber, amount) {
    document.getElementById('invoiceId').value = invoiceId;
    document.getElementById('invoiceNumber').value = invoiceNumber;
    document.getElementById('amount').value = amount.toFixed(2);
    document.getElementById('maxAmount').textContent = amount.toFixed(2);

    var modal = new bootstrap.Modal(document.getElementById('paymentModal'));
    modal.show();
}

// Handle payment form submission
document.getElementById('paymentForm').addEventListener('submit', function(e) {
    e.preventDefault();
    submitPayment();
});

// Validate payment amount
document.getElementById('amount').addEventListener('input', function() {
    var amount = parseFloat(this.value);
    var maxAmount = parseFloat(document.getElementById('maxAmount').textContent);

    if (amount > maxAmount) {
        this.setCustomValidity('Amount cannot exceed the balance due');
    } else if (amount <= 0) {
        this.setCustomValidity('Amount must be greater than 0');
    } else {
        this.setCustomValidity('');
    }
});

function submitPayment() {
    const form = document.getElementById('paymentForm');
    const submitBtn = document.getElementById('submitPaymentBtn');
    const errorDiv = document.getElementById('paymentError');
    const successDiv = document.getElementById('paymentSuccess');

    // Hide previous messages
    errorDiv.classList.add('d-none');
    successDiv.classList.add('d-none');

    // Disable submit button
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Processing...';

    // Get form data
    const formData = new FormData(form);

    // Add AJAX header
    fetch(form.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            successDiv.textContent = data.message;
            successDiv.classList.remove('d-none');

            // Close modal after 2 seconds and refresh page
            setTimeout(() => {
                const modal = bootstrap.Modal.getInstance(document.getElementById('paymentModal'));
                modal.hide();
                location.reload();
            }, 2000);
        } else {
            errorDiv.textContent = data.message;
            errorDiv.classList.remove('d-none');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        errorDiv.textContent = 'An error occurred while processing the payment. Please try again.';
        errorDiv.classList.remove('d-none');
    })
    .finally(() => {
        // Re-enable submit button
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="fas fa-credit-card me-1"></i> Make Payment';
    });
}
</script>
